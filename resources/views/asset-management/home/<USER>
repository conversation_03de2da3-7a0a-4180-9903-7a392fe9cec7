@extends("layouts.app")

@push('style')
<link href="{{ asset('/') }}css/app/custom_dashboard.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="row">
    <!-- Card 1: Total Aset dengan Detail -->
    <div class="col-12">
        <div class="stats-card main-asset-card compact">
            <div class="row">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center mb-3">
                        <div class="stats-icon main-asset me-3 compact">
                            <i class="fa fa-cubes"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stats-number compact">{{ number_format($totalEquipments) }}</div>
                            <div class="stats-description">Total aset peralatan yang aktif</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <div class="sub-stat-item compact">
                                <div class="sub-stat-icon pending compact">
                                    <i class="fa fa-book"></i>
                                </div>
                                <div class="sub-stat-content">
                                    <div class="sub-stat-number compact">{{ number_format($totalPendingDocuments) }}</div>
                                    <div class="sub-stat-label compact">Buku Bantu Pending</div>
                                    <div class="sub-stat-progress">
                                        <div class="progress h-2px rounded-3 bg-light">
                                            <div class="progress-bar bg-warning" style="width: 65%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="sub-stat-item compact">
                                <div class="sub-stat-icon location compact">
                                    <i class="fa fa-exchange-alt"></i>
                                </div>
                                <div class="sub-stat-content">
                                    <div class="sub-stat-number compact">{{ number_format($totalTodayLocationChanges) }}</div>
                                    <div class="sub-stat-label compact">Perubahan Posisi Hari Ini</div>
                                    <div class="sub-stat-progress">
                                        <div class="progress h-2px rounded-3 bg-light">
                                            <div class="progress-bar bg-info" style="width: 45%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 d-flex align-items-center justify-content-center">
                    <div class="asset-illustration compact">
                        <i class="fa fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Card 3: Perubahan Posisi Terakhir -->
<div class="row">
    <div class="col-12">
        <div class="data-panel">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fa fa-history me-2"></i>
                    Perubahan Posisi Terakhir
                </h5>
                <div class="card-actions">
                    <a href="{{ route('asset-management.position-change.index') }}" class="btn btn-sm btn-outline-primary">
                        Lihat Semua <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                @if(count($latestLocationChanges ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th>Waktu Scan</th>
                                <th>Kode Aset</th>
                                <th>Nama Aset</th>
                                <th>Ruangan</th>
                                <th>Ruangan Sebelumnya</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($latestLocationChanges as $change)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-clock text-muted me-2"></i>
                                        <span>{{ $change->scan_time }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $change->asset->asset_code }}</span>
                                </td>
                                <td>
                                    <strong>{{ $change->asset->asset_name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ $change->room->room_name }}</span>
                                </td>
                                <td>
                                    @if($change->prevRoom)
                                        <span class="badge bg-secondary">{{ $change->prevRoom->room_name }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">Berhasil</span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="empty-state">
                    <i class="fa fa-map-marker-alt"></i>
                    <p class="mb-0">Belum ada data perubahan posisi</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection