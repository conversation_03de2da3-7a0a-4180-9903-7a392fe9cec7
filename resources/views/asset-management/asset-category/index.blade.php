@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
    rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush


@push('menu')
@include('menu.asset_management')
@endpush

@section('content')
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in kategori aset table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <div class="datatable-filter-icon btn-modern-rounded">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>Kode Kategori</th>
                    <th>Nama Kategori</th>
                    <th>Tipe Kategori</th>
                    <th>Sub Kategori</th>
                    <th>Jumlah Aset</th>
                    <th style="width: 80px;">Action</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Filter Data</label>
                    <select name="filter" id="filter" class="form-select">
                        <option value="asset">Aset Rumah Sakit</option>
                        <option value="non-asset">Non Aset Rumah Sakit</option>
                        <option value="all">Semua Kategori</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearFilter">Clear Filter</button>
                <button type="button" class="btn btn-primary" id="applyFilter">Apply Filter</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Kategori</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between border-bottom mb-3 pb-2">
                    <div class="d-block fs-14px mb-2">
                        Kode Kategori : <strong id="kode_kategori">123</strong><br>
                        Nama Kategori : <strong id="nama_kategori">123</strong>
                    </div>

                    <div class="d-block fs-14px text-end">
                        Tipe Kategori : <strong id="tipe_kategori">123</strong><br>
                        Jumlah Aset : <strong id="jumlah_aset">10</strong>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <select class="form-select" id="filter_condition">
                            <option value="">Semua Kondisi</option>
                            <option value="BAIK">Baik</option>
                            <option value="RUSAK_RINGAN">Rusak Ringan</option>
                            <option value="RUSAK_BERAT">Rusak Berat</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped w-100" id="datatable-asset">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Kode QR</th>
                                <th>Kode Barang</th>
                                <th>Nama Aset</th>
                                <th>Kode Register / SN</th>
                                <th>Lokasi Aset</th>
                                <th>Kondisi</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                <a href="javascript:;" class="btn btn-warning btn-export-excel" data-id=""><i class="fas fa-file-excel me-1"></i> Export Excel</a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/master/app.js') }}"></script>
<script src="{{ asset('/js/app/asset-management/assetCategory.js') }}"></script>
@endpush