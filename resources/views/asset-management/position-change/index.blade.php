@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in perubahan posisi table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <div class="datatable-filter-icon btn-modern-rounded">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>Kode Aset</th>
                    <th>Nama Aset</th>
                    <th>Ruangan KIR</th>
                    <th>Asal Ruangan</th>
                    <th>Ruangan Terbaru</th>
                    <th>Waktu Perpindahan</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Tipe Filter</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="filterType" id="filterRuangan" value="ruangan" checked>
                        <label class="btn btn-outline-primary" for="filterRuangan">Filter by Ruangan</label>
                        
                        <input type="radio" class="btn-check" name="filterType" id="filterAsset" value="asset">
                        <label class="btn btn-outline-primary" for="filterAsset">Filter by QR / Asset</label>
                    </div>
                </div>

                <div class="form-group mb-3" id="form-ruangan">
                    <label for="ruangan" class="form-label">Ruangan</label>
                    <select name="ruangan" id="ruangan" class="form-select ruangan"></select>
                </div>

                <div class="form-group mb-3 d-none" id="form-asset">
                    <label for="asset" class="form-label">QR / Asset</label>
                    <select name="asset" id="asset" class="form-select asset"></select>
                </div>

                <div class="form-group mb-3">
                    <label class="form-label">Periode</label>
                    <input id="datepicker" type="text" class="form-control" readonly="readonly" style="background-color:white; cursor:pointer;">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearFilter">Clear Filter</button>
                <button type="button" class="btn btn-primary" id="applyFilter">Apply Filter</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/master/app.js') }}"></script>
<script src="{{ asset('/js/app/asset-management/positionChange.js') }}"></script>
@endpush
