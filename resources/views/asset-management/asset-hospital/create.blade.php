@extends("layouts.app")
@push("style")
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker3.min.css" rel="stylesheet"/>
    <link href="{{ asset('/css/app/asset-management/createAssetHospital.css') }}" rel="stylesheet"/>
    <style>
        .modal-success .modal-header {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .modal-danger .modal-header {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .modal-warning .modal-header {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
@endpush

@push("menu")
    @include("menu.asset_management")
@endpush

@section("content")
    <div class="modern-card" style="position: relative;">
        <div class="modern-card-body">
            <!-- Inline alert for form feedback -->
            <div id="formAlert" class="alert d-none" role="alert" aria-live="polite"></div>

{{-- Main form for creating asset hospital entries --}}
            <form action="" method="post" id="formdata" data-hospital-name="{{ $hospitalName ?? 'RSUD Dr. Soedarso' }}" data-rkbmd-prefix="{{ config('app.rkbmd_prefix') }}">
                @csrf

{{-- Section for primary transaction and acquisition details --}}
                <!-- Main Information Section -->
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="section-title">
                        <h3>Informasi Utama</h3>
                        <p class="section-subtitle">Lengkapi detail transaksi dan sumber perolehan aset</p>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="entry_date" class="form-label">Tanggal Barang Masuk <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="entry_date" name="entry_date" value="{{ old('entry_date', now()->format('Y-m-d')) }}" required placeholder="YYYY-MM-DD" autocomplete="off">
                        <small class="helper-text">Otomatis diisi hari ini, tidak dapat diubah</small>
                        <div class="invalid-feedback" id="entry_date_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="payment_date" class="form-label">Tanggal Pembayaran <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="payment_date" name="payment_date" value="{{ old('payment_date', now()->format('Y-m-d')) }}" required placeholder="YYYY-MM-DD" autocomplete="off">
                        <small class="helper-text">Tanggal sesuai dokumen pembayaran</small>
                        <div class="invalid-feedback" id="payment_date_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="contract_number" class="form-label">No. Kontrak/SP <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="contract_number" name="contract_number" placeholder="Contoh: 005/SPK/IX/2025" required>
                        <small class="helper-text">Nomor kontrak atau surat perintah</small>
                        <div class="invalid-feedback" id="contract_number_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="job_description" class="form-label">Uraian Pekerjaan <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="job_description" name="job_description" placeholder="Masukkan uraian pekerjaan" required>
                        <small class="helper-text">Deskripsi uraian pekerjaan</small>
                        <div class="invalid-feedback" id="job_description_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="payment_ba_number" class="form-label">No. BA Pembayaran <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="payment_ba_number" name="payment_ba_number" placeholder="No. BA / dokumen bayar" required>
                        <small class="helper-text">Nomor berita acara pembayaran</small>
                        <div class="invalid-feedback" id="payment_ba_number_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="acquisition_source" class="form-label">Asal Perolehan <span class="required-indicator">*</span></label>
                        <select class="form-select w-100" id="acquisition_source" name="acquisition_source" required>
                            <option value="">Pilih Asal Perolehan</option>
                            <option value="apbn">APBN</option>
                            <option value="apbd">APBD</option>
                            <option value="hibah">Hibah/Pemberian</option>
                            <option value="kso/pinjam">KSO/Pinjam</option>
                            <option value="blud">BLU/BLUD</option>
                            <option value="jkn">JKN</option>
                            <option value="dak">DAK</option>
                            <option value="sisoin">SISOIN</option>
                            <option value="swasta">Swasta/Swadana</option>
                        </select>
                        <small class="helper-text">Sumber perolehan aset</small>
                        <div class="invalid-feedback" id="acquisition_source_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="distributor_id" class="form-label">Distributor <span class="required-indicator">*</span></label>
                        <select class="form-select w-100" id="distributor_id" name="distributor_id" required data-placeholder="Pilih Distributor">
                            <option value="">Pilih Distributor</option>
                        </select>
                        <small class="helper-text">Pilih distributor atau penyedia barang</small>
                        <div class="invalid-feedback" id="distributor_id_error"></div>
                    </div>
                    <div class="form-group">
                        <label for="contract_type" class="form-label">Bentuk Kontrak <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="contract_type" name="contract_type" placeholder="Masukan bentuk kontrak" required>
                        <small class="helper-text">Jenis kontrak pengadaan</small>
                        <div class="invalid-feedback" id="contract_type_error"></div>
                    </div>


                    <div class="form-group">
                        <label for="total_items" class="form-label">Jumlah Barang <span class="required-indicator">*</span></label>
                        <input type="number" class="form-control calculated-field" id="total_items" min="1" required readonly disabled placeholder="Otomatis terhitung">
                        <small class="helper-text">Total item yang akan dimasukkan</small>
                        <div class="invalid-feedback" id="total_items_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="total_price" class="form-label">Total Harga <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control currency-input calculated-field" id="total_price" required readonly disabled placeholder="Otomatis terhitung">
                        <small class="helper-text">Total nilai semua barang</small>
                        <div class="invalid-feedback" id="total_price_error"></div>
                    </div>

                    <div class="form-group textarea-field">
                        <label for="description" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="description" name="description" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                        <small class="helper-text">Informasi tambahan mengenai transaksi</small>
                        <div class="invalid-feedback" id="description_error"></div>
                    </div>
                </div>

                <!-- Sub Sections inside Informasi Utama -->
                <div class="subsections" style="margin-left: 1.5rem; border-left: 3px solid var(--primary-color); padding-left: 1rem; background: #f8fafc; border-radius: 0 8px 8px 0; margin-top: 1rem;">
{{-- Collapsible subsection for ASPAK data fields --}}
                    <!-- ASPAK Sub Section -->
                    <div class="subsection" id="aspak-section" style="margin-bottom: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <button type="button" class="subsection-toggle" aria-expanded="false" aria-controls="aspak-content">
                            <div class="section-header subsection-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="section-title">
                                    <h3>ASPAK</h3>
                                    <p class="section-subtitle">Data ASPAK</p>
                                </div>
                                <i class="toggle-icon fas fa-chevron-down"></i>
                            </div>
                        </button>
                        <div class="subsection-content" id="aspak-content" style="display: none;">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="aspak_distributor" class="form-label">Distributor</label>
                                    <input type="text" class="form-control" id="aspak_distributor" name="aspak_distributor" placeholder="Otomatis dari Distributor">
                                    <small class="helper-text">Duplikat dari field Distributor di Informasi Utama</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_maintenance_officer" class="form-label">PJ Pemeliharaan</label>
                                    <input type="text" class="form-control" id="aspak_maintenance_officer" name="aspak_maintenance_officer" placeholder="Masukkan nama PJ pemeliharaan">
                                    <small class="helper-text">Penanggung jawab pemeliharaan aset</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_procurement_year" class="form-label">Tahun Pengadaan</label>
                                    <input type="text" class="form-control" id="aspak_procurement_year" name="aspak_procurement_year" readonly placeholder="Otomatis dari Tanggal Barang Masuk">
                                    <small class="helper-text">Duplikat tahun dari Tanggal Barang Masuk</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_procurement_source" class="form-label">Sumber Pengadaan</label>
                                    <input type="text" class="form-control" id="aspak_procurement_source" name="aspak_procurement_source" readonly placeholder="Otomatis dari Asal Perolehan">
                                    <small class="helper-text">Duplikat dari field Asal Perolehan di Informasi Utama</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_acquisition_price" class="form-label">Harga Perolehan</label>
                                    <input type="text" class="form-control" id="aspak_acquisition_price" name="aspak_acquisition_price" readonly placeholder="Otomatis dari Total Harga">
                                    <small class="helper-text">Duplikat dari field Total Harga di Informasi Utama</small>
                                </div>
                            </div>
                        </div>
                    </div>

{{-- Collapsible subsection for SIAP BMD data fields --}}
                    <!-- SIAP BMD Sub Section -->
                    <div class="subsection" id="siapbmd-section" style="margin-bottom: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <button type="button" class="subsection-toggle" aria-expanded="false" aria-controls="siapbmd-content">
                            <div class="section-header subsection-header">
                                <div class="section-icon" style="background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div class="section-title">
                                    <h3>SIAP BMD</h3>
                                    <p class="section-subtitle">Data SIAP BMD</p>
                                </div>
                                <i class="toggle-icon fas fa-chevron-down"></i>
                            </div>
                        </button>
                        <div class="subsection-content" id="siapbmd-content" style="display: none;">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="siap_bmd_year" class="form-label">Tahun</label>
                                    <input type="text" class="form-control" id="siap_bmd_year" name="siap_bmd_year" readonly>
                                    <small class="form-text helper-text">Otomatis diambil dari Tanggal Barang Masuk</small>
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_rkbmd_code" class="form-label">Kode RKBMD</label>
                                    <input type="text" class="form-control" id="siap_bmd_rkbmd_code" name="siap_bmd_rkbmd_code" placeholder="Masukkan kode RKBMD">
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_activity" class="form-label">Kegiatan</label>
                                    <select class="form-select select2-ajax" id="siap_bmd_activity" name="siap_bmd_activity" data-placeholder="Pilih Kegiatan">
                                        <option value="">Pilih Kegiatan</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_sub_activity" class="form-label">SUB Kegiatan</label>
                                    <select class="form-select select2-ajax" id="siap_bmd_sub_activity" name="siap_bmd_sub_activity" data-placeholder="Pilih Sub Kegiatan" disabled>
                                        <option value="">Pilih Sub Kegiatan</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_kusa_name" class="form-label">Nama Kusa</label>
                                    <input type="text" class="form-control" id="siap_bmd_kusa_name" name="siap_bmd_kusa_name" placeholder="Masukkan nama kusa">
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_hospital_name" class="form-label">Nama RS</label>
                                    <input type="text" class="form-control" id="siap_bmd_hospital_name" name="siap_bmd_hospital_name" placeholder="Masukkan nama rumah sakit">
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_price" class="form-label">Harga</label>
                                    <input type="text" class="form-control" id="siap_bmd_price" name="siap_bmd_price" readonly>
                                    <small class="form-text helper-text">Otomatis diambil dari Total Harga</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

{{-- Visual divider between main information and item details --}}
                <!-- Section Divider -->
                <div class="section-divider" style="border-top: 2px solid var(--border-color); margin: 1.5rem 0; position: relative;">
                    <div style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%); background: white; padding: 0 1rem; color: var(--helper-text); font-size: 0.7rem; font-weight: 600;">DETAIL BARANG</div>
                </div>

{{-- Section for managing and adding item details with automatic calculations --}}
                <!-- Items Section -->
                <div class="items-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="section-title">
                            <h3>Detail Barang</h3>
                            <p class="section-subtitle">Kelola item barang dengan perhitungan harga otomatis</p>
                        </div>
                        <button type="button" class="btn btn-success btn-modern" id="addItemBtn">
                            <i class="fas fa-plus"></i>
                            <span>Tambah Barang</span>
                        </button>
                    </div>

                    <div id="itemsContainer">
                        <!-- Dynamic items will be added here -->
                    </div>
                </div>

{{-- Action buttons for canceling or submitting the form --}}
                <!-- Form Actions -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary btn-modern" onclick="window.history.back()">
                        <i class="fas fa-times"></i>
                        <span>Batal</span>
                    </button>
                    <button type="submit" class="btn btn-primary btn-modern" id="submitBtn">
                        <i class="fas fa-save"></i>
                        <span>Simpan</span>
                    </button>
                </div>
            </form>
            <!-- Lightweight overlay for loading state -->
            <div id="formLoadingOverlay" class="d-none" style="position: absolute; inset: 0; background: rgba(255,255,255,0.6); display: flex; align-items: center; justify-content: center; z-index: 10;">
                <div class="spinner-border text-primary" role="status" aria-live="polite" aria-label="Menyimpan..."></div>
            </div>
        </div>
    </div>

{{-- Modal for confirming form submission before processing --}}
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmSubmissionModal" tabindex="-1" aria-labelledby="confirmSubmissionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmSubmissionModalLabel">Confirm Submission</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to submit this form? Please review all information before confirming.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmSubmitBtn">Submit</button>
                </div>
            </div>
        </div>
    </div>

{{-- Modal for displaying form submission feedback and errors --}}
    <!-- Custom Bootstrap Modal for Form Feedback -->
    <div class="modal fade" id="formFeedbackModal" tabindex="-1" aria-labelledby="formFeedbackModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="formFeedbackModalLabel">Notification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="modalMessageContent"></div>
                    <div id="errorMessagesSection" class="d-none mt-3">
                        <ul id="errorMessagesList" class="mb-0 ps-3"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
@endsection

{{-- Scripts for initializing form components and handling interactions --}}
@push("script")
    <script>
        window.hospitalName = "{{ $hospitalName ?? config('app.hospital_name') }}";
        window.rkbmdPrefix = "{{ config('app.rkbmd_prefix') }}";
    </script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
    <script src="{{ asset('/js/app/asset-management/createAssetHospital.js') }}"></script>
@endpush
