@extends("layouts.app")
@push("style")
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker3.min.css" rel="stylesheet"/>
    <link href="{{ asset('/css/app/asset-management/createAssetHospital.css') }}" rel="stylesheet"/>
    <style>
        .modal-success .modal-header {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .modal-danger .modal-header {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .modal-warning .modal-header {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
@endpush

@push("menu")
    @include("menu.asset_management")
@endpush

@section("content")
    <div class="modern-card" style="position: relative;">
        <div class="modern-card-body">
            <!-- Inline alert for form feedback -->
            <div id="formAlert" class="alert d-none" role="alert" aria-live="polite"></div>

            {{-- Main form for updating asset hospital entry --}}
            <form action="{{ route('asset-management.asset-hospital.update', $asset->id) }}" method="post" id="formdata"
                  data-hospital-name="{{ $hospitalName ?? 'RSUD Dr. Soedarso' }}"
                  data-rkbmd-prefix="{{ config('app.rkbmd_prefix') }}">
                @csrf
                @method('PUT')

                {{-- Section for primary transaction and acquisition details --}}
                <!-- Main Information Section -->
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="section-title">
                        <h3>Informasi Utama</h3>
                        <p class="section-subtitle">Lengkapi detail transaksi dan sumber perolehan aset</p>
                    </div>
                </div>

                {{-- Grid layout for main information fields --}}
                <div class="form-grid">
                    <div class="form-group">
                        <label for="entry_date" class="form-label">Tanggal Barang Masuk <span
                                class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="entry_date" name="entry_date"
                               value="{{ old('entry_date', $assetEntry->received_date->format('Y-m-d')) }}" required
                               placeholder="YYYY-MM-DD" autocomplete="off">
                        <small class="helper-text">Otomatis diisi hari ini, tidak dapat diubah</small>
                        <div class="invalid-feedback" id="entry_date_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="payment_date" class="form-label">Tanggal Pembayaran <span
                                class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="payment_date" name="payment_date"
                               value="{{ old('payment_date', $assetEntry->payment_date->format('Y-m-d')) }}" required
                               placeholder="YYYY-MM-DD" autocomplete="off">
                        <small class="helper-text">Tanggal sesuai dokumen pembayaran</small>
                        <div class="invalid-feedback" id="payment_date_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="contract_number" class="form-label">No. Kontrak/SP <span class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="contract_number" name="contract_number"
                               placeholder="Contoh: 005/SPK/IX/2025"
                               value="{{ old('contract_number', $assetEntry->bast_contract_number) }}" required>
                        <small class="helper-text">Nomor kontrak atau surat perintah</small>
                        <div class="invalid-feedback" id="contract_number_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="job_description" class="form-label">Uraian Pekerjaan <span
                                class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="job_description" name="job_description"
                               placeholder="Masukkan uraian pekerjaan"
                               value="{{ old('job_description', $assetEntry->purchasing_account) }}" required>
                        <small class="helper-text">Deskripsi uraian pekerjaan</small>
                        <div class="invalid-feedback" id="job_description_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="payment_ba_number" class="form-label">No. BA Pembayaran <span
                                class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="payment_ba_number" name="payment_ba_number"
                               placeholder="No. BA / dokumen bayar"
                               value="{{ old('payment_ba_number', $assetEntry->bast_payment_number) }}" required>
                        <small class="helper-text">Nomor berita acara pembayaran</small>
                        <div class="invalid-feedback" id="payment_ba_number_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="acquisition_source" class="form-label">Asal Perolehan <span
                                class="required-indicator">*</span></label>
                        <select class="form-select w-100" id="acquisition_source" name="acquisition_source" required>
                            <option value="">Pilih Asal Perolehan</option>
                            <option
                                value="apbn" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'apbn' ? 'selected' : '' }}>
                                APBN
                            </option>
                            <option
                                value="apbd" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'apbd' ? 'selected' : '' }}>
                                APBD
                            </option>
                            <option
                                value="hibah" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'hibah' ? 'selected' : '' }}>
                                Hibah/Pemberian
                            </option>
                            <option
                                value="kso/pinjam" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'kso/pinjam' ? 'selected' : '' }}>
                                KSO/Pinjam
                            </option>
                            <option
                                value="blud" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'blud' ? 'selected' : '' }}>
                                BLU/BLUD
                            </option>
                            <option
                                value="jkn" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'jkn' ? 'selected' : '' }}>
                                JKN
                            </option>
                            <option
                                value="dak" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'dak' ? 'selected' : '' }}>
                                DAK
                            </option>
                            <option
                                value="sisoin" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'sisoin' ? 'selected' : '' }}>
                                SISOIN
                            </option>
                            <option
                                value="swasta" {{ old('acquisition_source', strtolower($assetEntry->source_supply)) == 'swasta' ? 'selected' : '' }}>
                                Swasta/Swadana
                            </option>
                        </select>
                        <small class="helper-text">Sumber perolehan aset</small>
                        <div class="invalid-feedback" id="acquisition_source_error"></div>
                    </div>

                    <div class="form-group">
                        <label for="distributor_id" class="form-label">Distributor <span
                                class="required-indicator">*</span></label>
                        <select class="form-select w-100" id="distributor_id" name="distributor_id" required
                                data-placeholder="Pilih Distributor">
                            <option value="">Pilih Distributor</option>
                            @if($assetEntry->distributor)
                                <option value="{{ $assetEntry->distributor->id }}"
                                        selected>{{ $assetEntry->distributor->distributor_name }}</option>
                            @endif
                        </select>
                        <small class="helper-text">Pilih distributor atau penyedia barang</small>
                        <div class="invalid-feedback" id="distributor_id_error"></div>
                    </div>
                    <div class="form-group">
                        <label for="contract_type" class="form-label">Bentuk Kontrak <span
                                class="required-indicator">*</span></label>
                        <input type="text" class="form-control" id="contract_type" name="contract_type"
                               placeholder="Masukan bentuk kontrak"
                               value="{{ old('contract_type', $assetEntry->contract_form) }}" required>
                        <small class="helper-text">Jenis kontrak pengadaan</small>
                        <div class="invalid-feedback" id="contract_type_error"></div>
                    </div>

                    <div class="form-group textarea-field">
                        <label for="description" class="form-label">Keterangan</label>
                        <textarea class="form-control" id="description" name="description" rows="2"
                                  placeholder="Catatan tambahan (opsional)">{{ old('description', $assetEntry->description) }}</textarea>
                        <small class="helper-text">Informasi tambahan mengenai transaksi</small>
                        <div class="invalid-feedback" id="description_error"></div>
                    </div>
                </div>

                {{-- Preview of assets associated with this transaction --}}
                <!-- Related Assets Preview -->
                @if(isset($assetEntry) && ($assetEntry->assets_count ?? 0) > 0)
                    <div class="section-header" style="margin-top: 1rem;">
                        <div class="section-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="section-title">
                            <h3>Ringkasan Aset Dalam Transaksi Ini</h3>
                            <p class="section-subtitle">
                                Total aset: {{ number_format($assetEntry->assets_count) }} • Menampilkan maksimal 10
                                aset terbaru
                            </p>
                        </div>
                    </div>

                    <div class="simple-table-card mb-3">
                        <div class="table-responsive">
                            <table class="simple-table">
                                <thead>
                                <tr>
                                    <th>Kode QR</th>
                                    <th>Nama Barang</th>
                                    <th>Merk</th>
                                    <th>Tipe</th>
                                    <th>Nama barang di kontrak</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach(($assetEntry->assets ?? []) as $a)
                                    <tr>
                                        <td>
                                            <span class="simple-code">{{ $a->qr_code }}</span>
                                        </td>
                                        <td>{{ $a->item_name }}</td>
                                        <td>{{ $a->brand }}</td>
                                        <td>{{ $a->type ?: '-' }}</td>
                                        <td>{{ $a->asset_name }}</td>
                                        <td>
                                            <a href="{{ route('asset-management.asset-hospital.edit', $a->id) }}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        @if($assetEntry->assets_count > 10)
                            <div class="simple-footer">
                                <small class="text-muted">
                                    Menampilkan 10 dari {{ number_format($assetEntry->assets_count) }} total aset
                                </small>
                            </div>
                        @endif
                    </div>
                @endif

                {{-- Collapsible subsections for ASPAK and SIAP BMD data --}}
                <!-- Sub Sections inside Informasi Utama -->
                <div class="subsections"
                     style="margin-left: 1.5rem; border-left: 3px solid var(--primary-color); padding-left: 1rem; background: #f8fafc; border-radius: 0 8px 8px 0; margin-top: 1rem;">
                    {{-- ASPAK data subsection --}}
                    <!-- ASPAK Sub Section -->
                    <div class="subsection" id="aspak-section"
                         style="margin-bottom: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <button type="button" class="subsection-toggle" aria-expanded="false"
                                aria-controls="aspak-content">
                            <div class="section-header subsection-header">
                                <div class="section-icon"
                                     style="background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="section-title">
                                    <h3>ASPAK</h3>
                                    <p class="section-subtitle">Data ASPAK</p>
                                </div>
                                <i class="toggle-icon fas fa-chevron-down"></i>
                            </div>
                        </button>
                        <div class="subsection-content" id="aspak-content" style="display: none;">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="aspak_distributor" class="form-label">Distributor</label>
                                    <input type="text" class="form-control" id="aspak_distributor"
                                           name="aspak_distributor" placeholder="Otomatis dari Distributor"
                                           value="{{ old('aspak_distributor', $assetEntry->aspak_distributor) }}">
                                    <small class="helper-text">Duplikat dari field Distributor di Informasi
                                        Utama</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_maintenance_officer" class="form-label">PJ Pemeliharaan</label>
                                    <input type="text" class="form-control" id="aspak_maintenance_officer"
                                           name="aspak_maintenance_officer" placeholder="Masukkan nama PJ pemeliharaan"
                                           value="{{ old('aspak_maintenance_officer', $assetEntry->aspak_maintenance_pic) }}">
                                    <small class="helper-text">Penanggung jawab pemeliharaan aset</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_procurement_year" class="form-label">Tahun Pengadaan</label>
                                    <input type="text" class="form-control" id="aspak_procurement_year"
                                           name="aspak_procurement_year" readonly
                                           placeholder="Otomatis dari Tanggal Barang Masuk"
                                           value="{{ old('aspak_procurement_year', $assetEntry->received_date ? $assetEntry->received_date->format('Y') : '') }}">
                                    <small class="helper-text">Duplikat tahun dari Tanggal Barang Masuk</small>
                                </div>
                                <div class="form-group">
                                    <label for="aspak_procurement_source" class="form-label">Sumber Pengadaan</label>
                                    <input type="text" class="form-control" id="aspak_procurement_source"
                                           name="aspak_procurement_source" readonly
                                           placeholder="Otomatis dari Asal Perolehan"
                                           value="{{ old('aspak_procurement_source', $assetEntry->source_supply) }}">
                                    <small class="helper-text">Duplikat dari field Asal Perolehan di Informasi
                                        Utama</small>
                                </div>
                            </div>
                        </div>
                    </div>

                        {{-- SIAP BMD data subsection --}}
                    <!-- SIAP BMD Sub Section -->
                    <div class="subsection" id="siapbmd-section"
                         style="margin-bottom: 0.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                        <button type="button" class="subsection-toggle" aria-expanded="false"
                                aria-controls="siapbmd-content">
                            <div class="section-header subsection-header">
                                <div class="section-icon"
                                     style="background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div class="section-title">
                                    <h3>SIAP BMD</h3>
                                    <p class="section-subtitle">Data SIAP BMD</p>
                                </div>
                                <i class="toggle-icon fas fa-chevron-down"></i>
                            </div>
                        </button>
                        <div class="subsection-content" id="siapbmd-content" style="display: none;">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="siap_bmd_year" class="form-label">Tahun</label>
                                    <input type="text" class="form-control" id="siap_bmd_year" name="siap_bmd_year"
                                           readonly
                                           value="{{ old('siap_bmd_year', $assetEntry->received_date ? $assetEntry->received_date->format('Y') : '') }}">
                                    <small class="form-text helper-text">Otomatis diambil dari Tanggal Barang
                                        Masuk</small>
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_rkbmd_code" class="form-label">Kode RKBMD</label>
                                    <input type="text" class="form-control" id="siap_bmd_rkbmd_code"
                                           name="siap_bmd_rkbmd_code" placeholder="Masukkan kode RKBMD"
                                           value="{{ old('siap_bmd_rkbmd_code', $assetEntry->siap_bmd_asset_need_plan_code) }}">
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_activity" class="form-label">Kegiatan</label>
                                    <select class="form-select select2-ajax" id="siap_bmd_activity"
                                            name="siap_bmd_activity" data-placeholder="Pilih Kegiatan">
                                        <option value="">Pilih Kegiatan</option>
                                        @if($assetEntry->siapBmdActivity)
                                            <option value="{{ $assetEntry->siapBmdActivity->id }}"
                                                    selected>{{ $assetEntry->siapBmdActivity->program_code . ' - ' . $assetEntry->siapBmdActivity->program_name }}</option>
                                        @endif
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_sub_activity" class="form-label">SUB Kegiatan</label>
                                    <select class="form-select select2-ajax" id="siap_bmd_sub_activity"
                                            name="siap_bmd_sub_activity" data-placeholder="Pilih Sub Kegiatan" disabled>
                                        <option value="">Pilih Sub Kegiatan</option>
                                        @if($assetEntry->siapBmdSubActivity)
                                            <option value="{{ $assetEntry->siapBmdSubActivity->id }}"
                                                    selected>{{ $assetEntry->siapBmdSubActivity->program_code . ' - ' . $assetEntry->siapBmdSubActivity->program_name }}</option>
                                        @endif
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_kusa_name" class="form-label">Nama Kusa</label>
                                    <input type="text" class="form-control" id="siap_bmd_kusa_name"
                                           name="siap_bmd_kusa_name" placeholder="Masukkan nama kusa"
                                           value="{{ old('siap_bmd_kusa_name', $assetEntry->siap_bmd_asset_user_name) }}">
                                </div>
                                <div class="form-group">
                                    <label for="siap_bmd_hospital_name" class="form-label">Nama RS</label>
                                    <input type="text" class="form-control" id="siap_bmd_hospital_name"
                                           name="siap_bmd_hospital_name" placeholder="Masukkan nama rumah sakit"
                                           value="{{ old('siap_bmd_hospital_name', config('app.hospital_name')) }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Visual divider between main info and item details --}}
                <!-- Section Divider -->
                <div class="section-divider"
                     style="border-top: 2px solid var(--border-color); margin: 1.5rem 0; position: relative;">
                    <div
                        style="position: absolute; top: -8px; left: 50%; transform: translateX(-50%); background: white; padding: 0 1rem; color: var(--helper-text); font-size: 0.7rem; font-weight: 600;">
                        DETAIL BARANG
                    </div>
                </div>

                {{-- Section for detailed item information --}}
                <!-- Items Section -->
                <div class="items-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="section-title">
                            <h3>Detail Barang</h3>
                            <p class="section-subtitle">Kelola item barang dengan perhitungan harga otomatis</p>
                        </div>
                    </div>

                    <div id="itemsContainer">
                        {{-- Individual item entry form --}}
                        <!-- Single item row for editing -->
                        <div class="item-row border rounded p-3 mb-3" data-index="0">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="text-secondary mb-0"><i class="fas fa-box me-2"></i>Barang 1</h6>
                            </div>

                            <!-- Hidden fields for asset IDs -->
                            <input type="hidden" name="asset_ids[0]" value="{{ $asset->id }}">
                            <input type="hidden" name="asset_entry_id" value="{{ $assetEntry->id }}">

                            <!-- Column 1: Item codes, names -->
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="item_id" class="form-label">Kode Barang <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control item-code" name="item_id" required
                                           placeholder="Kode Barang" readonly disabled
                                           value="{{ old('item_id', $asset->item ? $asset->item->item_code . ' - ' . $asset->item->item_name : '') }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="item_name" class="form-label">Nama Barang <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="item_name" required
                                           placeholder="Masukkan nama barang" readonly disabled
                                           value="{{ old('item_name', $asset->item_name) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="contract_item_name" class="form-label">Nama Barang di Kontrak
                                        <span class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="contract_item_name"
                                           placeholder="Masukkan nama barang di kontrak" required
                                           value="{{ old('contract_item_name', $asset->asset_name) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- New row for Merk and No. AKD/AKL -->
                            <div class="row">
                                <div class="col-lg-6 col-md-6 mb-3">
                                    <label for="brand" class="form-label">Merk <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="brand"
                                           placeholder="Masukkan merk" required
                                           value="{{ old('brand', $asset->brand) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-6 col-md-6 mb-3">
                                    <label for="akd_akl_number" class="form-label">No. AKD/AKL <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="akd_akl_number"
                                           placeholder="Masukkan nomor AKD/AKL" required
                                           value="{{ old('akd_akl_number', $asset->akd_number) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- New row for Tipe/Model and Spesifikasi Umum -->
                            <div class="row">
                                <div class="col-lg-6 col-md-6 mb-3">
                                    <label for="type_model" class="form-label">Tipe/Model <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="type_model"
                                           placeholder="Masukkan tipe/model" required
                                           value="{{ old('type_model', $asset->type) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-6 col-md-6 mb-3">
                                    <label for="general_specification" class="form-label">Spesifikasi
                                        Umum</label>
                                    <input type="text" class="form-control" name="general_specification"
                                           placeholder="Masukkan spesifikasi umum"
                                           value="{{ old('general_specification', $asset->general_specifications) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Column 2: Brand, type, specifications -->
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="material" class="form-label">Bahan <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="material"
                                           placeholder="Masukkan bahan" required
                                           value="{{ old('material', $asset->material) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="size" class="form-label">Ukuran <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="size"
                                           placeholder="Masukkan ukuran" required
                                           value="{{ old('size', $asset->size) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Column 3: Material, size, unit, prices -->
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="uom_id" class="form-label">Satuan Barang <span
                                            class="required-indicator">*</span></label>
                                    <select class="form-select item-uom" name="uom_id" required
                                            data-placeholder="Pilih Satuan">
                                        <option value="">Pilih Satuan</option>
                                        @if($asset->uom)
                                            <option value="{{ $asset->uom->id }}"
                                                    selected>{{ $asset->uom->uom_name }}</option>
                                        @endif
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="unit_price" class="form-label">Harga Satuan <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control currency-input no-prefix item-unit-price"
                                           name="unit_price" required placeholder="Masukkan harga satuan"
                                           value="{{ old('unit_price', number_format($asset->unit_price, 0, ',', '.')) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Column 4: Identification numbers -->
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="asset_description" class="form-label">Keterangan
                                        Asset</label>
                                    <textarea class="form-control" name="asset_description" rows="2"
                                              placeholder="Masukkan keterangan asset">{{ old('asset_description', $asset->description) }}</textarea>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="serial_number" class="form-label">No. SN <span
                                            class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" name="serial_number"
                                           placeholder="Masukkan nomor serial" required
                                           value="{{ old('serial_number', $asset->serial_number) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="rfid_tag" class="form-label">Tag RFID</label>
                                    <input type="text" class="form-control" name="rfid_tag"
                                           placeholder="Masukkan tag RFID"
                                           value="{{ old('rfid_tag', $asset->rfid_tag) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="frame_number" class="form-label">No. Rangka</label>
                                    <input type="text" class="form-control" name="frame_number"
                                           placeholder="Masukkan nomor rangka"
                                           value="{{ old('frame_number', $asset->chassis_number) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="engine_number" class="form-label">No. Mesin</label>
                                    <input type="text" class="form-control" name="engine_number"
                                           placeholder="Masukkan nomor mesin"
                                           value="{{ old('engine_number', $asset->engine_number) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Column 5: Additional identification -->
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="police_number" class="form-label">No. Polisi</label>
                                    <input type="text" class="form-control" name="police_number"
                                           placeholder="Masukkan nomor polisi"
                                           value="{{ old('police_number', $asset->license_plate_number) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="bpkb_number" class="form-label">No. BPKB</label>
                                    <input type="text" class="form-control" name="bpkb_number"
                                           placeholder="Masukkan nomor BPKB"
                                           value="{{ old('bpkb_number', $asset->bpkb_number) }}">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <label for="condition" class="form-label">Kondisi <span
                                            class="required-indicator">*</span></label>
                                    <select class="form-select" name="condition" required>
                                        <option
                                            value="BAIK" {{ old('condition', $asset->asset_condition) == 'BAIK' ? 'selected' : '' }}>
                                            BAIK
                                        </option>
                                        <option
                                            value="RUSAK_RINGAN" {{ old('condition', $asset->asset_condition) == 'RUSAK_RINGAN' ? 'selected' : '' }}>
                                            RUSAK_RINGAN
                                        </option>
                                        <option
                                            value="RUSAK_BERAT" {{ old('condition', $asset->asset_condition) == 'RUSAK_BERAT' ? 'selected' : '' }}>
                                            RUSAK_BERAT
                                        </option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            {{-- Item-specific ASPAK and SIAP BMD data --}}
                            <!-- Sub Sections for Item -->
                            <div class="subsections mt-3">
                                {{-- Item-specific ASPAK data --}}
                                <!-- ASPAK Sub Section for Item -->
                                <div class="subsection" id="item-aspak-section-0">
                                    <button type="button" class="subsection-toggle" aria-expanded="false"
                                            aria-controls="item-aspak-content-0">
                                        <div class="section-header subsection-header">
                                            <div class="section-icon">
                                                <i class="fas fa-database"></i>
                                            </div>
                                            <div class="section-title">
                                                <h3>ASPAK</h3>
                                                <p class="section-subtitle">Data ASPAK untuk barang ini</p>
                                            </div>
                                            <i class="toggle-icon fas fa-chevron-down"></i>
                                        </div>
                                    </button>
                                    <div class="subsection-content" id="item-aspak-content-0" style="display: none;">
                                        <div class="form-grid">
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_item_id">Nama
                                                    Barang</label>
                                                <select class="form-select aspak-nama-barang"
                                                        name="aspak_item_id"
                                                        data-placeholder="Pilih Nama Barang">
                                                    <option value="">Pilih Nama Barang</option>
                                                    @if($asset->aspakItem)
                                                        <option value="{{ $asset->aspakItem->id }}"
                                                                selected>{{ $asset->aspakItem->item_code . ' - ' . $asset->aspakItem->item_name }}</option>
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label"
                                                       for="aspak_service_room_id">Lokasi</label>
                                                <select class="form-select aspak-lokasi"
                                                        name="aspak_service_room_id"
                                                        data-placeholder="Pilih Lokasi">
                                                    <option value="">Pilih Lokasi</option>
                                                    @if($asset->aspakServiceRoom)
                                                        <option value="{{ $asset->aspakServiceRoom->id }}"
                                                                selected>{{ $asset->aspakServiceRoom->room_service_code . ' - ' . $asset->aspakServiceRoom->room_service_name }}</option>
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_merk">Merk Alat</label>
                                                <input type="text" class="form-control" name="aspak_merk"
                                                       placeholder="Masukkan merk" readonly disabled
                                                       value="{{ old('aspak_merk', $asset->brand) }}">
                                                <small class="helper-text">Duplikat dari Merk di informasi Umum</small>
                                                <div class="invalid-feedback" id="aspak_merk_error"></div>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_merk_tipe">Merk
                                                    Tipe</label>
                                                <input type="text" class="form-control" name="aspak_merk_tipe"
                                                       placeholder="Masukkan merk tipe" readonly disabled
                                                       value="{{ old('aspak_merk_tipe', $asset->type) }}">
                                                <small class="helper-text">Duplikat dari Tipe/Model di informasi
                                                    Umum</small>
                                                <div class="invalid-feedback"
                                                     id="aspak_merk_tipe_error"></div>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_alat">Alat</label>
                                                <input type="text" class="form-control" name="aspak_alat"
                                                       placeholder="Masukkan nama alat"
                                                       value="{{ old('aspak_alat', $asset->aspak_tool) }}">
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_kondisi_alat">Kondisi
                                                    Alat</label>
                                                <select class="form-select" name="aspak_kondisi_alat" readonly
                                                        disabled>
                                                    <option
                                                        value="BAIK" {{ old('aspak_kondisi_alat', $asset->asset_condition) == 'BAIK' ? 'selected' : '' }}>
                                                        BAIK
                                                    </option>
                                                    <option
                                                        value="RUSAK_RINGAN" {{ old('aspak_kondisi_alat', $asset->asset_condition) == 'RUSAK_RINGAN' ? 'selected' : '' }}>
                                                        RUSAK_RINGAN
                                                    </option>
                                                    <option
                                                        value="RUSAK_BERAT" {{ old('aspak_kondisi_alat', $asset->asset_condition) == 'RUSAK_BERAT' ? 'selected' : '' }}>
                                                        RUSAK_BERAT
                                                    </option>
                                                </select>
                                                <small class="helper-text">Duplikat dari Kondisi di informasi
                                                    umum</small>
                                                <div class="invalid-feedback"
                                                     id="aspak_kondisi_alat_error"></div>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label"
                                                       for="aspak_product_origin">Produk</label>
                                                <select class="form-select" name="aspak_product_origin">
                                                    <option value="">Pilih Produk</option>
                                                    <option
                                                        value="DOMESTIC" {{ old('aspak_product_origin', $asset->product_origin) == 'DOMESTIC' ? 'selected' : '' }}>
                                                        Dalam Negeri
                                                    </option>
                                                    <option
                                                        value="FOREIGN" {{ old('aspak_product_origin', $asset->product_origin) == 'FOREIGN' ? 'selected' : '' }}>
                                                        Luar Negeri
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="form-group textarea-field">
                                                <label class="form-label"
                                                       for="aspak_description">Keterangan</label>
                                                <textarea class="form-control" name="aspak_description"
                                                          rows="2"
                                                          placeholder="Catatan tambahan (opsional)">{{ old('aspak_description', $asset->aspak_description) }}</textarea>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_image_url">Link
                                                    Gambar</label>
                                                <input type="url" class="form-control" name="aspak_image_url"
                                                       placeholder="https://..."
                                                       value="{{ old('aspak_image_url', $asset->aspak_image_url) }}">
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_power_source">Sumber
                                                    daya</label>
                                                <select class="form-select" name="aspak_power_source">
                                                    <option value="">Pilih Sumber Daya</option>
                                                    <option
                                                        value="AC" {{ old('aspak_power_source', $asset->power_source) == 'AC' ? 'selected' : '' }}>
                                                        AC
                                                    </option>
                                                    <option
                                                        value="DC" {{ old('aspak_power_source', $asset->power_source) == 'DC' ? 'selected' : '' }}>
                                                        DC
                                                    </option>
                                                    <option
                                                        value="None" {{ old('aspak_power_source', $asset->power_source) == 'None' ? 'selected' : '' }}>
                                                        None
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_electricity">Daya
                                                    (Watt)</label>
                                                <input type="number" class="form-control"
                                                       name="aspak_electricity" placeholder="Contoh: 500"
                                                       value="{{ old('aspak_electricity', $asset->electricity) }}">
                                            </div>
                                            <div class="form-group" style="display:flex;align-items:center;gap:.5rem;">
                                                <input type="checkbox" class="form-check-input"
                                                       id="items-0-aspak_connected_ups"
                                                       name="aspak_tersambung_ups"
                                                       value="1" {{ old('aspak_tersambung_ups', $asset->connected_ups) ? 'checked' : '' }}>
                                                <label class="form-label" for="items-0-aspak_connected_ups"
                                                       style="margin:0;">Tersambung ke UPS</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_information_tkdn">Informasi
                                                    TKDN</label>
                                                <input type="text" class="form-control"
                                                       name="aspak_information_tkdn"
                                                       placeholder="Masukkan informasi TKDN"
                                                       value="{{ old('aspak_information_tkdn', $asset->information_tkdn) }}">
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_risk_class">Kelas
                                                    Risiko</label>
                                                <input type="text" class="form-control"
                                                       name="aspak_risk_class"
                                                       placeholder="Masukkan kelas risiko"
                                                       value="{{ old('aspak_risk_class', $asset->risk_class) }}">
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="aspak_technical_lifetime_year">Umur
                                                    Teknis (tahun)</label>
                                                <input type="text" class="form-control"
                                                       name="aspak_technical_lifetime_year"
                                                       placeholder="Contoh: 3"
                                                       value="{{ old('technical_lifetime_year', $asset->technical_lifetime_year) }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                    {{-- Item-specific SIAP BMD data --}}
                                <!-- SIAP BMD Sub Section for Item -->
                                <div class="subsection" id="item-siapbmd-section-0">
                                    <button type="button" class="subsection-toggle" aria-expanded="false"
                                            aria-controls="item-siapbmd-content-0">
                                        <div class="section-header subsection-header">
                                            <div class="section-icon"
                                                 style="background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);">
                                                <i class="fas fa-clipboard-list"></i>
                                            </div>
                                            <div class="section-title">
                                                <h3>SIAP BMD</h3>
                                                <p class="section-subtitle">Data SIAP BMD untuk barang ini</p>
                                            </div>
                                            <i class="toggle-icon fas fa-chevron-down"></i>
                                        </div>
                                    </button>
                                    <div class="subsection-content" id="item-siapbmd-content-0" style="display: none;">
                                        <div class="form-grid">
                                            <div class="form-group">
                                                <label class="form-label" for="siap_bmd_item_code">Kode
                                                    Barang</label>
                                                <input type="text" class="form-control"
                                                       name="siap_bmd_item_code"
                                                       placeholder="Otomatis dari Kode Barang" readonly
                                                       value="{{ old('siap_bmd_item_code', $asset->item ? $asset->item->item_code : '') }}">
                                                <small class="helper-text">Duplikat dari Kode Barang di informasi
                                                    Umum</small>
                                                <div class="invalid-feedback"
                                                     id="siap_bmd_item_code_error"></div>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="siap_bmd_asset_name">Nama
                                                    Aset</label>
                                                <input type="text" class="form-control"
                                                       name="siap_bmd_asset_name"
                                                       placeholder="Otomatis dari Nama Barang" readonly
                                                       value="{{ old('siap_bmd_asset_name', $asset->item_name) }}">
                                                <small class="helper-text">Duplikat dari Nama Barang di informasi
                                                    Umum</small>
                                                <div class="invalid-feedback"
                                                     id="siap_bmd_asset_name_error"></div>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="siap_bmd_common_name">Nama
                                                    Umum</label>
                                                <input type="text" class="form-control"
                                                       name="siap_bmd_common_name"
                                                       placeholder="Otomatis dari Nama Barang di Kontrak" readonly
                                                       value="{{ old('siap_bmd_common_name', $asset->asset_name) }}">
                                                <small class="helper-text">Duplikat dari Nama Barang di Kontrak di
                                                    informasi Umum</small>
                                                <div class="invalid-feedback"
                                                     id="siap_bmd_common_name_error"></div>
                                            </div>
                                            <div class="form-group">
                                                <label class="form-label" for="siap_bmd_model">Model</label>
                                                <input type="text" class="form-control" name="siap_bmd_model"
                                                       placeholder="Otomatis dari Tipe/Model" readonly
                                                       value="{{ old('siap_bmd_model', $asset->type) }}">
                                                <small class="helper-text">Duplikat dari Tipe/Model di informasi
                                                    Umum</small>
                                                <div class="invalid-feedback" id="siap_bmd_model_error"></div>
                                            </div>
                                            <div class="form-group textarea-field">
                                                <label class="form-label"
                                                       for="siap_bmd_specification">Spesifikasi</label>
                                                <textarea class="form-control" name="siap_bmd_specification"
                                                          rows="2" placeholder="Otomatis dari Spesifikasi Umum"
                                                          readonly>{{ old('siap_bmd_specification', $asset->general_specifications) }}</textarea>
                                                <small class="helper-text">Duplikat dari Spesifikasi Umum di informasi
                                                    Umum</small>
                                                <div class="invalid-feedback"
                                                     id="siap_bmd_specification_error"></div>
                                            </div>
                                            <div class="form-group textarea-field">
                                                <label class="form-label"
                                                       for="siap_bmd_placement_description">Deskripsi</label>
                                                <textarea class="form-control"
                                                          name="siap_bmd_placement_description" rows="2"
                                                          placeholder="Otomatis dari Keterangan Asset"
                                                          readonly>{{ old('siap_bmd_placement_description', $asset->description) }}</textarea>
                                                <small class="helper-text">Duplikat dari Keterangan Asset di informasi
                                                    Umum</small>
                                                <div class="invalid-feedback"
                                                     id="siap_bmd_placement_description_error"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Action buttons for form submission --}}
                <!-- Form Actions -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary btn-modern" onclick="window.history.back()">
                        <i class="fas fa-times"></i>
                        <span>Batal</span>
                    </button>
                    <button type="submit" class="btn btn-primary btn-modern" id="submitBtn">
                        <i class="fas fa-save"></i>
                        <span>Simpan</span>
                    </button>
                </div>
            </form>
            <!-- Lightweight overlay for loading state -->
            <div id="formLoadingOverlay" class="d-none"
                 style="position: absolute; inset: 0; background: rgba(255,255,255,0.6); display: flex; align-items: center; justify-content: center; z-index: 10;">
                <div class="spinner-border text-primary" role="status" aria-live="polite"
                     aria-label="Menyimpan..."></div>
            </div>
        </div>
    </div>

    {{-- Confirmation and feedback modals --}}
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmSubmissionModal" tabindex="-1" aria-labelledby="confirmSubmissionModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmSubmissionModalLabel">Confirm Submission</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to submit this form? Please review all information before confirming.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="confirmSubmitBtn">Submit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Bootstrap Modal for Form Feedback -->
    <div class="modal fade" id="formFeedbackModal" tabindex="-1" aria-labelledby="formFeedbackModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="formFeedbackModalLabel">Notification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="modalMessageContent"></div>
                    <div id="errorMessagesSection" class="d-none mt-3">
                        <ul id="errorMessagesList" class="mb-0 ps-3"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
    <script src="{{ asset('/js/app/asset-management/editAssetHospital.js') }}"></script>
    <script>
        window.updateUrl = '{{ route("asset-management.asset-hospital.update", $asset->id) }}';
    </script>
@endpush
