@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.asset_management')
@endpush

@section('content')
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <div class="panel-body">
            <div class="d-flex justify-content-end mb-3">
                <button type="button" id="btn-add-row" class="btn btn-outline-primary me-1"><i
                        class="fas fa-plus-circle me-1"></i><PERSON>bah Baris</button>
            </div>

            <div class="table-responsive">
                <form action="" method="post" id="form-allocation">
                    <table class="table table-bordered table-striped w-100 mb-3">
                        <thead>
                            <tr>
                                <th>Data Aset</th>
                                <th><PERSON><PERSON></th>
                                <th>Kode Register / SN</th>
                                <th>Peruntukan</th>
                                <th class="text-center">#</th>
                            </tr>
                        </thead>
                        <tbody id="table-allocation"></tbody>
                    </table>

                    <div class="form-group d-flex justify-content-end">
                        <a href="{{ route('asset-management.asset-hospital.index') }}"
                            class="btn btn-outline-danger me-1"><i class="fas fa-undo me-1"></i>Kembali</a>
                        <button type="button" id="btn-save" class="btn btn-primary"><i
                                class="fas fa-save me-1"></i>Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/asset-management/alocationAssetHospital.js') }}"></script>
@endpush
