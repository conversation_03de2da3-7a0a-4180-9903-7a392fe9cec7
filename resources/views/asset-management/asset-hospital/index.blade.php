@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in asset table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <a href="{{ route('asset-management.asset-hospital.create') }}" class="btn btn-primary btn-modern-rounded add-btn">
                <i class="fas fa-plus"></i>
                <span>Tambah Aset</span>
            </a>
            <a href="#modal-dialog-import" class="btn btn-info btn-modern-rounded import-btn" data-bs-toggle="modal">
                <i class="fas fa-upload"></i>
                <span>Import Aset</span>
            </a>
            <a href="{{ route('asset-management.asset-hospital.print_label') }}" class="btn btn-secondary btn-modern-rounded print-btn">
                <i class="fas fa-print"></i>
                <span>Print Label</span>
            </a>
            <div class="datatable-filter-icon btn-modern-rounded" data-bs-toggle="modal" data-bs-target="#filterDrawer">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Foto Aset</th>
                    <th>Foto Mutasi</th>
                    <th class="text-center" width="7%">Kode QR</th>
                    <th>Nama Barang</th>
                    <th>Kode Barang</th>
                    <th>Kode Register / SN</th>
                    <th>Tanggal Pembayaran</th>
                    <th>Tanggal Barang Masuk</th>
                    <th>Tahun</th>
                    <th>Asal Perolehan</th>
                    <th>Peruntukan</th>
                    <th style="width: 80px;">Aksi</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Filter Data</label>
                    <select name="kategori" id="kategori" class="form-select form-control">
                        <option value="all">Semua Data Aset</option>
                        <option value="not-allocation">Belum di Alokasikan</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>


{{-- Import Data Aset --}}
<div class="modal fade" id="modal-dialog-import">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Import Data Aset</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="" method="post" enctype="multipart/form-data" id="form-import-asset">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="file-import" class="form-label">File</label>
                        <input type="file" class="form-control" id="file-import" name="file_import">

                        <span class="d-block text-danger" id="error_file_import"></span>
                    </div>
                </div>
                <div class=" modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <a href="{{ route('asset-management.asset-hospital.template.download') }}" class="btn btn-info"><i class="fas fa-download me-1"></i>Download Format</a>
                    <button type="button" class="btn btn-success" id="btn-import-asset"><i class="fas fa-upload"></i> Import</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-img-preview">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Preview Foto</h4>
            </div>
            <div class="modal-body text-center">
                <img src="" class="img-asset-preview img-fluid" alt="">
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Photo -->
<div class="modal fade" id="modal-edit-photo" tabindex="-1" aria-labelledby="modal-edit-photo-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-photo-label">Update Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="form-upload-photo" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <input type="hidden" id="asset-id" name="asset_id">
                    <div class="mb-3">
                        <label for="photo" class="form-label">Pilih Foto <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="photo" name="photo" accept="image/*" required>
                        <div class="form-text">Format yang diizinkan: JPG, JPEG, PNG. Maksimal 2MB.</div>
                        <div class="form-error-message text-danger" id="error_photo"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Preview:</label>
                        <div class="text-center">
                            <img id="photo-preview" src="" alt="Preview" class="img-fluid" style="max-height: 400px; display: none;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="btn-upload-photo">Upload Photo</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal QR Code -->
<div class="modal fade" id="modal-qr-code" tabindex="-1" aria-labelledby="modal-qr-code-label" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-qr-code-label">QR Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <div id="qr-code-image" class="mb-3"></div>
                    <label class="form-label fw-bold">Kode QR:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="qr-code-text" readonly>
                        <button class="btn btn-outline-secondary" type="button" id="copy-qr-code">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetHospital.js') }}"></script>
@endpush
