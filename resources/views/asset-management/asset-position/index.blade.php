@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
@include('menu.asset_management')
@endpush

@section('content')
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in kartu inventaris ruangan table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <div class="datatable-filter-icon btn-modern-rounded">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>Kode Ruangan</th>
                    <th>Nama Ruangan</th>
                    <th>Nama Gedung</th>
                    <th>Penanggung Jawab</th>
                    <th>Jumlah Aset</th>
                    <th style="width: 80px;">Action</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="250">250</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Kategori Ruangan</label>
                    <select name="category" id="category" class="form-select">
                        <option value="all">Semua Kategori</option>
                        @foreach($roomCategories as $category)
                        <option value="{{ $category->id }}">{{ $category->room_category_name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearFilter">Clear Filter</button>
                <button type="button" class="btn btn-primary" id="applyFilter">Apply Filter</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Kategori</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between border-bottom mb-3 pb-2">
                    <div class="d-block fs-14px mb-2">
                        Kode Ruangan : <strong id="kode_ruangan">123</strong><br>
                        Nama Ruangan : <strong id="nama_ruangan">123</strong>
                    </div>

                    <div class="d-block fs-14px text-end">
                        PIC Ruangan : <strong id="pic_room">123</strong><br>
                        Jumlah Aset : <strong id="jumlah_aset">10</strong>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped w-100" id="datatable-asset">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Kode QR</th>
                                <th>Kode Barang</th>
                                <th>Nama Aset</th>
                                <th>Kode Register / SN</th>
                                <th>Merk</th>
                                <th>Distributor</th>
                                <th>Harga</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                <a href="javascript:;" class="btn btn-warning btn-export-excel" data-id=""><i class="fas fa-file-excel me-1"></i> Export Excel</a>

            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script>
    let guard = @json($guard);
</script>
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/js/app/master/app.js') }}"></script>
<script src="{{ asset('/js/app/asset-management/assetPosition.js') }}"></script>
@endpush
