<div class="d-flex justify-content-between border-bottom mb-3 pb-2">
    <div class="d-block fs-14px mb-2">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-1">Nama Aset : <strong id="asset_name">{{ $asset->item->item_name }}</strong></div>
                <div class="mb-1">Kode Barang : <strong id="item_code">{{ $asset->item->item_code }}</strong></div>
                <div class="mb-1">Harga : <strong id="unit_price">{{ number_format($asset->unit_price, 0, ",", ".") }}</strong></div>
                <div class="mb-1">Peruntukan : <strong id="location">{{ $asset->room->room_name ?? "-" }}</strong></div>
                <div class="mb-1">Kode QR : <strong id="qr_code_asset">{{ $asset->qr_code }}</strong></div>
                <div class="mb-1">Tag RFID : <strong id="tag_rfid">{{ $asset->rfid_tag }}</strong></div>
            </div>
            <div class="col-md-6">
                <div class="mb-1">Kode Register : <strong id="register_code">{{ $asset->register_code }}</strong></div>
                <div class="mb-1">Serial Number : <strong id="serial_num">{{ $asset->serial_number }}</strong></div>
                <div class="mb-1">No. Rangka : <strong id="chassis_num">{{ $asset->chassis_number }}</strong></div>
                <div class="mb-1">No. Mesin : <strong id="engine_num">{{ $asset->engine_number }}</strong></div>
                <div class="mb-1">No. Polisi : <strong id="license_num">{{ $asset->license_plate_number }}</strong></div>
                <div class="mb-1">No. BPKB : <strong id="bpkb_num">{{ $asset->bpkb_number }}</strong></div>
            </div>
        </div>
    </div>
    <div class="d-block fs-14px text-end">
        {!! $qrCode !!}
    </div>
</div>

<div class="mb-4">
    <h5 class="border-bottom pb-2 mb-3">Document BAST</h5>
    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Tanggal</th>
                    <th>Jenis</th>
                    <th>Pihak Pertama</th>
                    <th>Pihak Kedua</th>
                    <th>Ruangan</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($documents as $document)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $document->created_at }}</td>
                    <td>{{ $document->document_type }}</td>
                    <td>{{ $document->party1_name }}</td>
                    <td>{{ $document->party2_name }}</td>
                    <td>{{ $document->room->room_name ?? "-" }}</td>
                    <td>
                        <a target="_blank" href="{{ asset("/storage/" . $document->document_path) }}" class="btn btn-sm btn-outline-primary btn-modern-rounded">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<div class="mb-4">
    <h5 class="border-bottom pb-2 mb-3">Jadwal Pemeliharaan</h5>
    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Tanggal</th>
                    <th>Jenis</th>
                    <th>Kategori</th>
                    <th>Note</th>
                    <th>Ruangan</th>
                </tr>
            </thead>
            <tbody>
                @foreach($maintenanceSchedule as $mts)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $mts->schedule_date }}</td>
                    <td>{{ $mts->schedule_type }}</td>
                    <td>{{ $mts->schedule_category }}</td>
                    <td>{{ $mts->schedule_note }}</td>
                    <td>{{ $mts->room_name  ?? "-" }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<div class="mb-4">
    <h5 class="border-bottom pb-2 mb-3">Riwayat Pemeliharaan</h5>
    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Tanggal</th>
                    <th>Kategori</th>
                    <th>Jenis</th>
                    <th>Total Pengeluaran (Rp.)</th>
                    <th>Note</th>
                    <th>Ruangan</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($maintenanceActivity as $mtact)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $mtact->activity_date }}</td>
                    <td>{{ $mtact->activity_category }}</td>
                    <td>{{ $mtact->activity_type }}</td>
                    <td>{{ number_format($mtact->activity_cost, 0, ",", ".") }}</td>
                    <td>{{ $mtact->notes }}</td>
                    <td>{{ $mtact->room_name }}</td>
                    <td>
                        <a href="{{ asset('/storage/' . $mtact->document_path) }}" target="_blank" class="btn btn-sm btn-outline-primary btn-modern-rounded">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<div class="mb-4">
    <h5 class="border-bottom pb-2 mb-3">Riwayat Perbaikan</h5>
    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Tanggal</th>
                    <th>Kerusakan</th>
                    <th>Dokumen Kerusakan</th>
                    <th>Tanggal Follow Up</th>
                    <th>Notes</th>
                    <th>Status</th>
                    <th>Dokumen Follow Up</th>
                </tr>
            </thead>
            <tbody>
                @foreach($maintenanceIncidental as $incident)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $incident->request_date }}</td>
                    <td>{{ $incident->request_issue }}</td>
                    <td>
                        <a href="{{ asset("/storage/". $incident->document_issue_path) }}" target="_blank" class="btn btn-sm btn-outline-primary btn-modern-rounded">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </td>
                    <td>{{ $incident->followup_date }}</td>
                    <td>{{ $incident->followup_notes }}</td>
                    <td>{{ $incident->followup_type }}</td>
                    <td>
                        <a href="{{ asset("/storage/". $incident->document_followup_path) }}" target="_blank" class="btn btn-sm btn-outline-primary btn-modern-rounded">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>