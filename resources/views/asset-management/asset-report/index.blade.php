@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_report_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.asset_management')
@endpush

@section('content')
    <div class="report-card">
        <div class="report-header">
            <h4>{{ $title ?? 'Laporan Asset Management' }}</h4>
        </div>

        <div class="report-body">
            <div class="d-flex justify-content-end mb-4">
                <button id="btn_export_excel" data-export-url="{{ $exportRoute }}"
                    data-export-filename="{{ $title }}"
                    data-params="{'start_date': '', 'end_date': ''}" class="btn btn-export" disabled>
                    <i class="fa fa-file-excel"></i>
                    Export Excel
                </button>
            </div>

            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label><i>Periode</i></label>
                        <input id="datepicker" type="text" class="form-control" readonly="readonly"
                            style="background-color:white; cursor:pointer;">
                    </div>

                    <div class="filter-group">
                        <button type="button" id="btn-filter" class="btn btn-show-data">
                            <i class="fas fa-search me-2"></i>
                            Tampilkan Data
                        </button>
                    </div>
                </div>
            </div>

            <div class="target-report" id="target-report">
                <!-- Empty State - akan ditampilkan saat awal -->
                <div class="empty-state" id="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="empty-state-title">
                        Mulai Analisis Data Asset Management
                    </div>
                    <div class="empty-state-description">
                        Pilih periode yang ingin Anda analisis, kemudian klik "Tampilkan Data" untuk melihat
                        laporan detail asset management.
                    </div>

                    <div class="empty-state-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">Pilih Periode</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">Klik Tampilkan Data</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('/js/app/asset-management/report.js') }}"></script>

    <script>
        // Hide empty state when data is loaded
        $(document).ready(function() {
            $('#btn-filter').on('click', function() {
                $('#empty-state').hide();
            });
        });
    </script>
@endpush
