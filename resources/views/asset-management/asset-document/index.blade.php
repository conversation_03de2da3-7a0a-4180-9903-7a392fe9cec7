@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
<style>
    /* Styling untuk header dokumen BAST */
    .datatable-modern-table th {
        text-align: center !important;
        vertical-align: middle !important;
        font-weight: 600;
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
    }
    
    /* Styling untuk kolom dokumen */
    .datatable-modern-table td:nth-child(8),
    .datatable-modern-table td:nth-child(9),
    .datatable-modern-table td:nth-child(10),
    .datatable-modern-table td:nth-child(11),
    .datatable-modern-table td:nth-child(12) {
        text-align: center !important;
        vertical-align: middle !important;
    }
    
    /* Hover effect untuk baris tabel */
    .datatable-modern-table tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05) !important;
    }
    
    /* Styling untuk icon dokumen */
    .datatable-modern-table .text-center i {
        font-size: 18px;
        transition: all 0.3s ease;
        padding: 4px;
        border-radius: 4px;
    }
    
    .datatable-modern-table .text-center a:hover i {
        transform: scale(1.1);
        background-color: rgba(25, 135, 84, 0.1);
    }
    
    .datatable-modern-table .text-center i.text-muted {
        opacity: 0.6;
    }
    
    .datatable-modern-table .text-center i.text-success {
        opacity: 1;
    }
    
    /* Tooltip styling */
    .tooltip {
        font-size: 12px;
    }
    
    .tooltip-inner {
        background-color: #333;
        color: white;
        border-radius: 4px;
        padding: 6px 10px;
        max-width: 200px;
    }
    
    .tooltip.bs-tooltip-top .tooltip-arrow::before {
        border-top-color: #333;
    }
    
    .tooltip.bs-tooltip-bottom .tooltip-arrow::before {
        border-bottom-color: #333;
    }
    
    .tooltip.bs-tooltip-start .tooltip-arrow::before {
        border-left-color: #333;
    }
    
    .tooltip.bs-tooltip-end .tooltip-arrow::before {
        border-right-color: #333;
    }
    
    
</style>
@endpush

@push("menu")
@include("menu.asset_management")
@endpush

@section("content")
@if ($errors->any())
<div class="alert alert-danger alert-dismissible fade show">
    <strong>Error!</strong> <br>
    @foreach ($errors->all() as $error)
    <li>{{ $error }}</li>
    @endforeach
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
@endif

<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in asset document table">
            </div>
        </div>
                    <div class="datatable-action-buttons">
                <x-asset-document.helper_book></x-asset-document.helper_book>
                <x-asset-document.download></x-asset-document.download>
                <x-asset-document.upload></x-asset-document.upload>
                <div class="datatable-filter-icon btn-modern-rounded" data-bs-toggle="modal" data-bs-target="#filterDrawer">
                    <i class="fas fa-filter"></i>
                </div>
            </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th rowspan="2" class="align-middle">#</th>
                    <th rowspan="2" class="align-middle">Foto</th>
                    <th rowspan="2" class="align-middle">Kode QR</th>
                    <th rowspan="2" class="align-middle">Nama Barang</th>
                    <th rowspan="2" class="align-middle">Kode Barang</th>
                    <th rowspan="2" class="align-middle">Kode Register / SN</th>
                    <th rowspan="2" class="align-middle">Ruangan / Spesifik</th>
                    <th colspan="5" class="text-center">Dokumen BAST</th>
                </tr>
                <tr>
                    <th>Pengadaaan</th>
                    <th>Penempatan</th>
                    <th>Pembayaran</th>
                    <th>Mutasi</th>
                    <th>Aset Rusak</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Filter Options</label>
                    <select name="filter_type" id="filter_type" class="form-select form-control">
                        <option value="">Semua Dokumen</option>
                        <option value="CHECKLIST">Pengadaan</option>
                        <option value="PENEMPATAN">Penempatan</option>
                        <option value="PEMBAYARAN">Pembayaran</option>
                        <option value="MUTASI">Mutasi</option>
                        <option value="RUSAK">Aset Rusak</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="clearFilterBtn">Clear Filter</button>
                <button type="button" class="btn btn-primary" id="applyFilterBtn">Apply Filter</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-document-preview">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Preview Dokumen</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body" id="list-doc">
                <!-- <div class="row mb-3">
                    <iframe src="" frameborder="0" height="500px" width="100%" id="frame-doc"></iframe>
                </div> -->

                <!-- <div class="row" id="row-image">
                </div> -->
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-img-preview">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Preview Foto</h4>
            </div>
            <div class="modal-body text-center">
                <img src="" class="img-asset-preview img-fluid" alt="">
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal QR Code -->
<div class="modal fade" id="modal-qr-code" tabindex="-1" aria-labelledby="modal-qr-code-label" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-qr-code-label">QR Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <div id="qr-code-image" class="mb-3"></div>
                    <label class="form-label fw-bold">Kode QR:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="qr-code-text" readonly>
                        <button class="btn btn-outline-secondary" type="button" id="copy-qr-code">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="{{ asset('/js/app/asset-management/assetDocument.js') }}"></script>
@endpush