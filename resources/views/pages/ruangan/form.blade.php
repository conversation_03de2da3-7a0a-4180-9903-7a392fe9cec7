@extends("layouts.app")
@push("style")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="{{ $action }}" method="post" id="formdata">
            @method($method)
            @csrf
            <input type="hidden" name="add" id="add" value="0">

            <div class="form-group mb-3">
                <label for="gedung" class="form-label">Nama Gedung</label>
                <select name="gedung" id="gedung" class="form-select">
                    <option value="">-- Pilih <PERSON>ed<PERSON> --</option>
                    @foreach ($gedung as $gd)
                    <option value="{{ $gd->id }}" {{ old('gedung', $ruangan->gedung_id) == $gd->id ? "selected" : "" }}>{{ $gd->nama_gedung }}</option>
                    @endforeach
                </select>

                @error('gedung')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="nama_ruangan" class="form-label">Nama Ruangan</label>
                <input type="text" class="form-control" id="nama_ruangan" name="nama_ruangan" value="{{ old('nama_ruangan', $ruangan->nama_ruangan) }}">

                @error('nama_ruangan')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="kode_ruangan" class="form-label">Kode Ruangan</label>
                <input type="text" class="form-control" id="kode_ruangan" name="kode_ruangan" value="{{ old('kode_ruangan', $ruangan->kode_ruangan) }}">

                @error('kode_ruangan')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i> Simpan & Tambah</button>
                <a href="{{ route('kategori.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script>
    $("#btn-save").on("click", function() {
        $("#formdata").submit();
    });

    $("#btn-save-add").on("click", function() {
        $("#add").val(1);
        $("#formdata").submit();
    });
</script>
@endpush
