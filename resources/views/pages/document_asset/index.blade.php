@extends('layouts.app')
@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
        rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@section('content')
    <div class="panel panel-inverse">
        <div class="panel-heading">
            <h4 class="panel-title">{{ $title ?? 'Blank page' }}</h4>
        </div>
        <div class="panel-body">
            <div class="d-flex justify-content-end mb-3">
                <a href="#modal-dialog-download" class="btn btn-danger me-1" data-bs-toggle="modal"><i
                        class="fas fa-download me-1"></i>Download Document</a>
                <a href="#modal-dialog-upload" class="btn btn-primary" data-bs-toggle="modal"><i
                        class="fas fa-upload me-1"></i>Upload Document</a>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped w-100" id="datatable">
                    <thead>
                        <tr>
                            <th rowspan="2" class="align-middle">
                                #
                            </th>
                            <th rowspan="2" class="align-middle">QR Code</th>
                            <th rowspan="2" class="align-middle">Tag RFID</th>
                            <th rowspan="2" class="align-middle">Nama Barang</th>
                            <th rowspan="2" class="align-middle">Kode Barang</th>
                            <th rowspan="2" class="align-middle">Kode Lokasi</th>
                            <th colspan="5" class="text-center align-middle">Dokumen BAST</th>
                        </tr>
                        <tr>
                            <th>Checklist</th>
                            <th>Penempatan</th>
                            <th>Pembayaran</th>
                            <th>Mutasi</th>
                            <th>Aset Rusak</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- #modal-dialog -->
    <div class="modal fade" id="modal-dialog-upload">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Upload Document BAST</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <form action="" id="form-upload">
                        <div class="form-group mb-3">
                            <label for="type" class="form-label">Type</label>
                            <select name="type" id="type" class="form-control">
                                <option value="penempatan">BAST Penempatan</option>
                                <option value="mutasi">BAST Mutasi</option>
                                <option value="pembayaran">BAST Pembayaran</option>
                            </select>

                            <span class="d-block text-danger" id="error_type"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label for="file_docs" class="form-label">File Dokumen</label>
                            <input type="file" name="file_docs" id="file_docs" class="form-control">

                            <span class="d-block text-danger" id="error_file_docs"></span>
                        </div>

                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Kode Barang</th>
                                    <th>Nama Barang</th>
                                    <th>QR Code</th>
                                    <th>Register Code</th>
                                    <th>Action</th>
                                </tr>
                            </thead>

                            <tbody id="target-upload">
                                <tr>
                                    <td>1</td>
                                    <td>
                                        <select name="kode_barang[]" id=""
                                            class="form-select kode-barang-upload"></select>
                                    </td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>
                                        <a href="javascript:;" class="btn btn-danger btn-sm btn-remove"><i
                                                class="fas fa-times"></i></a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <button href="javascript:;" class="btn btn-primary" id="btn-upload">Upload</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-dialog-download">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Download Document BAST</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('document-bast.download.document') }}" method="post" id="form-download">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="type" class="form-label">Type</label>
                            <select name="type" id="type" class="form-control">
                                <option value="penempatan">BAST Penempatan</option>
                                <option value="mutasi">BAST Mutasi</option>
                                <option value="rusak">BAST Rusak</option>
                            </select>
                        </div>

                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Kode Barang</th>
                                    <th>Nama Barang</th>
                                    <th>QR Code</th>
                                    <th>Register Code</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="target-download">
                                <tr>
                                    <td>1</td>
                                    <td>
                                        <select name="kode_barang[]" id=""
                                            class="form-select kode-barang"></select>
                                    </td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>
                                        <a href="javascript:;" class="btn btn-danger btn-sm btn-remove"><i
                                                class="fas fa-times"></i></a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <button href="javascript:;" class="btn btn-danger" id="btn-download">Download</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-detail">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Dokumen BAST</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <iframe src="" frameborder="0" height="500px" width="100%" id="frame-detail"></iframe>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                responsive: true,
                ajax: "{{ route('document-bast.list') }}",
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'qr_code',
                        name: 'qr_code'
                    },
                    {
                        data: 'tag_rfid',
                        name: 'tag_rfid'
                    },
                    {
                        data: 'nama_barang',
                        name: 'nama_barang'
                    },
                    {
                        data: 'kode_barang',
                        name: 'kode_barang'
                    },
                    {
                        data: 'nama_ruangan',
                        name: 'nama_ruangan'
                    },
                    {
                        data: 'checklist',
                        name: 'checklist'
                    },
                    {
                        data: 'penempatan',
                        name: 'penempatan'
                    },
                    {
                        data: 'checklist',
                        name: 'checklist'
                    },
                    {
                        data: 'checklist',
                        name: 'checklist'
                    },
                    {
                        data: 'checklist',
                        name: 'checklist'
                    },
                ],
            });
        });

        $("#btn-import").click(function(e) {
            e.preventDefault();
            var $btn = $(this);
            $btn.prop('disabled', true).empty().append(
                `<i class="fas fa-circle-notch fa-spin me-1"></i> Uploading...`);

            var formImport = new FormData($("#form-upload")[0]);
            $("#error_type").text("");
            $("#error_file_docs").text("");
            $("#error_file_import").text("");

            $.ajax({
                url: "{{ route('document-bast.upload') }}",
                type: "POST",
                data: formImport,
                contentType: false,
                processData: false,
                success: function(response) {
                    successMessage(response.message);
                    $("#modal-dialog").modal("hide");
                    location.reload();
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $.each(errors, (function(i, error) {
                            $("#" + i).addClass("is-invalid");
                            $("#error_" + i).text(error[0]);
                        }))
                    } else {
                        errorMessage(xhr.responseJSON.message);
                    }
                },
                complete: function() {
                    $btn.prop('disabled', false).empty().append("Upload");
                }
            });
        });

        $("#datatable").on("click", ".btn-detail", function() {
            let url = $(this).data("url");

            $("#frame-detail").attr("src", url);
        });

        function initializeSelect2Download() {
            $(".kode-barang").select2({
                ajax: {
                    url: "{{ route('asset.list.register.asset') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page || 1
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: $.map(data.data, function(item) {
                                return {
                                    id: item.id,
                                    text: item.barang.kode_barang + ' - ' + item.barang.nama_barang +
                                        " - " + item.register_code
                                };
                            }),
                            pagination: {
                                more: data.current_page < data.last_page
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Pilih Kode Barang',
                minimumInputLength: 0,
                allowClear: true,
                width: '100%',
                dropdownParent: $('#modal-dialog-download')
            });
        }

        initializeSelect2Download();

        function initializeSelect2Upload() {
            $(".kode-barang-upload").select2({
                ajax: {
                    url: "{{ route('asset.list.register.asset') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page || 1
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: $.map(data.data, function(item) {
                                return {
                                    id: item.id,
                                    text: item.barang.kode_barang + ' - ' + item.barang.nama_barang +
                                        " - " + item.register_code
                                };
                            }),
                            pagination: {
                                more: data.current_page < data.last_page
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Pilih Kode Barang',
                minimumInputLength: 0,
                allowClear: true,
                width: '100%',
                dropdownParent: $('#modal-dialog-upload')
            });
        }

        initializeSelect2Upload();

        $('#target-download').on('select2:select', '.kode-barang', function(e) {
            const selectedCount = $('#target-download tr').length;
            const selected = $(this).val();
            const currentRow = $(this).closest('tr'); // Get the current row

            $.ajax({
                url: "{{ route('document-bast.asset.detail') }}",
                method: 'POST',
                data: {
                    id: selected
                },
                success: function(response) {
                    currentRow.find('td:eq(2)').text(response.nama_barang);
                    currentRow.find('td:eq(3)').text(response.qr_code);
                    currentRow.find('td:eq(4)').text(response.register_code);

                    // Check if the current row is the last row
                    if (currentRow.index() === selectedCount - 1) {
                        const newRow = `
                    <tr>
                        <td></td>
                        <td>
                            <select name="kode_barang[]" class="form-select kode-barang"></select>
                        </td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>
                            <a href="javascript:;" class="btn btn-danger btn-sm btn-remove"><i class="fas fa-times"></i></a>
                        </td>
                    </tr>
                `;
                        $('#target-download').append(newRow);
                        initializeSelect2('.kode-barang'); // Reinitialize Select2 for the new row
                        updateRowNumbers(); // Update row numbers
                    }
                }
            });
        });

        $('#target-download').on('click', '.btn-remove', function() {
            $(this).closest('tr').remove();
            updateRowNumbers();
        });

        $('#target-upload').on('select2:select', '.kode-barang-upload', function(e) {
            const selectedCount = $('#target-upload tr').length;
            const selected = $(this).val();
            const currentRow = $(this).closest('tr'); // Get the current row

            $.ajax({
                url: "{{ route('document-bast.asset.detail') }}",
                method: 'POST',
                data: {
                    id: selected
                },
                success: function(response) {
                    currentRow.find('td:eq(2)').text(response.nama_barang);
                    currentRow.find('td:eq(3)').text(response.qr_code);
                    currentRow.find('td:eq(4)').text(response.register_code);

                    // Check if the current row is the last row
                    if (currentRow.index() === selectedCount - 1) {
                        const newRow = `
                    <tr>
                        <td></td>
                        <td>
                            <select name="kode_barang[]" class="form-select kode-barang-upload"></select>
                        </td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>
                            <a href="javascript:;" class="btn btn-danger btn-sm btn-remove"><i class="fas fa-times"></i></a>
                        </td>
                    </tr>
                `;
                        $('#target-upload').append(newRow);
                        initializeSelect2Upload();
                        updateRowNumbers();
                    }
                }
            });
        });

        $('#target-upload').on('click', '.btn-remove', function() {
            $(this).closest('tr').remove();
            updateRowNumbers();
        });

        function updateRowNumbers() {
            $('#target-download tr').each(function(index) {
                $(this).find('td:first').text(index + 1);
            });
        }

        $("#btn-download").click(function(e) {
            e.preventDefault();
            $("#form-download").submit();
        });
    </script>
@endpush
