@extends("layouts.app")
@push("style")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="{{ $action }}" method="post" id="formdata">
            @method($method)
            @csrf
            <input type="hidden" name="add" id="add" value="0">

            <div class="form-group mb-3">
                <label for="nama_kategori" class="form-label">Nama <PERSON></label>
                <input type="text" class="form-control" id="nama_kategori" name="nama_kategori" value="{{ old('nama_kategori', $kategori->nama_kategori) }}">

                @error('nama_kategori')
                <span class="d-block text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i> Simpan</button>
                <button type="button" class="btn btn-info" id="btn-save-add"><i class="fas fa-save me-1"></i> Simpan & Tambah</button>
                <a href="{{ route('kategori.index') }}" class="btn btn-outline-danger"><i class="fas fa-undo me-1"></i>Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script>
    $("#btn-save").on("click", function() {
        $("#formdata").submit();
    });

    $("#btn-save-add").on("click", function() {
        $("#add").val(1);
        $("#formdata").submit();
    });
</script>
@endpush