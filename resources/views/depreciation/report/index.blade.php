@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_report_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
    @include("menu.depreciation")
@endpush

@section("content")
    <div class="report-card">
        <div class="report-header">
            <h4>{{ $title ?? "Laporan Depres<PERSON>i" }}</h4>
        </div>

        <div class="report-body">
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label><i>Ruangan</i></label>
                        <select style="width: 100%" name="ruangan" id="ruangan" class="form-select room"></select>
                    </div>

                    <div class="filter-group">
                        <label><i>Barang</i></label>
                        <select style="width: 100%" name="item_id" id="item_id" class="form-select item_id"></select>
                    </div>

                    <div class="filter-group">
                        <label><i>Tahun Perolehan</i></label>
                        <select id="yearpicker" name="yearpicker" class="form-select">
                            <option value="">Semua Tahun</option>
                            @php
                                $currentYear = date('Y');
                                $startYear = $currentYear - 10;
                                $endYear = $currentYear + 1;
                            @endphp
                            @for ($year = $startYear; $year <= $endYear; $year++)
                                <option value="{{ $year }}">{{ $year }}</option>
                            @endfor
                        </select>
                    </div>

                    <div class="filter-group">
                        <button type="button" id="btn-filter" class="btn btn-show-data">
                            <i class="fas fa-search me-2"></i>
                            Tampilkan Data
                        </button>
                    </div>
                </div>
            </div>

            <div class="target-report" id="target-report">
                <!-- Empty State - akan ditampilkan saat awal -->
                <div class="empty-state" id="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="empty-state-title">
                        Mulai Analisis Data Depresiasi
                    </div>
                    <div class="empty-state-description">
                        Pilih ruangan, barang, dan tahun perolehan yang ingin Anda analisis, kemudian klik "Tampilkan Data" untuk melihat
                        laporan detail depresiasi.
                    </div>

                    <div class="empty-state-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">Pilih Ruangan</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">Pilih Barang</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">Pilih Tahun Perolehan</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">Klik Tampilkan Data</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/depreciation/report.js') }}"></script>

    <script>
        // Hide empty state when data is loaded
        $(document).ready(function() {
            $('#btn-filter').on('click', function() {
                $('#empty-state').hide();
            });
        });
    </script>
@endpush
