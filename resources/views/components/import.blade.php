<a href="#modal-dialog" class="btn btn-success me-1" data-bs-toggle="modal"><i class="fas fa-upload me-1"></i>{{ $label }}</a>
<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{ $label }}</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form action="{{ $route }}" method="post" enctype="multipart/form-data" id="form-import">
                    <div class="form-group mb-3">
                        <label for="file" class="form-label">File</label>
                        <input type="file" name="file" id="file" class="form-control">

                        <div class="d-block text-danger" id="error-file"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                <button type="button" class="btn btn-success" id="btn-import">Import</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $("#btn-import").click(function(e) {
            e.preventDefault();
            var $btn = $(this);
            $btn.prop('disabled', true).empty().append(`<i class="fas fa-circle-notch fa-spin me-1"></i> Importing...`);

            var formImport = new FormData($("#form-import")[0]);
            $("#error-file").text("");

            $.ajax({
                url: "{{ $route }}",
                type: "POST",
                data: formImport,
                contentType: false,
                processData: false,
                success: function(response) {
                    successMessage(response.message);
                    $("#modal-dialog").modal("hide");
                    location.reload();
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        if (errors.file) {
                            $("#error-file").text(errors.file[0]); // Show validation error for file
                        }
                    } else {
                        errorMessage(xhr.responseJSON.message);
                    }
                },
                complete: function() {
                    $btn.prop('disabled', false).empty().append("Import");
                }
            });
        });
    });
</script>