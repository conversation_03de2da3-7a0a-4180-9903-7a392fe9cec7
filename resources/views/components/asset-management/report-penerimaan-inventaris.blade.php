<i style="float: right">Laporan Dibuat: {{date('Y-m-d : H:i:s')}}</i><br>
<div class="text-center mb-4">
    <h4>DAFTAR LAPORAN INVENTARIS ANGGARAN TAHUN {{ date('Y', strtotime($params['end'])) }}</h4>
    <h4>{{ config('app.report_header_hospital') }}</h4>
    <h4>PROVINSI {{ strtoupper(config('app.hospital_province')) }}</h4>
    <h4>PERIODE: {{ $params['start'] }} - {{ $params['end'] }}</h4>
    @php
        $jenisText = request('jenis_inventaris', 'all') === 'all' ? 'SEMUA JENIS INVENTARIS' :
                    (request('jenis_inventaris') === 'intrakomptabel' ? 'INTRAKOMPTABEL' : 'EKSTRAKOMPTABEL');
    @endphp
    <h4>{{ $jenisText }}</h4>
</div>

<div class="mb-3">
    <div>Provinsi : {{ strtoupper(config('app.hospital_province')) }}</div>
    <div>Kabupaten/Kota : {{ strtoupper(config('app.hospital_city')) }}</div>
</div>

<div class="table-responsive">
    <table class="table table-bordered table-striped mt-5">
        <thead>
        <tr>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Kode Barang</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Nama Barang</th>
            <th colspan="2" style="text-align: center;">Spesifikasi</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Jumlah Barang</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Satuan Barang</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Harga Satuan (Rp)</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Total Nilai Barang (Rp)</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Total Biaya Atribut (Rp)</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Nilai Perolehan Barang (Rp)</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Harga Satuan Perolehan (Rp)</th>
            <th colspan="4" style="text-align: center;">Sub Kegiatan dan Rekening Anggaran Belanja Daerah Atas Pengadaan Barang</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Tanggal Perolehan</th>
            <th colspan="3" style="text-align: center;">Dokumen Sumber Perolehan</th>
            <th rowspan="2" style="vertical-align: middle; text-align: center;">Keterangan</th>
        </tr>
        <tr>
            <th>Nama Barang</th>
            <th>Merek/Type</th>
            <th>Kode Sub Kegiatan</th>
            <th>Nama Sub Kegiatan</th>
            <th>Kode Rekening</th>
            <th>Uraian Belanja</th>
            <th>Bentuk</th>
            <th>Nama Penyedia</th>
            <th>Nomor</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($data as $val)
            <tr>
                <td>{{ $val->item->item_code ?? '-' }}</td>
                <td>{{ $val->item->item_name ?? '-' }}</td>
                <td>{{ $val->item->item_name ?? '-' }}</td>
                <td>{{ $val->brand ?? '-' }}</td>
                <td>{{ $val->quantity ?? '-' }}</td>
                <td>{{ $val->uom_name ?? '-' }}</td>
                <td style="text-align: right">{{ number_format($val->unit_price, 0, ',', '.') }}</td>
                <td style="text-align: right">{{ number_format(($val->quantity * $val->unit_price), 0, ',', '.') }}</td>
                <td style="text-align: right"></td>
                <td style="text-align: right">{{ number_format(($val->quantity * $val->unit_price), 0, ',', '.') }}</td>
                <td style="text-align: right">{{ number_format($val->unit_price, 0, ',', '.') }}</td>
                <td>{{ $val->sub_activity_code  ?? '-' }}</td>
                <td>{{ $val->sub_activity_name  ?? '-' }}</td>
                <td>{{ $val->spending_account_code  ?? '-' }}</td>
                <td>{{ $val->purchasing_account ?? '-' }}</td>
                <td>{{ $val->received_date ?? '-' }}</td>
                <td>{{ $val->contract_form ?? '-' }}</td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
