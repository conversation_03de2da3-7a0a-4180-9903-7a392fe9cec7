<i style="float: right">Laporan Dibuat: {{date('Y-m-d : H:i:s')}}</i><br>
<h3 style="text-align: center" class="text-uppercase">
    KARTU INVENTARIS RUANGAN (KIR)<br>
</h3>

<h5 class="d-block text-uppercase">
    <span class="d-block"><PERSON><PERSON><PERSON> : {{ config('app.hospital_province') }}</span>
    <span class="d-block">Kotamadia : {{ config('app.hospital_city') }}</span>
    <span class="d-block">satuan kerja : {{ config('app.report_header_hospital') }}</span>
    <span class="d-block">Ruangan : {{ $room->room_name ?? "Semua Ruangan" }}</span>
</h5>

<div class="table-responsive">
    <table class="table table-bordered table-striped mt-5">
        <thead>
        <tr>
            <th rowspan="2" class="align-middle text-center">No. Urut</th>
            <th rowspan="2" class="align-middle text-center"><PERSON><PERSON> / <PERSON></th>
            <th rowspan="2" class="align-middle text-center">Merk / Model</th>
            <th rowspan="2" class="align-middle text-center">No. Seri Pabrikan</th>
            <th rowspan="2" class="align-middle text-center">Ukuran</th>
            <th rowspan="2" class="align-middle text-center">Bahan</th>
            <th rowspan="2" class="align-middle text-center">Tahun Pembuatan / Pembelian</th>
            <th rowspan="2" width="5" class="align-middle text-center">No. Kode Barang</th>
            <th rowspan="2" class="align-middle text-center">Register</th>
            <th rowspan="2" class="align-middle text-center">Jumlah Barang</th>
            <th rowspan="2" class="align-middle text-center">Harga Beli Perolehan (Rp.)</th>
            <th colspan="3" class="align-middle text-center">Keadaan Barang</th>
            <th rowspan="2" class="align-middle text-center">Keterangan Mutasi/DLL</th>
        </tr>
        <tr>
            <th>B</th>
            <th>RR</th>
            <th>RB</th>
        </tr>
        <tr>
            <th class="text-center">1</th>
            <th class="text-center">2</th>
            <th class="text-center">3</th>
            <th class="text-center">4</th>
            <th class="text-center">5</th>
            <th class="text-center">6</th>
            <th class="text-center">7</th>
            <th class="text-center">8</th>
            <th class="text-center" colspan="2">9</th>
            <th class="text-center">10</th>
            <th class="text-center">11</th>
            <th class="text-center">12</th>
            <th class="text-center">13</th>
            <th class="text-center">14</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $val)
            <tr>
                <td>{{$loop->iteration}}</td>
                <td>{{ $val->item_name }}</td>
                <td>{{ $val->brand }}</td>
                <td>{{ $val->serialNumbers }}</td>
                <td>{{ $val->size }}</td>
                <td>{{ $val->material }}</td>
                <td>-</td>
                <td>{{ $val->item_code }}</td>
                <td>{{ $val->registerCodes }}</td>
                <td>{{ $val->asset_count }}</td>
                <td>{{ number_format($val->total_price, 0, ',', '.')}}</td>
                <td>{{ $val->condition == "BAIK" ? "✅" : "-" }}</td>
                <td>{{ $val->condition == "RUSAK RINGAN" ? "✅" : "-" }}</td>
                <td>{{ $val->condition == "RUSAK BERAT" ? "✅" : "-" }}</td>
                <td>{{ $val->description }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>

