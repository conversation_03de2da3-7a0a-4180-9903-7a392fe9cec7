<i style="float: right">Laporan Dibuat: {{date('Y-m-d : H:i:s')}}</i><br>
<h3 style="text-align: center" class="text-uppercase">
    laporan daftar barang <br>
    aset peralatan dan mesin <br>
</h3>
<h3 class="text-center">
    {{ config('app.report_header_hospital') }} <br>
    Tahun : {{ now()->year }}
</h3>

<style>
    .table-wrapper {
        position: relative;
        max-height: 50vh;
        overflow: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .table-wrapper table {
        margin-bottom: 0;
    }
    
    .table-wrapper thead th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 1;
    }
    
    .table-wrapper tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }

    /* Lebar scroll custom */
    .table-wrapper::-webkit-scrollbar {
        width: 12px;
        height: 12px;
    }

    /* Track scroll custom */
    .table-wrapper::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    /* Handle scroll custom */
    .table-wrapper::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }

    /* Handle on hover */
    .table-wrapper::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Ukuran kolom yang tetap */
    .table th {
        white-space: nowrap;
        min-width: 100px;
    }
    
    .table td {
        white-space: nowrap;
        padding: 8px;
    }

    /* Kolom nomor lebih kecil */
    .table th:first-child,
    .table td:first-child {
        min-width: 50px;
    }
</style>

<div class="table-wrapper">
    <table class="table table-bordered table-striped" style="min-width: 2000px;">
        <thead>
        <tr>
            <th rowspan="2" class="align-middle text-center">No</th>
            <th colspan="2" class="align-middle text-center">Kode</th>
            <th rowspan="2" class="align-middle text-center">Nama Barang</th>
            <th rowspan="2" class="align-middle text-center">Merk/Type</th>
            <th rowspan="2" class="align-middle text-center">Bahan</th>
            <th rowspan="2" class="align-middle text-center">Kondisi</th>
            <th rowspan="2" class="align-middle text-center">Asal Perolehan</th>
            <th rowspan="2" class="align-middle text-center">Tangal Perolehan</th>
            <th colspan="4" class="align-middle text-center">Nomor</th>
            <th rowspan="2" class="align-middle text-center">UEB</th>
            <th rowspan="2" class="align-middle text-center">Harga Perolehan</th>
            <th rowspan="2" class="align-middle text-center">Akumulasi Penyusutan Sebelumnya</th>
            <th rowspan="2" class="align-middle text-center">Beban Penyusutan</th>
            <th rowspan="2" class="align-middle text-center">Akumulasi Penyusutan</th>
            <th rowspan="2" class="align-middle text-center">Sisa UEB</th>
            <th rowspan="2" class="align-middle text-center">Nilai Buku</th>
            <th rowspan="2" class="align-middle text-center">QR Code</th>
            <th rowspan="2" class="align-middle text-center">Merk</th>
            <th rowspan="2" class="align-middle text-center">Tipe/Model</th>
            <th rowspan="2" class="align-middle text-center">No. AKD/AKL</th>
            <th rowspan="2" class="align-middle text-center">No. Kontrak/SP</th>
            <th rowspan="2" class="align-middle text-center">Kode Rekening Belanja</th>
            <th rowspan="2" class="align-middle text-center">No. BA Pembayaran</th>
            <th rowspan="2" class="align-middle text-center">Uraian Rekening Belanja</th>
            <th rowspan="2" class="align-middle text-center">Kelistrikan</th>
        </tr>
        <tr>
            <th class="align-middle text-center">Barang</th>
            <th class="align-middle text-center">Register</th>
            <th class="align-middle text-center">Rangka</th>
            <th class="align-middle text-center">Mesin</th>
            <th class="align-middle text-center">Polisi</th>
            <th class="align-middle text-center">BPKB</th>
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $val)
            <tr>
                <td>{{$loop->iteration}}</td>
                <td>{{ $val->item->item_code }}</td>
                <td>{{ $val->register_code }}</td>
                <td>{{ $val->item->item_name }}</td>
                <td>{{ $val->assetEntry->brand }}</td>
                <td>{{ $val->assetEntry->material }}</td>
                <td>{{ $val->asset_condition }}</td>
                <td>{{ $val->assetEntry->source_supply }}</td>
                <td>{{ $val->assetEntry->received_date }}</td>
                <td>{{ $val->chassis_number }}</td>
                <td>{{ $val->engine_number }}</td>
                <td>{{ $val->license_plate_number }}</td>
                <td>{{ $val->bpkb_number }}</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td>{{ $val->qr_code }}</td>
                <td>{{ $val->assetEntry->brand }}</td>
                <td>{{ $val->assetEntry->type }}</td>
                <td>{{ $val->assetEntry->akd_number }}</td>
                <td>{{ $val->assetEntry->bast_contract_number }}</td>
                <td>{{ $val->assetEntry->spending_account_code }}</td>
                <td>{{ $val->assetEntry->bast_payment_number }}</td>
                <td>{{ $val->assetEntry->purchasing_account }}</td>
                <td>{{ $val->electricity }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
