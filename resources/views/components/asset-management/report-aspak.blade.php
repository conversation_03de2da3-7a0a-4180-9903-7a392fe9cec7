<i style="float: right">Laporan Dibuat: {{ date('Y-m-d : H:i:s') }}</i><br>
<h3 style="text-align: center" class="text-uppercase">
    LAPORAN PENERIMAAN, UJI FUNGSI DAN DISTRIBUSI<br>
    DI UNIT PELAYANAN<br>
    TAHUN {{ Carbon\Carbon::parse($params['end'])->format('Y') }}
</h3>
<h5 class="text-center">
    Periode: {{ Carbon\Carbon::parse($params['start'])->format('d F Y') }} - {{ Carbon\Carbon::parse($params['end'])->format('d F Y') }}
</h5>

<div class="table-responsive">
    <table class="table table-bordered table-striped mt-5">
        <thead>
            <tr>
                <th class="align-middle text-center" rowspan="2">No</th>
                <th class="align-middle text-center" rowspan="2"><PERSON><PERSON></th>
                <th class="align-middle text-center" rowspan="2">Nomor Serial Number</th>
                <th class="align-middle text-center" rowspan="2">Nomor AKD / AKL</th>
                <th class="align-middle text-center" rowspan="2">Merek</th>
                <th class="align-middle text-center" rowspan="2">Model / Type</th>
                <th class="align-middle text-center" rowspan="2">No. Foto Alkes</th>
                <th class="align-middle text-center" rowspan="2">Distributor</th>
                <th class="align-middle text-center" rowspan="2">Tahun Pembelian</th>
                <th class="align-middle text-center" rowspan="2">Asal Perolehan</th>
                <th class="align-middle text-center" rowspan="2">Harga Satuan (Rp)</th>
                <th class="align-middle text-center" colspan="3">Kondisi Alat</th>
                <th class="align-middle text-center" rowspan="2">Penempatan/Lokasi Barang</th>
                <th class="align-middle text-center" rowspan="2">Kode Barang</th>
                <th class="align-middle text-center" rowspan="2">Kode Register</th>
                <th class="align-middle text-center" rowspan="2">Kelistrikan</th>
                <th class="align-middle text-center" rowspan="2">Keterangan</th>
            </tr>
            <tr>
                <th class="align-middle text-center">B</th>
                <th class="align-middle text-center">RR</th>
                <th class="align-middle text-center">RB</th>
            </tr>
        </thead>

        <tbody>
            @foreach ($data as $key => $asset)
                <tr>
                    <td>{{ $key + 1 }}</td>
                    <td>{{ $asset->item->item_name ?? '-'}}</td>
                    <td>{{ $asset->serial_number ?? '-'}}</td>
                    <td>{{ $asset->assetEntry->akd_number ?? '-' }}</td>
                    <td>{{ $asset->assetEntry->brand ?? '-'}}</td>
                    <td>{{ $asset->assetEntry->model_type ?? 'IPS'}}</td>
                    <td>
                        @if($asset->last_media_path)
                            <img src="{{ asset('/storage/' . $asset->last_media_path) }}" width="70px" height="70px" class="img-fluid text-center">
                        @else
                            <span class="text-danger">Belum ada foto</span>
                        @endif
                    </td>
                    <td>{{ $asset->assetEntry->distributor->distributor_name ?? '-' }}</td>
                    <td>{{ Carbon\Carbon::parse($asset->payment_date)->format('Y') }}</td>
                    <td>{{ $asset->assetEntry->source_supply ?? '-' }}</td>
                    <td>Rp {{ number_format($asset->unit_price ?? 0, 0, ',', '.') }}</td>
                    <td class="align-middle text-center">{{ $asset->assetEntry->condition == 'BAIK' ? '√' : '-' }}</td>
                    <td class="align-middle text-center">{{ $asset->assetEntry->condition == 'RUSAK_RINGAN' ? '√' : '-' }}</td>
                    <td class="align-middle text-center">{{ $asset->assetEntry->condition == 'RUSAK_BERAT' ? '√' : '-' }}</td>
                    <td>{{ $asset->room->room_name ?? '-' }}</td>
                    <td>{{ $asset->item->item_code ?? '-'}}</td>
                    <td>{{ str_pad(($key + 1), 4, '0', STR_PAD_LEFT) }}</td>
                    <td>{{ $asset->electricity ?? '-' }}</td>
                    <td></td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
