<a href="#modal-dialog" class="btn btn-primary" data-bs-toggle="modal" id="btn-add"><i class="fas fa-plus-circle me-1"></i>
    <PERSON>bah Kategori Pemeliharaan</a>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Kategori Pemeliharaan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
            <form action="{{ route('master-data.maintenance-category.store') }}" method="post" id="formdata">
                    @csrf

                    <div class="form-group mb-3">
                        <label class="form-label"><PERSON><PERSON></label>
                        <input type="text" class="form-control" name="maintenance_category_name" id="maintenance_category_name">
                        <div class="d-block text-danger error-message" id="error_maintenance_category_name"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Aktif</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="active" id="active" value="1">
                            <label class="form-check-label" for="active">Aktif</label>
                        </div>
                        <div class="d-block text-danger error-message" id="error_active"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
            </div>
        </div>
    </div>
</div>
