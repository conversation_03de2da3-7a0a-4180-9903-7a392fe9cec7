<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Ruangan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('master-data.room.store') }}" method="post" id="formdata">
                    <div class="form-group mb-3">
                        <label class="form-label">Kode <PERSON>uang<PERSON></label>
                        <input type="text" class="form-control" name="kode_ruangan" id="kode_ruangan">

                        <div class="d-block text-danger error-message" id="error_kode_ruangan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label"><PERSON><PERSON><PERSON></label>
                        <select id="kategori-ruangan" class="form-select" name="kategori_ruangan">
                        </select>

                        <div class="d-block text-danger error-message" id="error_kategori_ruangan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Nama Ruangan</label>
                        <input type="text" class="form-control" name="nama_ruangan" id="nama_ruangan">

                        <div class="d-block text-danger error-message" id="error_nama_ruangan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Nama Gedung</label>
                        <input type="text" class="form-control" name="nama_gedung" id="nama_gedung">

                        <div class="d-block text-danger error-message" id="error_nama_gedung"></div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Scanner ID</label>
                        <input type="text" class="form-control" name="device_id" id="device_id">

                        <div class="d-block text-danger error-message" id="error_device_id"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Penanggung Jawab</label>
                        <select name="penanggung_jawab" id="penanggung_jawab" class="form-select penanggung_jawab">
                            <option value="">-- Pilih Penanggung Jawab --</option>

                        </select>

                        <div class="d-block text-danger error-message" id="error_penanggung_jawab"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Divisi</label>
                        <select id="division_id" class="form-select" name="division_id">
                            <option value="">-- Pilih Divisi --</option>
                            @foreach(\App\Models\Division::where('active', 1)->get() as $division)
                                <option value="{{ $division->id }}">{{ $division->division_name }}</option>
                            @endforeach
                        </select>
                        <div class="d-block text-danger error-message" id="error_division_id"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal Import Excel -->
<div class="modal fade" id="modal-import-excel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Import Excel Ruangan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <!-- Area untuk menampilkan pesan sukses/error -->
                <div id="import-message-area" class="d-none mb-3">
                    <div id="import-success-message" class="alert alert-success d-none">
                        <i class="fas fa-check-circle me-1"></i>
                        <span id="success-text"></span>
                    </div>
                    <div id="import-error-message" class="alert alert-danger d-none">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <span id="error-text"></span>
                        <div id="error-details" class="mt-3"></div>
                    </div>
                </div>

                <form id="form-import-excel" enctype="multipart/form-data">
                    <div class="form-group mb-3">
                        <label class="form-label">File Excel</label>
                        <input type="file" class="form-control" name="excel_file" id="excel_file" accept=".xlsx,.xls" required>
                        <div class="form-text">Format file yang didukung: .xlsx, .xls</div>
                        <div class="d-block text-danger error-message" id="error_excel_file"></div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>Format Excel yang diperlukan:</h6>
                        <ul class="mb-0">
                            <li>Kolom A: Kode Ruangan</li>
                            <li>Kolom B: Nama Ruangan</li>
                            <li>Kolom C: Nama Gedung</li>
                            <li>Kolom D: Kode Kategori Ruangan</li>
                            <li>Kolom E: Kode Penanggung Jawab</li>
                            <li>Kolom F: ID Divisi</li>
                        </ul>
                        <div class="alert alert-warning mt-2 mb-0">
                            <small><i class="fas fa-info-circle me-1"></i>Catatan: Pastikan Kode Penanggung Jawab, Kode Kategori Ruangan, dan ID Divisi sesuai dengan data yang ada di sistem.</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel-import">Batal</a>
                <a href="{{ route('master-data.room.download-template') }}" target="_blank" class="btn btn-info" id="btn-download-template">
                    <i class="fas fa-download me-1"></i>Download Template
                </a>
                <a href="javascript:;" class="btn btn-success" id="btn-import-submit">
                    <i class="fas fa-upload me-1"></i>Import
                </a>
            </div>
        </div>
    </div>
</div>
