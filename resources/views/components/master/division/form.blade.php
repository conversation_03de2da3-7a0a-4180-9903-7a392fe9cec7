<button class="btn btn-primary" id="btn-add">
    <i class="fas fa-plus me-1"></i>Tambah Bidang
</button>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Bidang</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="{{ route('master-data.division.store') }}" method="post" id="formdata">
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="room_id">Ruangan</label>
                        <select name="room_id" id="room_id" class="form-select room_id">
                            <option value="">-- <PERSON><PERSON><PERSON> --</option>
                        </select>
                        <span class="text-danger error-message" id="error_room_id"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="nama_bidang">Nama Bidang</label>
                        <input type="text" class="form-control" name="nama_bidang" id="nama_bidang">
                        <span class="text-danger error-message" id="error_nama_bidang"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="deskripsi_bidang">Deskripsi</label>
                        <textarea class="form-control" name="deskripsi_bidang" id="deskripsi_bidang" rows="3"></textarea>
                        <span class="text-danger error-message" id="error_deskripsi_bidang"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="status">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="1">Aktif</option>
                            <option value="0">Tidak Aktif</option>
                        </select>
                        <span class="text-danger error-message" id="error_status"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                    <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
                </div>
            </form>
        </div>
    </div>
</div> 