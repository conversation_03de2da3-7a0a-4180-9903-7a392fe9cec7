@if(hasPermissionInGuard("Data UOM - Action"))
<a href="#modal-dialog" class="btn btn-primary" data-bs-toggle="modal" id="btn-add">
    <i class="fas fa-plus-circle me-1"></i> Tambah UOM
</a>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form UOM</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('master-data.uom.store') }}" method="post" id="formdata">
                    @csrf
                    <div class="form-group mb-3">
                        <label class="form-label">Nama UOM</label>
                        <input type="text" class="form-control" name="uom_name" id="uom_name">
                        <div class="d-block text-danger error-message" id="error_uom_name"></div>
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Status</label>
                        <select name="active" id="active" class="form-select">
                            <option value="1">Aktif</option>
                            <option value="0">Tidak Aktif</option>
                        </select>
                        <div class="d-block text-danger error-message" id="error_active"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                <a href="javascript:;" class="btn btn-primary" id="btn-save">
                    <i class="fas fa-save me-1"></i>Simpan
                </a>
            </div>
        </div>
    </div>
</div>
@endif