<button class="btn btn-primary" id="btn-add">
    <i class="fas fa-plus me-1"></i>Tambah
</button>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Program</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="" id="formdata">
                @csrf
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="kategori_program">Kategori Program</label>
                        <select name="kategori_program" id="kategori_program" class="form-select">
                            <option value="PERENCANAAN">PERENCANAAN</option>
                            <option value="PEMELIHARAAN">PEMELIHARAAN</option>
                        </select>
                        <span class="text-danger error-message" id="error_kategori_program"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="kode_program">Kode Program</label>
                        <input type="text" class="form-control" name="kode_program" id="kode_program" placeholder="Kode Program">
                        <span class="text-danger error-message" id="error_kode_program"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="nama_program">Nama Program</label>
                        <input type="text" class="form-control" name="nama_program" id="nama_program" placeholder="Nama Program">
                        <span class="text-danger error-message" id="error_nama_program"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="top_parent">Top Parent</label>
                        <select name="top_parent" id="top_parent" class="form-select top_parent"></select>
                        <span class="text-danger error-message" id="error_top_parent"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="parent_id">Parent ID</label>
                        <select name="parent_id" id="parent_id" class="form-select parent_id"></select>
                        <span class="text-danger error-message" id="error_parent_id"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="tipe_program">Tipe Program</label>
                        <select name="tipe_program" id="tipe_program" class="form-select">
                            <option value="GROUP">GROUP</option>
                            <option value="OUTPUT">OUTPUT</option>
                        </select>
                        <span class="text-danger error-message" id="error_tipe_program"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="asset_type">Tipe Aset</label>
                        <select name="asset_type" id="asset_type" class="form-select">
                            <option value="ASSET">ASSET</option>
                            <option value="LOGISTIC">LOGISTIC</option>
                            <option value="OTHER">OTHER</option>
                        </select>
                        <span class="text-danger error-message" id="error_asset_type"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label for="active">Status</label>
                        <select name="active" id="active" class="form-select">
                            <option value="1">Active</option>
                            <option value="0">Non Active</option>
                        </select>
                        <span class="text-danger error-message" id="error_active"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <a href="javascript:;" class="btn btn-primary" id="btn-save">Submit</a>
                </div>
            </form>
        </div>
    </div>
</div>
