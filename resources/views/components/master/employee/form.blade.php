<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Form Karyawan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('master-data.employee.store') }}" method="post" id="formdata">
                    <div class="form-group mb-3">
                        <label class="form-label"><PERSON><PERSON>ryawan</label>
                        <input type="text" class="form-control" name="kode_karyawan" id="kode_karyawan">
                        <div class="d-block text-danger error-message" id="error_kode_karyawan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label"><PERSON><PERSON></label>
                        <input type="text" class="form-control" name="nama_karyawan" id="nama_karyawan">
                        <div class="d-block text-danger error-message" id="error_nama_karyawan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Golongan Karyawan</label>
                        <input type="text" class="form-control" name="golongan_karyawan" id="golongan_karyawan">
                        <div class="d-block text-danger error-message" id="error_golongan_karyawan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Jabatan Karyawan</label>
                        <input type="text" class="form-control" name="jabatan_karyawan" id="jabatan_karyawan">
                        <div class="d-block text-danger error-message" id="error_jabatan_karyawan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Tipe Karyawan</label>
                        <select name="tipe_karyawan" id="tipe_karyawan" class="form-select form-control">
                            <option value="KARYAWAN">Karyawan</option>
                            <option value="DIREKTUR">Direktur</option>
                            <option value="PIC_PENGURUS_BARANG">PIC Pengurus Barang</option>
                            <option value="PIC_BARANG_RUSAK">PIC Barang Rusak</option>
                            <option value="PIC_PENYIMPANAN_BARANG">PIC Penyimpanan Barang</option>
                            <option value="KETUA_TEAM_ASET">Ketua Team Aset</option>
                            <option value="KEPALA_PERENCANAAN_PROGRAM_ASET">Kepala Perencanaan Program Aset</option>
                        </select>
                        <div class="d-block text-danger error-message" id="error_tipe_karyawan"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Kategori</label>
                        <select name="category" id="category" class="form-select form-control">
                            <option value="EMPLOYEE">Employee</option>
                            <option value="OFFICER">Officer</option>
                            <option value="DEVELOPER">Developer</option>
                        </select>
                        <div class="d-block text-danger error-message" id="error_category"></div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">Role</label>
                        <select name="role_code" id="role_code" class="form-select role_code form-control">
                            <option value="">Pilih Role</option>
                        </select>
                        <div class="d-block text-danger error-message" id="error_role_code"></div>
                    </div>

                    <!-- Field status aktif dihapus karena semua karyawan otomatis aktif -->
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Batal</a>
                <a href="javascript:;" class="btn btn-primary" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal Import Excel -->
<div class="modal fade" id="modal-import-excel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Import Excel Karyawan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <!-- Area untuk menampilkan pesan sukses/error -->
                <div id="import-message-area" class="d-none mb-3">
                    <div id="import-success-message" class="alert alert-success d-none">
                        <i class="fas fa-check-circle me-1"></i>
                        <span id="success-text"></span>
                    </div>
                    <div id="import-error-message" class="alert alert-danger d-none">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <span id="error-text"></span>
                        <div id="error-details" class="mt-3"></div>
                    </div>
                </div>

                <form id="form-import-excel" enctype="multipart/form-data">
                    <div class="form-group mb-3">
                        <label class="form-label">File Excel</label>
                        <input type="file" class="form-control" name="excel_file" id="excel_file" accept=".xlsx,.xls" required>
                        <div class="form-text">Format file yang didukung: .xlsx, .xls</div>
                        <div class="d-block text-danger error-message" id="error_excel_file"></div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>Format Excel yang diperlukan:</h6>
                        <ul class="mb-0">
                            <li>Kolom A: Kode Karyawan</li>
                            <li>Kolom B: Nama Karyawan</li>
                            <li>Kolom C: Golongan Karyawan</li>
                            <li>Kolom D: Jabatan Karyawan</li>
                            <li>Kolom E: Tipe Karyawan</li>
                            <li>Kolom F: Kategori</li>
                            <li>Kolom G: Role Code</li>
                        </ul>
                        <div class="alert alert-warning mt-2 mb-0">
                            <small><i class="fas fa-info-circle me-1"></i>Catatan: Semua karyawan yang diimport akan otomatis berstatus aktif.</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel-import">Batal</a>
                <a href="{{ route('master-data.employee.download-template') }}" target="_blank" class="btn btn-info" id="btn-download-template">
                    <i class="fas fa-download me-1"></i>Download Template
                </a>
                <a href="javascript:;" class="btn btn-success" id="btn-import-submit">
                    <i class="fas fa-upload me-1"></i>Import
                </a>
            </div>
        </div>
    </div>
</div>
