<h3>{{$logistic->logistic_number}}</h3>

<div class="row mt-3">
    <div class="col-md-6 col-12">

    </div>
    <div class="col-md-6 col-12" style="text-align: right">
        <address class="mt-5px mb-5px">
            Tanggal Penyesuaian<br>
            <strong class="text-dark">{{$logistic->logistic_date}}</strong>
        </address>
    </div>
</div>

<div class="table-responsive mt-5">
    <table class="table table-invoice">
        <thead>
        <tr>
            <th class="text-center">Barang</th>
            <th class="text-center">Asset</th>
            <th class="text-center">Satuan</th>
            <th class="text-center">Harga Satuan</th>
            <th class="text-center">Qty</th>
            <th class="text-center">Total</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($logisticDetail as $item)
            <tr>
                <td>
                    <span class="text-dark">{{$item->item_name}}</span><br>
                    <small>{{$item->item_code}}</small>
                </td>
                <td>
                    <span class="text-dark">{{$item->asset_name}}</span><br>
                    <small>{{$item->asset_code}}</small>
                </td>
                <td>{{$item->uom_name}}</td>
                <td class="text-end">{{number_format($item->unit_price,0,",",".")}}</td>
                <td class="text-end">{{number_format($item->quantity,0,",",".")}}</td>
                <td class="text-end">{{number_format($item->total_price,0,",",".")}}</td>
            </tr>
        @endforeach
        </tbody>
        <tfoot>
        <tr>
            <td colspan=4">TOTAL</td>
            <td class="text-end">{{number_format($logistic->total_logistic_quantity,0,",",".")}}</td>
            <td class="text-end">{{number_format($logistic->total_logistic_price,0,",",".")}}</td>
        </tr>
        </tfoot>
    </table>

    <div class="mt-4">
        <strong class="text-dark">Tipe Penyesuaian: </strong><br>
        {{$logistic->logistic_category === 'ADJUSTMENT_EXCESS' ? 'Penambahan' : 'Pengurangan'}}
    </div>
    <div class="mt-4">
        <strong class="text-dark">Catatan: </strong><br>
        {{$logistic->logistic_notes}}
    </div>
    <div class="mt-4">
        <strong class="text-dark">Pembuat: </strong><br>
        {{$logistic->created_by_name}}
    </div>
    @if (isset($logistic->logistic_document_path))
        <div class="mt-4">
            <strong class="text-dark">File Attachment: </strong><br>
            <a href="{{ asset('storage/' . $logistic->logistic_document_path) }}" target="_blank">
                {{ basename($logistic->logistic_document_path) }}
            </a>
        </div>
    @endif
</div>
