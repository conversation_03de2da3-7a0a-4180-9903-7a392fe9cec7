@php
    // Menentukan semester berdasarkan tanggal akhir
    $endDate = \Carbon\Carbon::parse($params['end']);
    $month = $endDate->month;
    $semester = '';

    if ($month <= 3) {
        $semester = 'I';
    } elseif ($month <= 6) {
        $semester = 'II';
    } elseif ($month <= 9) {
        $semester = 'III';
    } else {
        $semester = 'IV';
    }
@endphp

<i style="float: right">Laporan Dibuat: {{ date('Y-m-d : H:i:s') }}</i><br>
<h3 style="text-align: center" class="text-uppercase">
    {{ strtoupper(config('app.report_header_hospital')) }} <br>
    LAPORAN SEMESTER TENTANG PENERIMAAN DAN PENGELUARAN BARANG PAKAI HABIS<br>
    SEMESTER {{ $semester }} TAHUN ANGGARAN {{ date('Y') }}<br>
    PERIODE : {{ $params['start'] . ' - ' . $params['end'] }}
</h3>

<div class="table-responsive">
    <table class="table table-bordered table-striped mt-5">
        <thead>
        <tr>
            <th rowspan="2" class="align-middle text-center">Dari</th>
            <th colspan="3" class="text-center">Penerimaan</th>
            <th rowspan="2" class="align-middle text-center">Banyaknya</th>
            <th rowspan="2" class="align-middle text-center">Nama Barang</th>
            <th rowspan="2" class="align-middle text-center">Harga Satuan</th>
            <th colspan="3" class="text-center">Bukti Penerimaan/BA</th>
            <th colspan="3" class="text-center">Surat Bon</th>
            <th rowspan="2" class="align-middle text-center">Untuk</th>
            <th rowspan="2" class="align-middle text-center">Banyaknya</th>
            <th rowspan="2" class="align-middle text-center">Nama Barang</th>
            <th rowspan="2" class="align-middle text-center">Harga Satuan</th>
            <th rowspan="2" class="align-middle text-center">Jumlah Harga</th>
            <th rowspan="2" class="align-middle text-center">Tanggal Penyerahan</th>
            <th rowspan="2" class="align-middle text-center">Ket</th>
        </tr>
        <tr>
            <th class="text-center">Perjanjian</th>
            <th class="text-center">Nomor</th>
            <th class="text-center">Tanggal</th>
            <th class="text-center">Nomor</th>
            <th class="text-center">Tanggal</th>
            <th class="text-center">Ket</th>
            <th class="text-center">No Urut</th>
            <th class="text-center">Keluar</th>
            <th class="text-center">Tanggal</th>
        </tr>
        </thead>
        <tbody>
        @php
            $maxRows = max(count($dataIncoming), count($data));
        @endphp
        @for ($i = 0; $i < $maxRows; $i++)
            <tr>
                {{-- Data Penerimaan --}}
                <td>{{ $dataIncoming[$i]->distributor_name ?? '' }}</td>
                <td>{{ $dataIncoming[$i]->perjanjian ?? '-' }}</td>
                <td>{{ $dataIncoming[$i]->logistic_number ?? '' }}</td>
                <td>{{ $dataIncoming[$i]->logistic_date ?? '' }}</td>
                <td class="text-right">{{ $dataIncoming[$i]->quantity ?? '' }}</td>
                <td>{{ $dataIncoming[$i]->asset_name ?? '' }}</td>
                <td class="text-right">{{ isset($dataIncoming[$i]->unit_price) ? number_format($dataIncoming[$i]->unit_price, 2) : '' }}</td>
                <td>{{ $dataIncoming[$i]->logistic_number ?? '' }}</td>
                <td>{{ $dataIncoming[$i]->logistic_date ?? '' }}</td>
                <td>-</td>

                {{-- Data Pengeluaran --}}
                <td>{{ $i + 1 }}</td>
                <td>{{ $data[$i]->logistic_number ?? '' }}</td>
                <td>{{ $data[$i]->logistic_date ?? '' }}</td>
                <td>{{ $data[$i]->room_name ?? '' }}</td>
                <td class="text-right">{{ $data[$i]->quantity ?? '' }}</td>
                <td>{{ $data[$i]->asset_name ?? '' }}</td>
                <td class="text-right">{{ isset($data[$i]->unit_price) ? number_format($data[$i]->unit_price, 2) : '' }}</td>
                <td class="text-right">{{ isset($data[$i]->total_price) ? number_format($data[$i]->total_price, 2) : '' }}</td>
                <td>{{ $data[$i]->logistic_date ?? '' }}</td>
                <td>rutin</td>
            </tr>
        @endfor
        </tbody>
    </table>
</div>
