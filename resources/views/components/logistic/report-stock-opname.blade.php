<div class="row">
    <div class="col-md-12">
        <div class="table-responsive" style="height: 500px; overflow-y: auto;">
            <style>
                .table-fixed-header thead th {
                    position: sticky;
                    top: 0;
                    background-color: #fff;
                    z-index: 1;
                    border-bottom: 2px solid #dee2e6;
                }
                .table-fixed-header {
                    border-collapse: separate;
                    border-spacing: 0;
                }
            </style>
            <table class="table table-bordered table-striped table-fixed-header">
                <thead>
                    <tr>
                        <th style="background-color: #fff;">#</th>
                        <th style="background-color: #fff;">Kode Logistik</th>
                        <th style="background-color: #fff;"><PERSON><PERSON></th>
                        <th style="background-color: #fff;">Tahun</th>
                        <th style="background-color: #fff;">Saldo Awal</th>
                        <th style="background-color: #fff;">Total Masuk</th>
                        <th style="background-color: #fff;">Total Keluar</th>
                        <th style="background-color: #fff;"><PERSON>do <PERSON>kh<PERSON></th>
                        <th style="background-color: #fff;"><PERSON><PERSON></th>
                        <th style="background-color: #fff;"><PERSON><PERSON><PERSON>rga</th>
                    </tr>
                </thead>

                <tbody>
                    @foreach ($data as $val)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $val->qr_code }}</td>
                        <td>{{ $val->asset_name }}</td>
                        <td class="text-center">{{ $val->tahun_terakhir ?? '-' }}</td>
                        <td class="text-end">{{ $val->qty_saldo_awal_asset }}</td>
                        <td class="text-end">{{ $val->qty_asset_masuk }}</td>
                        <td class="text-end">{{ $val->qty_asset_keluar }}</td>
                        <td class="text-end">{{ $val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar }}</td>
                        <td class="text-end">{{ number_format($val->unit_price, 0, ',', '.') }}</td>
                        <td class="text-end">{{ number_format($val->unit_price * ($val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar), 0, ',', '.') }}</td>
                    </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr class="fw-bold">
                        <td colspan="4" class="text-center">Total</td>
                        <td class="text-end">{{ number_format($data->sum('qty_saldo_awal_asset'), 0, ',', '.') }}</td>
                        <td class="text-end">{{ number_format($data->sum('qty_asset_masuk'), 0, ',', '.') }}</td>
                        <td class="text-end">{{ number_format($data->sum('qty_asset_keluar'), 0, ',', '.') }}</td>
                        <td class="text-end">{{ number_format($data->sum(function($val) { 
                            return $val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar;
                        }), 0, ',', '.') }}</td>
                        <td class="text-end">-</td>
                        <td class="text-end">{{ number_format($data->sum(function($val) {
                            return $val->unit_price * ($val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar);
                        }), 0, ',', '.') }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>