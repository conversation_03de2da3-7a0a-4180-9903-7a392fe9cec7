<i style="float: right">Laporan Dibuat: {{ date('Y-m-d : H:i:s') }}</i><br>
<h3 style="text-align: center" class="text-uppercase">
    {{ config('app.report_header_hospital') }}<br>
    PERIODE : {{ $params['start'] . ' - ' . $params['end'] }}
    @if(isset($params['room_name']) && $params['room_name'])
        <br>RUANGAN : {{ $params['room_name'] }}
    @endif
</h3>

<div class="table-responsive">
    <table class="table table-striped mt-5">
        <thead>
        <tr>
            <th rowspan="2" class="align-middle text-center">No</th>
            <th rowspan="2" class="align-middle text-center">Nomor Request</th>
            <th rowspan="2" class="align-middle text-center">Tanggal Logistik</th>
            <th rowspan="2" class="align-middle text-center"><PERSON><PERSON></th>
            <th rowspan="2" class="align-middle text-center"><PERSON><PERSON></th>
            <th rowspan="2" class="align-middle text-center"><PERSON><PERSON></th>
            <th rowspan="2" class="align-middle text-center">Jumlah Barang Request</th>
            <th rowspan="2" class="align-middle text-center">Jumlah Barang Keluar</th>
            <th rowspan="2" class="align-middle text-center">Satuan Barang</th>
            <th rowspan="2" class="align-middle text-center">Status</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($data as $index => $logistic)
            <tr>
                <td class="text-center">{{ $index + 1 }}</td>
                <td class="text-center">{{ $logistic->logistic_number }}</td>
                <td class="text-center">{{ $logistic->logistic_date }}</td>
                <td class="text-center">{{ $logistic->room_name }}</td>
                <td class="text-center">{{ $logistic->asset_entry_code }}</td>
                <td>{{ $logistic->asset_name }}</td>
                <td class="text-right">{{ $logistic->request_quantity }}</td>
                <td class="text-right">{{ ($logistic->logistic_id) ? $logistic->quantity : "-" }}</td>
                <td class="text-center">{{ $logistic->uom_name }}</td>
                <td class="text-center">{{ ($logistic->logistic_id) ? "Barang Keluar" : "Menunggu Realisasi" }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>

</div>
