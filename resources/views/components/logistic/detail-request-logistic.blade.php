<h3>{{$logistic->logistic_number}}</h3>

<div class="row mt-3">
    <div class="col-md-6 col-12">
        <div class="mt-2">
            <strong class="text-dark">R<PERSON>an: </strong><br>
            {{$logistic->room_code}} - {{$logistic->room_name}}
        </div>
        <div class="mt-2">
            <strong class="text-dark">Bidang Permintaan: </strong><br>
            {{$logistic->field_name}}
        </div>
    </div>
    <div class="col-md-6 col-12" style="text-align: right">
        <address class="mt-5px mb-5px">
            Tanggal Permintaan Barang<br>
            <strong class="text-dark">{{$logistic->logistic_date}}</strong>
        </address>
    </div>
</div>


<div class="table-responsive mt-5">
    <table class="table table-invoice">
        <thead>
        <tr>
            <th class="text-center"><PERSON>ang</th>
            <th class="text-center">Barang <PERSON></th>
            <th class="text-center">Satuan</th>
            <th class="text-center">Qty</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($logisticDetail as $item)
            <tr>
                <td>
                    <span class="text-dark">{{$item->item_name}}</span><br>
                    <small>{{$item->item_code}}</small>
                </td>
                <td>
                    <span class="text-dark">{{$item->asset_name}}</span><br>
                    <small>{{$item->asset_code}}</small>
                </td>
                <td>{{$item->uom_name}}</td>
                <td class="text-end">{{number_format($item->quantity,0,",",".")}}</td>
            </tr>
        @endforeach
        </tbody>
        <tfoot>
        <tr>
            <td colspan=3"><b>TOTAL</b></td>
            <td class="text-end"><b>{{number_format($logistic->total_logistic_quantity,0,",",".")}}</b></td>
        </tr>
        </tfoot>
    </table>

    <div class="mt-4">
        <strong class="text-dark">Pembuat: </strong><br>
        {{$logistic->created_by_name}}
    </div>

    <div class="mt-4">
        <strong class="text-dark">Catatan: </strong><br>
        {{$logistic->logistic_notes}}
    </div>
    @if (isset($logistic->logistic_document_path))
        <div class="mt-4">
            <strong class="text-dark">File Attachment: </strong><br>
            <a href="{{ asset('storage/' . $logistic->logistic_document_path) }}" target="_blank">
                {{ basename($logistic->logistic_document_path) }}
            </a>
        </div>
    @endif
</div>
