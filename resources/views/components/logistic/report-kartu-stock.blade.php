<div class="row">
    <div class="col-md-12">
        <span class="d-block text-center fs-14px fw-800 mb-3">Laporan Kartu Stock</span>

        <div class="row mb-3">
            <div class="col-md-3 text-center">
                <span class="d-block fs-20px fw-800">{{ $summary->qty_saldo_awal_asset }}</span>
                <strong>Saldo Awal</strong>
            </div>

            <div class="col-md-3 text-center">
                <span class="d-block fs-20px fw-800">{{ $summary->qty_asset_masuk }}</span>
                <strong>Total Masuk</strong>
            </div>

            <div class="col-md-3 text-center">
                <span class="d-block fs-20px fw-800">{{ $summary->qty_asset_keluar }}</span>
                <strong>Total Keluar</strong>
            </div>

            <div class="col-md-3 text-center">
                <span class="d-block fs-20px fw-800">{{ $summary->qty_saldo_awal_asset + $summary->qty_asset_masuk - $summary->qty_asset_keluar }}</span>
                <strong>Saldo Akhir</strong>
            </div>
        </div>

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Kode QR</th>
                    <th>Nama Aset</th>
                    <th>Tanggal Transaksi</th>
                    <th>Nomor Logistik</th>
                    <th>Jenis Transaksi</th>
                    <th>Ruangan</th>
                    <th>Jumlah</th>
                    <th>Harga</th>
                </tr>
            </thead>

            <tbody>
                @foreach ($details as $detail)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $detail->asset->asset_code }}</td>
                    <td>{{ $detail->asset->asset_name }}</td>
                    <td>{{ Carbon\Carbon::parse($detail->logistic->logistic_date)->format('d/m/Y') }}</td>
                    <td>{{ $detail->logistic->logistic_number }}</td>
                    <td>{{ $detail->logistic_type }}</td>
                    <td>{{ $detail->room_code ? $detail->room_code.' - '.$detail->room_name : '-' }}</td>
                    <td class="text-end">{{ $detail->quantity }}</td>
                    <td class="text-end">{{ number_format($detail->unit_price, 0, ',', '.') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>