<i style="float: right">Laporan Dibuat: {{ date('Y-m-d : H:i:s') }}</i><br>

<h3 style="text-align: center">
    LAPORAN PENGADAAN BMD BERUPA ASET LANCAR PERSEDIAAN<br>
    {{ config('app.report_header_hospital') }}<br>
    PROVINSI {{ strtoupper(config('app.hospital_province')) }}<br>
    PERIODE : {{ $params['start'] . ' - ' . $params['end'] }}
</h3>

<div class="mt-3">
    <div class="row">
        <div class="col-md-2">
            <p>Provinsi</p>
            <p>Kabupaten/Kota</p>
        </div>
        <div class="col-md-4">
            <p>: {{ strtoupper(config('app.hospital_province')) }}</p>
            <p>: {{ strtoupper(config('app.hospital_city')) }}</p>
        </div>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-bordered table-striped mt-3">
        <thead>
        <tr>
            <th colspan="2" class="text-center">Penggolongan dan Kodifikasi Barang</th>
            <th rowspan="2" class="align-middle text-center">Spesifikasi<br>Nama Barang</th>
            <th rowspan="2" class="align-middle text-center">Merek/Type</th>
            <th rowspan="2" class="align-middle text-center">Jumlah<br>Barang</th>
            <th rowspan="2" class="align-middle text-center">Satuan<br>Barang</th>
            <th rowspan="2" class="align-middle text-center">Harga Satuan<br>(Rp)</th>
            <th rowspan="2" class="align-middle text-center">Total Nilai Barang<br>(Rp)</th>
            <th rowspan="2" class="align-middle text-center">Total Biaya<br>Ambil (Rp)</th>
            <th rowspan="2" class="align-middle text-center">Nilai Perolehan<br>Barang (Rp)</th>
            <th rowspan="2" class="align-middle text-center">Harga Satuan<br>Perolehan (Rp)</th>
            <th colspan="4" class="text-center">Sub Kegiatan dan Rekening Anggaran Belanja Daerah<br>Asal Pengadaan Barang</th>
            <th rowspan="2" class="align-middle text-center">Tanggal<br>Perolehan</th>
            <th colspan="2" class="text-center">Dokumen Sumber Perolehan</th>
            <th rowspan="2" class="align-middle text-center">Nomor</th>
            <th rowspan="2" class="align-middle text-center">Keterangan</th>
        </tr>
        <tr>
            <th class="text-center">Kode Barang</th>
            <th class="text-center">Nama Barang</th>
            <th class="text-center">Kode Sub<br>Kegiatan</th>
            <th class="text-center">Nama Sub<br>Kegiatan</th>
            <th class="text-center">Kode<br>Rekening</th>
            <th class="text-center">Uraian<br>Belanja</th>
            <th class="text-center">Bentuk</th>
            <th class="text-center">Nama<br>Penyedia</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($data as $index => $logistic)
            <tr>
                <td class="text-center">{{ $logistic->asset_code }}</td>
                <td>{{ $logistic->asset_name }}</td>
                <td></td>
                <td></td>
                <td class="text-right">{{ $logistic->quantity }}</td>
                <td class="text-center">{{ $logistic->uom_name }}</td>
                <td class="text-right">{{ number_format($logistic->unit_price, 2) }}</td>
                <td class="text-right">{{ number_format($logistic->total_price, 2) }}</td>
                <td></td>
                <td class="text-right"></td>
                <td class="text-right"></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td class="text-center">{{ $logistic->logistic_date }}</td>
                <td></td>
                <td></td>
                <td>{{ $logistic->logistic_number }}</td>
                <td>{{ $logistic->logistic_notes }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>
</div>
