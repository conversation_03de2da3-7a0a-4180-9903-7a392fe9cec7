<h3>{{$logistic->logistic_number}}</h3>

<div class="row mt-3">
    <div class="col-md-6 col-12">

    </div>
    <div class="col-md-6 col-12" style="text-align: right">
        <address class="mt-5px mb-5px">
            Tanggal Permintaan Barang<br>
            <strong class="text-dark">{{$logistic->logistic_date}}</strong>
        </address>
    </div>
</div>

<div class="table-responsive mt-5">
    <table class="table table-invoice">
        <thead>
        <tr>
            <th class="text-center">Barang</th>
            <th class="text-center">Barang Habis Pakai</th>
            <th style="width: 150px" class="text-center">Satuan</th>
            <th style="width: 150px" class="text-center">Qty</th>
            <th style="width: 150px" class="text-center">Saldo Akhir</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($logisticDetail as $item)
            <tr>
                <td>
                    <span class="text-dark">{{$item->item_name}}</span><br>
                    <small>{{$item->item_code}}</small>
                </td>
                <td>
                    <span class="text-dark">{{$item->asset_name}}</span><br>
                    <small>{{$item->asset_code}}</small>
                </td>
                <td>{{$item->uom_name}}</td>
                <td>
                    <input name="request_logistic_detail_id[]" value="{{$item->id}}" style="display: none">
                    <input name="asset_qty[]" class="form-control asset-qty" value="{{$item->quantity}}" min="0" max="{{$item->quantity}}" placeholder="Kuantitas">
                </td>
                <td>{{$item->latest_balance}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>

    <div class="mt-4">
        <strong class="text-dark">Ruangan: </strong><br>
        {{$logistic->room_code}} - {{$logistic->room_name}}
    </div>

    <div class="mt-4">
        <strong class="text-dark">Pembuat: </strong><br>
        {{$logistic->created_by_name}}
    </div>

    <div class="mt-4">
        <strong class="text-dark">Catatan: </strong><br>
        {{$logistic->logistic_notes}}
    </div>
    @if (isset($logistic->logistic_document_path))
        <div class="mt-4">
            <strong class="text-dark">File Attachment: </strong><br>
            <a href="{{ asset('storage/' . $logistic->logistic_document_path) }}" target="_blank">
                {{ basename($logistic->logistic_document_path) }}
            </a>
        </div>
    @endif
</div>

<script>
    document.querySelectorAll('.asset-qty').forEach(function (input) {
        let maxValue = parseInt(input.getAttribute('value'));
        input.addEventListener('input', function () {
            this.value = this.value.replace(/[^0-9]/g, '');
            if (parseInt(this.value) > maxValue) {
                this.value = maxValue;
            }
        });
        input.addEventListener('blur', function () {
            if (this.value === '') {
                this.value = '0';
            }
        });
    });

    document.querySelector('form').addEventListener('submit', function (e) {
        document.querySelectorAll('.asset-qty').forEach(function (input) {
            let maxValue = parseInt(input.getAttribute('value'));
            if (parseInt(input.value) > maxValue) {
                alert('Nilai tidak boleh lebih dari ' + maxValue + '.');
                input.value = maxValue;
                e.preventDefault();
            }
            if (input.value === '') {
                input.value = '0';
            }
        });
    });
</script>
