<i style="float: right">Laporan Dibuat: {{ date('Y-m-d : H:i:s') }}</i><br>
<h3 style="text-align: center" class="text-uppercase">
    {{ strtoupper(config('app.report_header_hospital')) }} <br>
    PERIODE : {{ $params['period_label'] }}<br>
    <small><PERSON><PERSON>: {{
        $params['logistic_type'] === 'all' ? 'Seluruh Data Logistik' :
        ($params['logistic_type'] === 'internal' ? 'Logistik' : 'Non Logistik')
    }}</small>
</h3>

<div class="table-responsive">
    <table class="table table-bordered table-striped mt-5">
        <thead>
        <tr>
            <th rowspan="2" class="align-middle text-center">No</th>
            <th rowspan="2" class="align-middle text-center"><PERSON><PERSON></th>
            <th rowspan="2" class="align-middle text-center"><PERSON><PERSON><PERSON></th>
            <th rowspan="2" class="align-middle text-center">Keterangan</th>
        </tr>
        </thead>
        <tbody>
        @php
            $totalPrice = 0;
        @endphp
        @foreach ($data as $index => $logistic)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $logistic->jenis_barang }}</td>
                <td>{{ $logistic->jumlah_harga ? number_format($logistic->jumlah_harga, 2) : 0 }}</td>
                <td>-</td>
            </tr>
            @php
                $totalPrice += $logistic->jumlah_harga;
            @endphp
        @endforeach
        </tbody>
        <tfoot>
        <th></th>
        <th><b>Total</b></th>
        <th>{{ number_format($totalPrice, 2) }}</th>
        <th></th>
        </tfoot>
    </table>

</div>
