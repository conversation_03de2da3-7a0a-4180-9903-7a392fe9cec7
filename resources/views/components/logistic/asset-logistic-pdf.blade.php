<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 0mm;
            box-sizing: border-box;
            page-break-after: always;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid black;
            text-align: center;
            margin-bottom: 10px;
        }
        .header img {
            width: 50px;
            display: block;
            margin: 0 auto;
        }
        .header .title {
            text-align: center;
        }
        .header .title h2 {
            margin: 0;
            font-size: 14px;
        }
        .header .title p {
            margin: 5px 0 0;
            font-size: 10px;
        }
        .report-title {
            text-align: center;
            margin-bottom: 15px;
        }
        .report-title h6 {
            margin: 0;
            font-size: 12px;
        }
        .filter-info {
            margin-bottom: 15px;
            font-size: 11px;
            font-style: italic;
        }
        .filter-info p {
            margin: 2px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10px !important;
        }
        th, td {
            border: 1px solid black;
            padding: 5px;
            text-align: left;
        }
        th {
            text-align: center;
            font-weight: bold;
        }
        .no {
            width: 5%;
            text-align: center;
        }
        .kode {
            width: 15%;
        }
        .nama {
            width: 25%;
        }
        .satuan {
            width: 10%;
            text-align: center;
        }
        .rekapitulasi {
            width: 20%;
        }
        .bidang {
            width: 15%;
        }
        .status {
            width: 10%;
            text-align: center;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 11px;
        }
        .text-center {
            text-align: center;
        }
        .text-end {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo">
                    </td>
                    <td style="border: none;">
                        <div class="title">
                            <h2>{{ config('app.report_header_area') }}</h2>
                            <h2>{{ config('app.report_header_hospital') }}</h2>
                            <p>{{ config('app.report_header_address') }}</p>
                            <p>{{ config('app.report_header_phone') }}</p>
                            <p>{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div class="report-title">
            <h6>
                <u>{{ $title }}</u><br>
                <i style="float: right">Laporan Dibuat: {{ $timestamp }}</i><br>
            </h6>
        </div>

        @if(isset($params) && (isset($params['filter_kode_barang']) || isset($params['filter_status']) || isset($params['search'])))
        <div class="filter-info">
            @if(isset($params['filter_kode_barang']))
                @php
                    $item = \App\Models\Item::find($params['filter_kode_barang']);
                @endphp
                @if($item)
                    <p>Kode Barang: {{ $item->item_code }} - {{ $item->item_name }}</p>
                @endif
            @endif
            @if(isset($params['filter_status']))
                <p>Filter Status: {{ $params['filter_status'] === 'active' ? 'Aktif' : 'Tidak Aktif' }}</p>
            @endif
            @if(isset($params['search']))
                <p>Pencarian: {{ $params['search'] }}</p>
            @endif
        </div>
        @endif

        <table>
            <thead>
                <tr>
                    <th class="no">#</th>
                    <th class="kode">Kode Barang</th>
                    <th class="nama">Nama Barang Habis Pakai</th>
                    <th class="satuan">Satuan</th>
                    <th class="rekapitulasi">Stok Rekapitulasi</th>
                    <th class="bidang">Bidang Permintaan</th>
                    <th class="status">Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data as $index => $row)
                <tr>
                    <td class="no">{{ $index + 1 }}</td>
                    <td class="kode">{{ $row->asset_entry_code }}</td>
                    <td class="nama">{{ $row->asset_name }}</td>
                    <td class="satuan">{{ $row->uom_name }}</td>
                    <td class="rekapitulasi">{{ $row->recapitulation_name }}</td>
                    <td class="bidang">{{ $row->stock_name }}</td>
                    <td class="status">{{ $row->active == 1 ? 'Aktif' : 'Tidak Aktif' }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr class="fw-bold">
                    <td colspan="6" class="text-center">Total</td>
                    <td class="text-center">{{ count($data) }} item</td>
                </tr>
            </tfoot>
        </table>
    </div>
</body>
</html>
