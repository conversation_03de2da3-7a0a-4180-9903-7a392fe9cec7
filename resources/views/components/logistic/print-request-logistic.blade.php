<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}"/>
    <title><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 12px;
        }

        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .header {
            text-align: center;
        }

        .header h1 {
            font-size: 18px;
            margin: 0;
        }

        .header h2 {
            font-size: 16px;
            margin: 5px 0;
        }

        .header p {
            font-size: 12px;
            margin: 2px 0;
        }

        .title {
            text-align: center;
            margin-top: 10px;
            font-size: 16px;
            font-weight: bold;
        }

        .info {
            margin-top: 5px;
            font-size: 12px;
            text-align: right;
        }

        .info span {
            display: block;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
        }

        .recipient {
            font-size: 12px;
        }

        .body {
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        table,
        th,
        td {
            border: 1px solid black;
        }

        th,
        td {
            padding: 8px;
            text-align: left;
        }

        th {
            text-align: center;
        }

        .footer {
            font-size: 12px;
            margin-top: 30px;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="header">
        <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
        <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
        <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
        <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
        <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
    </div>

    <hr>

    <div class="info">
        <p>No. Nota: {{ $logistic->logistic_number }}</p>
    </div>

    <div class="title">
        <h3>NOTA PERMINTAAN BARANG</h3>
    </div>

    <div class="recipient">
        <span style="margin: 0 0 10px 0;">Pontianak,</span>
        <p style="margin: 0 0 2px 0;">Kepada Yth.</p>
        <p style="margin: 0 0 2px 0;">Direktur {{ config('app.hospital_name') }}</p>
        <p style="margin: 0 0 2px 0;">u.p. Ka. Bag. Perencanaan Program dan Aset</p>
        <p style="margin: 0 0 10px 0;">di</p>
        <b><u>PONTIANAK</u></b>
    </div>

    <div class="body" style="margin-top: 30px;">
        <p>Bersama ini disampaikan permohonan penyaluran barang untuk pelaksanaan kegiatan, dengan rincian sebagai berikut :</p>
        <table>
            <thead>
            <tr>
                <th>No</th>
                <th>Nama Barang</th>
                <th>Volume</th>
                <th>Satuan</th>
            </tr>
            </thead>
            <tbody>
            @foreach ($logisticDetail as $item)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $item->item_name }}</td>
                    <td class="text-end">{{ number_format($item->quantity, 0, ',', '.') }}</td>
                    <td>{{ $item->uom_name }}</td>
                </tr>
            @endforeach

            </tbody>
        </table>
        <p>Instalasi / Ruangan: {{ $logistic->room_code." ".$logistic->room_name }}</p>
        <p>Bidang Permintaan: {{ $logistic->field_name }}</p>
    </div>

    <div class="footer">
        <p>Demikian disampaikan, atas perhatiannya diucapkan terima kasih.</p>
    </div>
</div>
</body>

</html>
