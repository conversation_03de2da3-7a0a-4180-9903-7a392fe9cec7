<!DOCTYPE html>
<html>

<head>
    <title>Laporan Stock Opname</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 0mm;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid black;
        }

        .header img {
            width: 100px;
        }

        .header .title {
            text-align: center;
        }

        .header .title h2 {
            margin: 0;
        }

        .header .title p {
            margin: 5px 0 0;
            font-size: 12px;
        }

        .info {
            margin-left: 82%;
            font-size: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th {
            text-align: center;
        }

        th,
        td {
            border: 1px solid black;
            padding: 5px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 50px;
        }

        .footer .signature {
            width: 200px;
        }

        .footer .signature p {
            margin: 5px 0;
        }
    </style>
</head>

<body>

    <div class="container">
        <div class="header" style="text-align: center; margin-bottom: 10px;">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo"
                            style="width: 50px; display: block; margin: 0 auto;">
                    </td>
                    <td style="border: none;">
                        <div class="title" style="text-align: center;">
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <h6 style="text-align: center;">
            <u>LAPORAN STOCK OPNAME</u><br>
            PERIODE : {{ $params['start'] . ' - ' . $params['end'] }}<br>
            @if(isset($params['stock_recap_name']) && $params['stock_recap_name'])
            JENIS BARANG : {{ $params['stock_recap_name'] }}<br>
            @endif
            <i style="float: right">Laporan Dibuat: {{ date('Y-m-d : H:i:s') }}</i><br>
        </h6>

        <table style="font-size: 10px !important;">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Kode Logistik</th>
                    <th>Nama Barang</th>
                    <th>Tahun</th>
                    <th>Saldo Awal</th>
                    <th>Total Masuk</th>
                    <th>Total Keluar</th>
                    <th>Saldo Akhir</th>
                    <th>Harga Satuan</th>
                    <th>Jumlah Harga</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($data as $index => $val)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $val->qr_code }}</td>
                    <td>{{ $val->asset_name }}</td>
                    <td class="text-center">{{ $val->tahun_terakhir ?? '-' }}</td>
                    <td class="text-end">{{ $val->qty_saldo_awal_asset }}</td>
                    <td class="text-end">{{ $val->qty_asset_masuk }}</td>
                    <td class="text-end">{{ $val->qty_asset_keluar }}</td>
                    <td class="text-end">{{ $val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar }}</td>
                    <td class="text-end">{{ number_format($val->unit_price, 0, ',', '.') }}</td>
                    <td class="text-end">{{ number_format($val->unit_price * ($val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar), 0, ',', '.') }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr class="fw-bold">
                    <td colspan="4" class="text-center">Total</td>
                    <td class="text-end">{{ number_format($data->sum('qty_saldo_awal_asset'), 0, ',', '.') }}</td>
                    <td class="text-end">{{ number_format($data->sum('qty_asset_masuk'), 0, ',', '.') }}</td>
                    <td class="text-end">{{ number_format($data->sum('qty_asset_keluar'), 0, ',', '.') }}</td>
                    <td class="text-end">{{ number_format($data->sum(function($val) {
                    return $val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar;
                }), 0, ',', '.') }}</td>
                    <td class="text-end">-</td>
                    <td class="text-end">{{ number_format($data->sum(function($val) {
                    return $val->unit_price * ($val->qty_saldo_awal_asset + $val->qty_asset_masuk - $val->qty_asset_keluar);
                }), 0, ',', '.') }}</td>
                </tr>
            </tfoot>
        </table>
    </div>
</body>

</html>