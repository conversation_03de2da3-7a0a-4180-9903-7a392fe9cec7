<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <title><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .container {
            width: 100%;
            height: auto;
            margin: 0 auto;
            padding: 0mm;
            box-sizing: border-box;
            page-break-after: always;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid black;
        }

        .header img {
            width: 100px;
        }

        .header .title {
            text-align: center;
        }

        .header .title h2 {
            margin: 0;
        }

        .header .title p {
            margin: 5px 0 0;
            font-size: 12px;
        }

        .info {
            margin-left: 60%;
            font-size: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th {
            text-align: center;
        }

        th,
        td {
            border: 1px solid black;
            padding: 5px;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 20px;
        }

        .footer .signature {
            width: 200px;
        }

        .footer .signature p {
            margin: 5px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header" style="text-align: center; margin-bottom: 10px;">
            <table width="100%" style="border: none;">
                <tr style="border: none;">
                    <td width="5%" style="text-align: left; border: none;">
                        <img src="{{ public_path('/img/logo/logo-kalbar.png') }}" alt="Logo"
                            style="width: 50px; display: block; margin: 0 auto;">
                    </td>
                    <td style="border: none;">
                        <div class="title" style="text-align: center;">
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_area') }}</h2>
                            <h2 style="font-size: 14px; margin: 0;">{{ config('app.report_header_hospital') }}</h2>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_address') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_phone') }}</p>
                            <p style="font-size: 10px; margin: 0;">{{ config('app.report_header_email') }}</p>
                        </div>
                    </td>
                </tr>
            </table>
        </div>


        <div class="info">
            <span style="display: block; margin-bottom: 5px;">Jam : <b style="font-size: 11px; padding-left: 20px; font-weight: bold;">{{ \Carbon\Carbon::parse($logistic->created_at)->format('H.i') }}</b></span>
            <span style="display: block; margin-bottom: 5px;">No : <b style="font-size: 11px; padding-left: 28px; font-weight: bold;">{{ $listUniqueLogistic }}</b></span>
            <span style="display: block; margin-bottom: 5px;">Ruangan : <b style="font-size: 11px; font-weight: bold;">{{$logistic->room_code}} - {{$logistic->room_name}}</b> </span>
        </div>

        <div class="row mt-2">
            <h4 style="text-align: center;"><u>BUKTI PENGAMBILAN BARANG DARI GUDANG</u></h4>

            <div class="table-responsive mt-5">
                <table class="table table-invoice" style="font-size: 9px !important;">
                    <thead>
                        <tr>
                            <th rowspan="2" width="20px">No</th>
                            <th rowspan="2" width="15%">Tgl. Penyerahan Brg. Menurut Permintaan</th>
                            <th rowspan="2" width="80px">Barang Diterima <br> dari Gudang</th>
                            <th rowspan="2" width="25%">Nama dan Kode Barang</th>
                            <th rowspan="2">Satuan</th>
                            <th rowspan="2">Harga</th>
                            <th colspan="2">Jumlah Barang</th>
                            <th rowspan="2">JumlahHarga</th>
                        </tr>
                        <tr>
                            <th>Angka</th>
                            <th>Huruf</th>
                        </tr>
                    </thead>

                    <tbody>
                        @php
                        $no = 1;
                        @endphp
                        @foreach ($logisticDetail as $item)
                        <tr>
                            <td>{{ $no++ }}</td>
                            <td>{{ \Carbon\Carbon::parse($logistic->logistic_date)->format('Y-m-d') }} {{ \Carbon\Carbon::parse($logistic->created_at)->format('H.i') }}</td>
                            <td>-</td>
                            <td>
                                <span style="font-size: 120%;" class="text-dark">{{$item->asset_name}}</span><br>
                                <small>{{$item->asset_code}}</small>
                            </td>
                            <td>{{$item->uom_name}}</td>
                            <td class="text-end">{{number_format($item->unit_price,0,",",".")}}</td>
                            <td class="text-end">{{number_format($item->quantity,0,",",".")}}</td>
                            <td class="text-end">{{ $item->quantity_in_words }}</td>
                            <td class="text-end">{{number_format($item->total_price,0,",",".")}}</td>
                        </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="6">TOTAL</td>
                            <td class="text-end">{{ number_format($logisticDetail->sum('quantity'), 0, ",", ".") }}</td>
                            <td class="text-end"></td>
                            <td class="text-end">{{ number_format($logisticDetail->sum('total_price'), 0, ",", ".") }}</td>
                        </tr>
                    </tfoot>
                </table>

                <div class="footer">
                    <table style="border: none; font-size: 10px; text-align: left;">
                        <tr style="border: none;">
                            <td style="width: 30%; text-align: left;border: none;">Yang Menerima</td>
                            <td style="width: 40%; text-align: left;border: none;"></td>
                            <td style="width: 30%; text-align: left;border: none;">Yang Menyerahkan<br>Pengurus Barang Pengguna</td>
                        </tr>
                        <tr style="border: none;">
                            <td colspan="3" style="height: 20px;border: none;"></td>
                        </tr>
                        <tr style="border: none;">
                            <td style="width: 30%; border: none;">
                                <span style="text-decoration: underline;">{{ $recipientName }}</span><br>
                                NIP. {{ $recipientNIP }}
                            </td>
                            <td style="width: 40%; border: none;"></td>
                            <td style="width: 30%; border: none;">
                                <span style="text-decoration: underline;">{{ $goodsManager }}</span><br>
                                Penata Muda<br>
                                NIP.{{ $goodsManagerNIP }}
                            </td>
                        </tr>
                    </table>
                </div>

                <div style="text-align: left; font-size: 10px; margin-top: 0px;">
                    <p style="margin-left: 40%;">
                        Mengetahui:<br>
                        Ketua Tim Kerja Asset
                    </p>

                    <div style="margin-top: 40px; margin-left: 40%;">
                        <span style="display: block;">
                            <strong><u>{{ $leadAsset }}</u></strong><br>
                            Pembina<br>
                            NIP: {{ $leadAssetNIP }}
                        </span>
                    </div>
                </div>

                <div style="font-size: 8px !important;">
                    <div class="mt-2">
                        <strong class="text-dark">Catatan: </strong><br>
                        {{$logistic->logistic_notes}}
                    </div>
                    @if (isset($logistic->logistic_document_path))
                    <div class="mt-4">
                        <strong class="text-dark">File Attachment: </strong><br>
                        <a href="{{ asset('storage/' . $logistic->logistic_document_path) }}" target="_blank">
                            {{ basename($logistic->logistic_document_path) }}
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</body>

</html>