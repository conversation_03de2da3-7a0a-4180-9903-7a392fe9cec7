<a href="#offcanvasAlocation" data-bs-toggle="offcanvas" class="btn btn-success me-1 d-none" id="btn-alokasi"><i class="fas fa-forward me-1"></i>Alokasi Asset</a>

<!-- Form -->
<div class="offcanvas offcanvas-end" id="offcanvasAlocation" data-bs-backdrop="false" data-bs-scroll="true" style="width: 720px !important;">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="offcanvasLabel">Form Alokasi</h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Kode Barang</th>
                    <th><PERSON>a <PERSON></th>
                    <th>Register Code</th>
                    <th>QR Code</th>
                    <th><PERSON><PERSON><PERSON></th>
                </tr>
            </thead>
            <tbody id="table-allocation"></tbody>
        </table>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#btn-alokasi').on('click', function() {
            $('#offcanvasAlocation').offcanvas('show');
            let selectedItems = [];
            const checkedCheckboxes = $("#datatable").find(".checkbox-id:checked");

            if (checkedCheckboxes.length > 0) {
                checkedCheckboxes.each(function() {
                    const itemId = $(this).val();
                    selectedItems.push(itemId);
                });
            }

            $.ajax({
                url: "{{ route('asset.allocation.list') }}",
                method: 'POST',
                data: {
                    id: selectedItems
                },
                success: function(response) {
                    let assets = response.assets;

                    $('#table-allocation').empty();
                    $.each(assets, function(key, asset) {
                        $('#table-allocation').append(`
                            <tr>
                                <td>${key + 1}</td>
                                <td>${asset.barang.kode_barang}</td>
                                <td>${asset.barang.nama_barang}</td>
                                <td>${asset.register_code}</td>
                                <td>${asset.qr_code}</td>
                            </tr>
                        `);
                    });
                }
            })
        });
    });
</script>
