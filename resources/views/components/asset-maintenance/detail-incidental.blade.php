<div class="row">
    <div class="col-md-2"></div>
    <div class="col-md-6"><h3 style="text-align: center; padding-top: 40px;">Formulir Kerusakan Barang</h3></div>
    <div class="col-md-4">
        <table>
            <tr>
                <td colspan="2">
                    <div style="padding: 10px; text-align: center">{{ config('app.hospital_name') }}</div>
                </td>
            </tr>
            <tr>
                <td>No Dokumen: &nbsp;</td>
                <td>{{$incidentalRequestDetail->request_code}}</td>
            </tr>
            <tr>
                <td>Tanggal: &nbsp;</td>
                <td>{{$incidentalRequestDetail->request_date}}</td>
            </tr>
            <tr>
                <td>Status: &nbsp;</td>
                <td>{{$incidentalRequestDetail->latest_status}}</td>
            </tr>
            <tr>
                <td>Klasifikasi: &nbsp;</td>
                <td>Internal</td>
            </tr>
        </table>
    </div>
</div>

<hr>

<div style="font-size: 90%; padding: 10px; text-align: right;">
    <i>Tanggal Request: {{$incidentalRequestDetail->request_date}}</i> <br>
    <i>Pembuat Request: {{$incidentalRequestDetail->created_by_name}}</i>
</div>

<div style="font-size: 110%; padding: 10px;">
    <div class="row" style="padding-left: 10px;">
        <div class="col-md-12">Bersama ini kami laporkan adanya kerusakan alat/barang sebagai berikut :</div>
    </div>
    <div class="row" style="padding-left: 20px; padding-top: 10px;">
        <div class="col-md-3">Jenis Barang</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->item_name}}</div>
    </div>
    <div class="row" style="padding-left: 20px; padding-top: 5px;">
        <div class="col-md-3">Pengguna</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->room_name}}</div>
    </div>
    <div class="row" style="padding-left: 20px; padding-top: 5px;">
        <div class="col-md-3">Unit</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->qr_code}} - {{$incidentalRequestDetail->asset_name}}</div>
    </div>
</div>

<div style="font-size: 110%; padding: 10px;">
    <div class="row" style="padding-left: 10px;">
        <div class="col-md-12">Data Barang:</div>
    </div>

    <div class="row" style="padding-left: 20px; padding-top: 10px;">
        <div class="col-md-3">Merk</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->brand}}</div>
    </div>
    <div class="row" style="padding-left: 20px; padding-top: 5px;">
        <div class="col-md-3">Type</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->type}}</div>
    </div>
    <div class="row" style="padding-left: 20px; padding-top: 5px;">
        <div class="col-md-3">Serial Number</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->serial_number}}</div>
    </div>
</div>

<div style="font-size: 110%; padding: 10px;">
    <div class="row" style="padding-left: 10px; padding-top: 10px;">
        <div class="col-md-3">Jenis Kerusakan</div>
        <div class="col-md-9">: {{$incidentalRequestDetail->request_issue}}</div>
    </div>
</div>


<div class="row">
    <div class="col-md-6">
        <div style="font-size: 110%; padding: 10px;">
            <div class="row" style="padding-left: 10px; padding-top: 10px;">
                <div class="col-md-3">Timeline</div>
            </div>
        </div>

        <div class="my-30p p-10">
            <div class="timeline">
                @foreach($incidentalRequestActivity as $item)
                    <div class="timeline-item">
                        <div class="timeline-time">
                            <span class="date">{{ \Carbon\Carbon::parse($item->activity_time ?? '')->format('Y-m-d') }}</span>
                            <span class="date">{{ \Carbon\Carbon::parse($item->activity_time ?? '')->format('H:i') }}</span>
                        </div>
                        <div class="timeline-icon"><a href="javascript:;">&nbsp;</a></div>
                        <div class="timeline-content">
                            <div class="timeline-body">
                                <b>{{$item->latest_status}}</b>
                                <div class="pt-3">
                                    {{$item->activity_name}}<br>
                                    {{$item->activity_issue}}<br>
                                    {{$item->activity_notes}}<br>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="col-md-6">
        @if(count($incidentalRequestOfficer)> 0)
            <div style="font-size: 110%; padding: 10px;">
                <div class="row" style="padding-left: 10px; padding-top: 10px;">
                    <div class="col-md-3">Petugas</div>
                </div>
                <div class="row" style="padding-left: 10px; padding-top: 20px;">
                    <table class="table table-bordered table-striped">
                        <thead>
                        <tr>
                            <th>Nama Petugas</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($incidentalRequestOfficer as $officer)
                            <tr>
                                <td>{{ $officer['employee_name'] }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif

        @if(count($incidentalRequestList)> 0)
            <div style="font-size: 110%; padding: 10px;">
                <div class="row" style="padding-left: 10px; padding-top: 10px;">
                    <div class="col-md-3">BHP</div>
                </div>
                <div class="row" style="padding-left: 10px; padding-top: 20px;">
                    <table class="table table-bordered table-striped">
                        <thead>
                        <tr>
                            <th>Nama</th>
                            <th>Harga</th>
                            <th>Qty</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($incidentalRequestList as $list)
                            <tr>
                                <td>{{ $list['name'] }}</td>
                                <td>{{ $list['price'] }}</td>
                                <td>{{ $list['qty'] }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
    </div>
</div>

<div style="font-size: 110%; padding: 10px; text-align: center; margin-top: 50px">
    Penanggung Jawab Perbaikan
    <div style="margin-top: 70px;">{{$incidentalRequestDetail->item_pic_name}}</div>
</div>
