<div class="w-100">
    <div class="text-center mb-4">
        <h4 class="mb-1">USULAN RENCANA KEBUTUHAN PEMELIHARAAN BARANG MILIK DAERAH</h4>
        <h5 class="mb-1">(RENCANA PEMELIHARAAN)</h5>
        <h5 class="mb-1">{{ strtoupper(config('app.report_header_hospital')) }}</h5>
        <h5>TAHUN {{ request()->year }}</h5>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered table-hover w-100" style="min-width: 1500px">
            <thead>
            <tr class="align-middle text-center">
                <th rowspan="3" style="width: 50px">No.</th>
                <th rowspan="3" style="width: 300px">Program/Kegiatan/Output</th>
                {{-- <PERSON><PERSON> --}}
                <th colspan="5" style="width: 500px"><PERSON><PERSON></th>
                {{-- <PERSON><PERSON><PERSON> --}}
                <th colspan="3" style="width: 150px"><PERSON><PERSON><PERSON></th>
                {{-- <PERSON>ulan Kebutuhan Pemeliharaan --}}
                <th colspan="3" style="width: 400px">Usulan Kebutuhan Pemeliharaan</th>
                <th rowspan="3" style="width: 100px">Ket</th>
            </tr>
            <tr class="text-center">
                {{-- Barang Yang Dipelihara --}}
                <th rowspan="2">Kode Barang</th>
                <th rowspan="2">Nama Barang</th>
                <th rowspan="2">Jumlah</th>
                <th rowspan="2">Satuan</th>
                <th rowspan="2">Status Barang</th>
                {{-- Kondisi Barang --}}
                <th colspan="3">Kondisi Barang</th>
                {{-- Usulan Kebutuhan Pemeliharaan --}}
                <th rowspan="2">Nama Pemeliharaan</th>
                <th rowspan="2">Jumlah</th>
                <th rowspan="2">Satuan</th>
            </tr>
            <tr class="text-center">
                {{-- Kondisi Barang --}}
                <th style="width: 50px">B</th>
                <th style="width: 50px">RR</th>
                <th style="width: 50px">RB</th>
            </tr>
            </thead>
            <tbody>
            @php
                $currentProgram = null;
                $no = 1;
            @endphp
            @forelse($data as $plan)
                @foreach($plan->maintenancePlanDetails as $detail)
                    @php
                        $programs = $detail->maintenancePlanDetailPrograms->sortBy('level');
                        $programStructure = $programs->pluck('program_name')->implode(' / ');
                    @endphp
                    @if($programStructure != $currentProgram)
                        @foreach($programs as $program)
                            <tr class="table-light">
                                <td></td>
                                <td colspan="13"><strong>{{ $program->program_code }} - {{ $program->program_name }}</strong></td>
                            </tr>
                        @endforeach
                        @php
                            $currentProgram = $programStructure;
                        @endphp
                    @endif
                    <tr>
                        <td class="text-center">{{ $no++ }}</td>
                        <td></td>
                        <td class="text-center">{{ $detail->code }}</td>
                        <td>{{ $detail->name }}</td>
                        <td class="text-center">{{ number_format($detail->quantity, 0, ',', '.') }}</td>
                        <td class="text-center">{{ $detail->uom_name }}</td>
                        <td class="text-center">-</td>
                        <td class="text-center">{{ $detail->condition == 'BAIK' ? '√' : '-' }}</td>
                        <td class="text-center">{{ $detail->condition == 'RUSAK_RINGAN' ? '√' : '-' }}</td>
                        <td class="text-center">{{ $detail->condition == 'RUSAK_BERAT' ? '√' : '-' }}</td>
                        <td>{{ $detail->maintenanceCategory->maintenance_category_name }}</td>
                        <td class="text-center">{{ number_format($detail->quantity, 0, ',', '.') }}</td>
                        <td class="text-center">{{ $detail->uom_name }}</td>
                        <td class="text-center">{{ $plan->maintenance_plan_notes ?? '-' }}</td>
                    </tr>
                @endforeach
            @empty
                <tr>
                    <td colspan="14" class="text-center">Tidak ada data</td>
                </tr>
            @endforelse
            </tbody>
        </table>
    </div>
</div>
