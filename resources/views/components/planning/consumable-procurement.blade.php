<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-3">
            <strong class="d-block">Tanggal Dibuat</strong>
            {{ date('d/m/Y', strtotime($plan->created_at)) }}
        </div>
        <div class="col-md-3">
            <strong class="d-block">Tahun Perencanaan</strong>
            {{ date('Y', strtotime($plan->request_date)) }}
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('planning.consumable-procurement.export', $plan->id) }}" class="btn btn-success btn-sm">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <strong class="d-block">Program</strong>
            {{ $plan->program->program_name }}
        </div>
        <div class="col-md-6">
            <strong class="d-block"><PERSON><PERSON>an</strong>
            <small class="text-muted">{{ $plan->room->room_code }}</small> - {{ $plan->room->room_name }}
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Program/Kegiatan/Output</th>
                    <th>Asset</th>
                    <th>Satuan</th>
                    <th>Quantity</th>
                </tr>
            </thead>

            <tbody>
                @php
                    // Group data by program hierarchy
                    $groupedData = [];
                    foreach ($plan->planDetails as $planDetail) {
                        $groupPrograms = $planDetail->planDetailPrograms()
                            ->where('program_type', 'GROUP')
                            ->orderBy('level')
                            ->get();
                        
                        $outputProgram = $planDetail->planDetailPrograms()
                            ->where('program_type', 'OUTPUT')
                            ->first();

                        // Create a unique key for the program hierarchy
                        $key = '';
                        foreach ($groupPrograms as $group) {
                            $key .= $group->program_code;
                        }
                        $key .= $outputProgram ? $outputProgram->program_code : '';

                        if (!isset($groupedData[$key])) {
                            $groupedData[$key] = [
                                'groups' => $groupPrograms,
                                'output' => $outputProgram,
                                'items' => []
                            ];
                        }

                        $groupedData[$key]['items'][] = $planDetail;
                    }
                @endphp

                @php $no = 1; @endphp
                @foreach($groupedData as $data)
                    {{-- Program Groups --}}
                    @foreach($data['groups'] as $group)
                        <tr>
                            <td></td>
                            <td>
                                <div class="mb-1">
                                    <small class="text-muted">{{ $group->program_code }}</small>
                                    <br>
                                    {{ $group->program_name }}
                                </div>
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    @endforeach

                    {{-- Program Output --}}
                    @if($data['output'])
                        <tr>
                            <td></td>
                            <td>
                                <div class="mb-1">
                                    <small class="text-muted">{{ $data['output']->program_code }}</small>
                                    <br>
                                    {{ $data['output']->program_name }}
                                </div>
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    @endif

                    {{-- Items under this program --}}
                    @foreach($data['items'] as $planDetail)
                        <tr>
                            <td>{{ $no++ }}</td>
                            <td></td>
                            <td>
                                <span class="text-danger d-block">{{ $planDetail->code ?? "-" }}</span>
                                <span>{{ $planDetail->name }}</span>
                            </td>
                            <td>{{ $planDetail->uom_name }}</td>
                            <td>{{ $planDetail->quantity }}</td>
                        </tr>
                    @endforeach
                @endforeach
            </tbody>
        </table>
    </div>
</div>