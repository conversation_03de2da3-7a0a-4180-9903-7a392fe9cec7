<div class="table-responsive">
    <div class="text-center mb-4">
        <h4 class="mb-1">USULAN RENCANA KEBUTUHAN PENGADAAN BARANG MILIK DAERAH</h4>
        <h5 class="mb-1">(RENCANA PENGADAAN)</h5>
        <h5 class="mb-1">{{ config('app.report_header_hospital') }}</h5>
        <h5>TAHUN {{ request()->year }}</h5>
    </div>
    <table class="table table-bordered table-hover">
        <thead>
        <tr class="align-middle text-center">
            <th rowspan="2" style="width: 3%">No.</th>
            <th rowspan="2" style="width: 20%">Program/Kegiatan/Output</th>
            {{-- Usulan Barang Milik Daerah --}}
            <th colspan="4" style="width: 20%">Usulan Barang Milik Daerah</th>
            {{-- <PERSON><PERSON><PERSON><PERSON> --}}
            <th colspan="2" style="width: 10%"><PERSON><PERSON><PERSON><PERSON></th>
            {{-- Data Barang <PERSON> Dapa<PERSON> Dioptimal<PERSON> --}}
            <th colspan="4" style="width: 20%">Data Barang Yang Dapat Dioptimalisasikan</th>
            {{-- Kebutuhan Riil Barang Milik Daerah --}}
            <th colspan="2" style="width: 10%">Kebutuhan Riil Barang Milik Daerah</th>
            {{-- Rencana Kebutuhan BMD Yang Disetujui --}}
            <th colspan="2" style="width: 10%">Rencana Kebutuhan Pengadaan BMD Yang Disetujui</th>
            <th rowspan="2" style="width: 5%">Cara Pemenuhan</th>
            <th rowspan="2" style="width: 5%">Ket</th>
        </tr>
        <tr class="text-center">
            {{-- Usulan Barang Milik Daerah --}}
            <th>Kode Barang</th>
            <th>Nama Barang</th>
            <th>Jumlah</th>
            <th>Satuan</th>
            {{-- Kebutuhan Maksimum --}}
            <th>Jumlah</th>
            <th>Satuan</th>
            {{-- Data Barang Yang Dapat Dioptimalisasikan --}}
            <th>Kode Barang</th>
            <th>Nama Barang</th>
            <th>Jumlah</th>
            <th>Satuan</th>
            {{-- Kebutuhan Riil Barang Milik Daerah --}}
            <th>Jumlah</th>
            <th>Satuan</th>
            {{-- Rencana Kebutuhan BMD Yang Disetujui --}}
            <th>Jumlah</th>
            <th>Satuan</th>
        </tr>
        </thead>
        <tbody>
        @php
            $groupedData = [];
            foreach ($data as $plan) {
                foreach ($plan->planDetails as $planDetail) {
                    $groupPrograms = $planDetail->planDetailPrograms()
                        ->where('program_type', 'GROUP')
                        ->orderBy('level')
                        ->get();

                    $outputProgram = $planDetail->planDetailPrograms()
                        ->where('program_type', 'OUTPUT')
                        ->first();

                    $key = '';
                    foreach ($groupPrograms as $group) {
                        $key .= $group->program_code;
                    }
                    $key .= $outputProgram ? $outputProgram->program_code : '';

                    if (!isset($groupedData[$key])) {
                        $groupedData[$key] = [
                            'groups' => $groupPrograms,
                            'output' => $outputProgram,
                            'items' => []
                        ];
                    }

                    // Create unique key for items based on code and uom
                    $itemKey = $planDetail->code . '_' . ($planDetail->uom ? $planDetail->uom->id : 'null');

                    if (!isset($groupedData[$key]['items'][$itemKey])) {
                        $groupedData[$key]['items'][$itemKey] = [
                            'code' => $planDetail->code,
                            'name' => $planDetail->name,
                            'quantity' => 0,
                            'uom' => $planDetail->uom
                        ];
                    }

                    // Sum the quantities
                    $groupedData[$key]['items'][$itemKey]['quantity'] += $planDetail->quantity;
                }
            }
            $no = 1;
        @endphp

        @forelse($groupedData as $data)
            {{-- Program Groups --}}
            @foreach($data['groups'] as $group)
                <tr class="table-light">
                    <td></td>
                    <td>{{ $group->program_code }} - {{ $group->program_name }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            @endforeach

            {{-- Output Program --}}
            @if($data['output'])
                <tr class="table-light">
                    <td></td>
                    <td>{{ $data['output']->program_code }} - {{ $data['output']->program_name }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            @endif

            {{-- Items --}}
            @foreach($data['items'] as $item)
                <tr>
                    <td class="text-center">{{ $no++ }}</td>
                    <td></td>
                    {{-- Usulan Barang Milik Daerah --}}
                    <td class="text-center">{{ $item['code'] ?? '-' }}</td>
                    <td>{{ $item['name'] }}</td>
                    <td class="text-center">{{ number_format($item['quantity'], 0, ',', '.') }}</td>
                    <td class="text-center">{{ $item['uom'] ? $item['uom']->uom_name : '-' }}</td>
                    {{-- Kebutuhan Maksimum --}}
                    <td class="text-center">{{ number_format($item['quantity'], 0, ',', '.') }}</td>
                    <td class="text-center">{{ $item['uom'] ? $item['uom']->uom_name : '-' }}</td>
                    {{-- Data Barang Yang Dapat Dioptimalisasikan --}}
                    <td class="text-center">{{ $item['code'] ?? '-' }}</td>
                    <td>{{ $item['name'] }}</td>
                    <td class="text-center">{{ number_format($item['quantity'], 0, ',', '.') }}</td>
                    <td class="text-center">{{ $item['uom'] ? $item['uom']->uom_name : '-' }}</td>
                    {{-- Kebutuhan Riil Barang Milik Daerah --}}
                    <td class="text-center">{{ number_format($item['quantity'], 0, ',', '.') }}</td>
                    <td class="text-center">{{ $item['uom'] ? $item['uom']->uom_name : '-' }}</td>
                    {{-- Rencana Kebutuhan BMD Yang Disetujui --}}
                    <td class="text-center">{{ number_format($item['quantity'], 0, ',', '.') }}</td>
                    <td class="text-center">{{ $item['uom'] ? $item['uom']->uom_name : '-' }}</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                </tr>
            @endforeach
        @empty
            <tr>
                <td colspan="18" class="text-center">Tidak ada data</td>
            </tr>
        @endforelse
        </tbody>
    </table>
</div>
