<a href="#modal-upload-doc" class="btn btn-primary btn-modern-rounded" data-bs-toggle="modal">
    <i class="fas fa-upload me-1"></i>
    <span>Upload Dokumen</span>
</a>

<div class="modal fade" id="modal-upload-doc">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h4 class="modal-title fw-semibold">Upload Dokumen</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <form action="{{ route('asset-management.asset-document.upload') }}" method="post" id="form-upload-doc"
                enctype="multipart/form-data">
                <div class="modal-body px-4">
                    @csrf
                    
                    <!-- Form Configuration Section -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-transparent border-0 py-3">
                            <h6 class="mb-0 fw-semibold text-primary">
                                <i class="fas fa-cog me-2"></i>Konfigurasi Upload
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-medium text-muted small">Tipe Dokumen</label>
                                    <select name="tipe" id="tipe" class="form-select form-select-sm border-0 bg-light">
                                        <option disabled selected>Pilih Tipe</option>
                                        <option value="checklist">BAST Pengadaan</option>
                                        <option value="penempatan">BAST Penempatan</option>
                                        <option value="mutasi">BAST Mutasi</option>
                                        <option value="pembayaran">BAST Pembayaran</option>
                                        <option value="rusak">BAST Aset Rusak</option>
                                        <option value="penempatan_lama">BAST Penempatan Lama</option>
                                    </select>
                                    @error('tipe')
                                        <span class="d-block text-danger small mt-1">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="col-md-6 d-none" id="document_code_field">
                                    <label class="form-label fw-medium text-muted small">Kode Dokumen / No. Dokumen</label>
                                    <input type="text" name="kode_dokumen" id="kode_dokumen" 
                                           class="form-control form-control-sm border-0 bg-light text-uppercase"
                                           placeholder="Masukkan kode dokumen dan tekan Enter">
                                    <small class="text-secondary">*Tekan enter untuk memunculkan asset</small>
                                    @error('kode_dokumen')
                                        <span class="d-block text-danger small mt-1">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="col-md-6 d-none" id="document_sub_type_field">
                                    <label class="form-label fw-medium text-muted small">Tipe Sub Dokumen</label>
                                    <select name="document_sub_type" id="document_sub_type" 
                                            class="form-select form-select-sm border-0 bg-light">
                                        <option disabled selected>Pilih Tipe Sub Dokumen</option>
                                        <option value="FAKTUR">Faktur Pembelian/Invoice</option>
                                        <option value="SJ">Surat Jalan/Delivery Order/Tanda Terima</option>
                                        <option value="COO">COO</option>
                                        <option value="SERTIFIKAT">Sertifikat/Kartu Garansi</option>
                                        <option value="BA">BA Uji Fungsi</option>
                                        <option value="REGISTER">Registrasi dari Depkes</option>
                                    </select>
                                    @error('document_sub_type')
                                        <span class="d-block text-danger small mt-1">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="col-md-12 d-none" id="document_field">
                                    <label class="form-label fw-medium text-muted small">Dokumen</label>
                                    <input type="file" name="dokumen" id="dokumen" accept="application/pdf"
                                           class="form-control form-control-sm border-0 bg-light">
                                    @error('dokumen')
                                        <span class="d-block text-danger small mt-1">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="col-md-12 d-none" id="ruangan_field">
                                    <label class="form-label fw-medium text-muted small">Ruangan</label>
                                    <select name="ruangan_upload" id="ruangan_upload" 
                                            class="form-select form-select-sm border-0 bg-light ruangan-upload">
                                        <option disabled selected>Pilih Ruangan</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Asset List Section - Only show when table is visible -->
                    <div class="card border-0 shadow-sm d-none" id="asset-list-section">
                        <div class="card-header bg-transparent border-0 py-3">
                            <h6 class="mb-0 fw-semibold text-primary">
                                <i class="fas fa-list me-2"></i>Daftar Asset
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0 modern-table d-none" id="table-upload">
                                    <thead class="bg-light">
                                        <tr>
                                            <th class="border-0 py-3 px-4 fw-medium text-muted small" style="width: 20%;">Kode Asset</th>
                                            <th class="border-0 py-3 px-4 fw-medium text-muted small" style="width: 15%;">Kode QR</th>
                                            <th class="border-0 py-3 px-4 fw-medium text-muted small" style="width: 20%;">Kode Register / SN</th>
                                            <th class="border-0 py-3 px-4 text-center fw-medium text-muted small" style="width: 35%;">Foto</th>
                                            <th class="border-0 py-3 px-4 text-center fw-medium text-muted small" style="width: 60px !important;">#</th>
                                        </tr>
                                    </thead>
                                    <tbody id="table-upload-doc" class="border-0"></tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-3 pb-3">
                            <div class="d-flex">
                                <button type="button" class="btn btn-sm btn-outline-primary btn-modern-rounded d-none" id="btn-add-row-upload">
                                    <i class="fas fa-plus me-1"></i>
                                    <span>Tambah Asset</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-outline-primary btn-modern-rounded me-1" id="btn-close-upload" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        <span>Batal</span>
                    </button>
                    <button type="submit" class="btn btn-primary btn-modern-rounded" id="btn-upload-doc">
                        <i class="fas fa-upload me-1"></i>
                        <span>Upload Dokumen</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.modern-table {
    border: none;
}

.modern-table thead th {
    border: none;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.modern-table tbody td {
    border: none;
    padding: 12px 16px;
    vertical-align: middle;
}

.modern-table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.modern-table tbody tr:not(:last-child) {
    border-bottom: 1px solid #f1f3f4;
}

.modern-table tbody tr.asset-row {
    transition: all 0.2s ease;
}

.form-select-sm, .form-control-sm {
    height: 38px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-select-sm:focus, .form-control-sm:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
    border-color: #007bff;
    background-color: #fff !important;
}

.card {
    border-radius: 12px;
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

.card-footer {
    border-radius: 0 0 12px 12px !important;
    background: #fafbfc;
}

.modal-content {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-radius: 16px 16px 0 0;
}

.modal-footer {
    border-radius: 0 0 16px 16px;
}

.btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
    background: transparent;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.text-primary {
    color: #007bff !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

.fw-medium {
    font-weight: 500 !important;
}

.text-muted {
    color: #6c757d !important;
}

.small {
    font-size: 0.875em !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* File input styling */
.form-control[type="file"] {
    padding: 8px 12px;
    line-height: 1.5;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    margin-right: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background: #0056b3;
}

/* Error styling */
.text-danger {
    color: #dc3545 !important;
}

.text-danger.small {
    font-size: 0.75em;
    margin-top: 4px;
    display: block;
}

/* Button styling consistency */
.btn-modern-rounded {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn-modern-rounded:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-outline-primary {
    border-color: #007bff;
    color: #007bff;
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: #fff;
}

/* Table column width optimization */
/* .modern-table th:nth-child(4) {
    min-width: 200px;
}

.modern-table td:nth-child(4) {
    min-width: 200px;
} */

/* File input in table styling */
.modern-table .form-control[type="file"] {
    font-size: 12px;
    padding: 6px 8px;
}

.modern-table .form-control[type="file"]::-webkit-file-upload-button {
    font-size: 11px;
    padding: 4px 8px;
}

/* Helper text styling */
.text-secondary {
    color: #6c757d !important;
    font-size: 0.75em;
    margin-top: 4px;
    display: block;
}

/* Error styling for upload form */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    animation: shake 0.5s ease-in-out;
}

.is-invalid:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    background-color: #fff !important;
}

.border-danger {
    border: 2px solid #dc3545 !important;
    border-radius: 8px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Alert styling in modal */
.modal-body .alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 20px;
    animation: slideInDown 0.3s ease-out;
}

.modal-body .alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.15);
}

.modal-body .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
}

.modal-body .alert .btn-close {
    opacity: 0.7;
    transition: opacity 0.2s;
}

.modal-body .alert .btn-close:hover {
    opacity: 1;
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Success state styling */
.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.is-valid:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    background-color: #fff !important;
}

/* Loading state styling */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Input loading state */
.form-control:disabled {
    background-color: #e9ecef !important;
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control:disabled:focus {
    box-shadow: none !important;
    border-color: #ced4da !important;
}

/* Focus state styling */
.form-control:focus:not(.is-invalid) {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15) !important;
    background-color: #fff !important;
}

.form-select:focus:not(.is-invalid) {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15) !important;
    background-color: #fff !important;
}
</style>
