<div id="sidebar" class="app-sidebar" style="box-shadow: 2px 0 8px rgba(53, 53, 53, 0.1);">
    <div class="app-sidebar-content" data-scrollbar="true" data-height="100%">
        <div class="menu">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <img src="/img/logo/pt_agos.jpg" style="width:30px">
                    <div class="brand-text">
                        <h3>{{ config('app.app_sider_name') }}</h3>
                        <small>{{ config('app.app_sider_description') }}</small>
                    </div>
                </div>
            </div>
            <div id="appSidebarProfileMenu" class="collapse">
                <div class="menu-item pt-5px">
                    <a href="javascript:;" class="menu-link">
                        <div class="menu-icon"><i class="fa fa-user"></i></div>
                        <div class="menu-text">Edit Profile</div>
                    </a>
                </div>
                <div class="menu-divider m-0"></div>
            </div>
            <div class="menu-header">Navigation</div>

            <div class="menu-item {{ request()->routeIs('home.index') ? 'active' : '' }}">
                <a href="{{ route('home.index') }}" class="menu-link">
                    <div class="menu-icon">
                        <i class="fa fa-home"></i>
                    </div>
                    <div class="menu-text">Home</div>
                </a>
            </div>
            @stack("menu")

            <div class="menu-item d-flex">
                <a href="javascript:;" class="app-sidebar-minify-btn ms-auto" data-toggle="app-sidebar-minify"><i class="fa fa-angle-double-left"></i></a>
            </div>

        </div>
    </div>
</div>
<div class="app-sidebar-bg"></div>
<div class="app-sidebar-mobile-backdrop"><a href="#" data-dismiss="app-sidebar-mobile" class="stretched-link"></a></div>


<style>
    .app-sidebar {
        background-color: #fafafa !important;
    }

    .app-sidebar .menu .menu-item .menu-link .menu-text {
        color: #000 !important;
    }

    .menu-item.has-sub .menu-caret {
        color: #000 !important;
    }

    .app-sidebar .menu .menu-item.active>.menu-link .menu-icon {
        color: #3983f4 !important;
    }

    .app-sidebar .menu .menu-item .menu-icon {
        color: #000 !important;
    }

    .app-sidebar .menu .menu-header {
        color: #000 !important;
    }

    .app-sidebar .app-sidebar-minify-btn {
        background-color: #3983f4 !important;
    }

    .app-sidebar .menu .menu-item>.menu-link {
        background: #fafafa !important;
        color: #fff !important;
        margin-left: 5px;
        margin-right: 5px;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .app-sidebar .menu .menu-item>.menu-link:hover {
        background: #e5e7eb !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transform: translateY(-1px);
    }

    .app-sidebar .menu .menu-item.active>.menu-link {
        background: #3983f4 !important;
        color: #fff !important;
        margin-left: 5px;
        margin-right: 5px;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(57, 131, 244, 0.3) !important;
        transform: translateY(-1px);
        transition: all 0.3s ease;
    }

    .app-sidebar .menu .menu-item.active>.menu-link .menu-text {
        color: #fff !important;
    }

    .app-sidebar .menu .menu-item.active>.menu-link .menu-icon {
        color: #fff !important;
    }

    /* Modern Sidebar Header Styles */
    .sidebar-header {
        background: #ffffff;
        border-bottom: 1px solid #e5e7eb;
        border-top: 1px solid #e5e7eb;
        padding: 1.5rem 1rem;
        margin-bottom: 0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .sidebar-brand {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .brand-text h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e3c72;
        letter-spacing: 0.05em;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .brand-text small {
        color: #6b7280;
        font-size: 0.75rem;
        font-weight: 500;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .sidebar-header {
            padding: 1rem 0.75rem;
        }
        
        .brand-text h3 {
            font-size: 1rem;
        }
        
        .brand-icon {
            width: 35px;
            height: 35px;
            font-size: 1.1rem;
        }
        .modern-breadcrumb{
            display: none;
        }
    }

    @media (min-width: 768px) {
        .app-content {
            margin-left: 250px;
        }

        .app-sidebar {
            width: 250px;
        }

    }
</style>