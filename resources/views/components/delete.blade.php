<form action="" method="post" id="form-delete">
    @csrf
    @method("DELETE")
</form>

<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script>
    $("#datatable").on("click", ".btn-delete", function() {
        swal({
                title: 'Hapus Data?',
                text: 'Menghapus data tidak bersifat permanen!',
                icon: 'warning',
                buttons: {
                    cancel: {
                        text: 'Batal',
                        value: null,
                        visible: true,
                        className: 'btn btn-default',
                        closeModal: true,
                    },
                    confirm: {
                        text: 'Hapus',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            })
            .then((isConfirm) => {
                if (isConfirm) {
                    let route = $(this).data("route");
                    $("#form-delete").attr("action", route);
                    $("#form-delete").submit();
                }
                $("#form-delete").attr("action", "");
            });
    });
</script>