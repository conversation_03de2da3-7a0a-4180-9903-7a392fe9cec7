<div class="table-responsive">
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>#</th>
                <th>Kode Aset</th>
                <th>Register Code</th>
                <th>QR Code</th>
                <th>Serial Number</th>
                <th><PERSON>a Aset</th>
                <th>Satu<PERSON></th>
                <th>Harga</th>
                <th><PERSON>hun <PERSON></th>
                <th>Aturan Penyusutan (tahun)</th>
                <th>Masa <PERSON></th>
                <th>Nilai Penyusutan</th>
                <th><PERSON><PERSON> Buku</th>
            </tr>
        </thead>
        <tbody>
            @forelse($data as $key => $item)
                @php
                    $receivedDate = \Carbon\Carbon::parse($item->assetEntry->received_date);
                    $now = \Carbon\Carbon::now();
                    $usedYears = $now->diffInYears($receivedDate);
                    
                    // Perhitungan penyusutan
                    if ($item->depreciation_year > 0) {
                        $depreciationPerYear = $item->unit_price / $item->depreciation_year;
                        $totalDepreciation = $usedYears * $depreciationPerYear;
                        
                        // Jika masa manfaat melebihi aturan penyusutan, nilai buku = 0
                        if ($usedYears >= $item->depreciation_year) {
                            $totalDepreciation = $item->unit_price;
                            $bookValue = 0;
                        } else {
                            $bookValue = $item->unit_price - $totalDepreciation;
                        }
                    } else {
                        // Jika depreciation_year = 0, tidak ada penyusutan
                        $depreciationPerYear = 0;
                        $totalDepreciation = 0;
                        $bookValue = $item->unit_price;
                    }
                @endphp
                <tr>
                    <td>{{ $key + 1 }}</td>
                    <td>{{ $item->asset_code }}</td>
                    <td>{{ $item->register_code }}</td>
                    <td>{{ $item->qr_code }}</td>
                    <td>{{ $item->serial_number }}</td>
                    <td>{{ $item->asset_name }}</td>
                    <td>{{ $item->uom_name }}</td>
                    <td>{{ number_format($item->unit_price, 0, ',', '.') }}</td>
                    <td>{{ $receivedDate->format('Y') }}</td>
                    <td>{{ $item->depreciation_year }}</td>
                    <td>{{ $usedYears }} tahun</td>
                    <td>{{ number_format($totalDepreciation, 0, ',', '.') }}</td>
                    <td>{{ number_format($bookValue, 0, ',', '.') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="13" class="text-center">Tidak ada data</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div> 