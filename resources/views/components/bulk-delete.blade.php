<form action="{{ $action }}" method="post" id="form-bulk-delete">
    @csrf
</form>

<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script>
    $(document).on("click", "#btn-bulk", function() {
        swal({
                title: 'Hapus Data Terpilih?',
                text: 'Menghapus data tidak bersifat permanen!',
                icon: 'warning',
                buttons: {
                    cancel: {
                        text: 'Batal',
                        value: null,
                        visible: true,
                        className: 'btn btn-default',
                        closeModal: true,
                    },
                    confirm: {
                        text: 'Hapus',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            })
            .then((isConfirm) => {
                if (isConfirm) {
                    let route = $(this).data("route");
                    $("#form-bulk-delete").submit();
                }
                $("#form-bulk-delete").attr("action", "");
            });
    });

    function toggleBulkButton() {
        const form = $("#form-bulk-delete");
        form.find('input[type="hidden"].checkbox-input').remove();

        const checkedCheckboxes = $("#datatable").find(".checkbox-id:checked");

        if (checkedCheckboxes.length > 0) {
            $("#btn-bulk").removeClass("d-none");
            $("#btn-alokasi").removeClass("d-none");
            $("#btn-export").removeClass("d-none");
            checkedCheckboxes.each(function() {
                const itemId = $(this).val();
                form.append(`<input type="hidden" name="id[]" value="${itemId}" class="checkbox-input">`);
            });
        } else {
            $("#btn-bulk").addClass("d-none");
            $("#btn-alokasi").addClass("d-none");
            $("#btn-export").addClass("d-none");
        }
    }

    $("#datatable").on("click", "#check-all", function() {
        const isChecked = $(this).is(":checked");
        $("#datatable").find(".checkbox-id").prop("checked", isChecked);
        toggleBulkButton();
    });

    $("#datatable").on("click", ".checkbox-id", function() {
        const allChecked = $("#datatable").find(".checkbox-id").length === $("#datatable").find(".checkbox-id:checked").length;
        $("#check-all").prop("checked", allChecked);
        toggleBulkButton();
    });
</script>