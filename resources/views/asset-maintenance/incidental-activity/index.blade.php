@extends("layouts.app")

@push("style")
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
    @include("menu.asset_maintenance")
@endpush

@section("content")
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <div class="">
                    <select name="target_ruangan" id="target_ruangan" class="form-select target_ruangan" style="width: 320px;">
                        <option value="">Se<PERSON><PERSON></option>
                    </select>
                </div>
            </div>
            <div class="datatable-action-buttons">
                <div class="datatable-filter-icon btn-modern-rounded">
                    <i class="fas fa-filter"></i>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
                <thead>
                <tr>
                    <th>Kode Request</th>
                    <th>Tanggal Request / Ruangan</th>
                    <th>Asset</th>
                    <th>Jenis Kerusakan</th>
                    <th>Status</th>
                    <th style="width: 80px;">Action</th>
                </tr>
                </thead>
            </table>
        </div>

        <div class="datatable-custom-pagination">
            <div class="datatable-rows-per-page">
                <label>Show:</label>
                <select id="rowsPerPage">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
        </div>
    </div>

    <!-- Filter Drawer -->
    <div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
        <div class="modal-dialog modal-drawer" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">Tanggal Mulai</label>
                        <input type="date" name="filter_date_start" id="filter_date_start" class="form-control">
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Tanggal Akhir</label>
                        <input type="date" name="filter_date_end" id="filter_date_end" class="form-control">
                    </div>
                    <div class="d-flex gap-2 mt-4">
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFilter">
                            <i class="fas fa-undo me-1"></i>Reset Filter
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="applyFilter">
                            <i class="fas fa-filter me-1"></i>Terapkan Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Detail -->
    <div class="modal fade" id="modal-dialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Detail Request</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div id="activityBody" class="modal-body"></div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Tutup</a>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Detail -->

    <!-- Modal Request BHP -->
    <div class="modal fade" id="modal-request-bhp">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Request BHP</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    Catatan Request BHP:
                    <textarea id="request_bhp_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-request-bhp" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Data</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Request BHP -->

    <!-- Modal Vendor Result -->
    <div class="modal fade" id="modal-vendor-result">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Hasil Perbaikan Vendor</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    Hasil Terkait Perbaikan Vendor:
                    <select id="final_vendor_result_select" class="form-control">
                        <option value="VENDOR_FIXED">Vendor Berhasil Memperbaiki</option>
                        <option value="VENDOR_NOT_FIXED">Vendor Gagal Memperbaiki</option>
                    </select>
                    <br>

                    Catatan Terkait Perbaikan Vendor:
                    <textarea id="vendor_result_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-vendor-result" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Data</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Vendor Result -->

    <!-- Modal Inspection Result -->
    <div class="modal fade" id="modal-inspection-result">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Hasil Pengecekan</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <button type="button" id="btn-add-inspection-result" class="btn btn-outline-primary me-1"><i class="fas fa-plus-circle me-1"></i>Tambah BHP</button>
                    </div>

                    Hasil Terkait Pengecekan:
                    <select id="inspection_result_select" class="form-control">
                        <option value="ASSIGN_FIXED">Berhasil Diperbaiki</option>
                        <option value="ASSIGN_NOT_FIXED">Gagal Diperbaiki</option>
                    </select>
                    <br>

                    <div class="table-responsive mb-3 show-bhp">
                        <table class="table table-bordered table-striped w-100 mb-3">
                            <thead>
                            <tr>
                                <th style="min-width: 300px !important;">Nama</th>
                                <th style="min-width: 300px !important;">Harga</th>
                                <th style="min-width: 300px !important;">Qty</th>
                            </tr>
                            </thead>
                            <tbody id="table-inspection-result"></tbody>
                        </table>
                    </div>
                    Catatan Penugasan:
                    <textarea id="inspection_result_notice" style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan / Deskripsi"></textarea>
                    <br>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-white" data-bs-dismiss="modal">Tutup</button>
                    <button id="save-inspection-result" class="btn btn-primary"><i class="fas fa-save me-1"></i>Update Data</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End of Modal Inspection Result -->

@endsection

@push("script")
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/app.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/incidentalActivity.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/incidentalActivityAction.js') }}"></script>
@endpush
