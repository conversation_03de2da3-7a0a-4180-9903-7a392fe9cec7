@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_report_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.asset_maintenance')
@endpush

@section('content')
    <div class="report-card">
        <div class="report-header">
            <h4>{{ $title ?? 'Laporan Log Insidental' }}</h4>
        </div>

        <div class="report-body">
            <div class="d-flex justify-content-end mb-4">
                <button id="btn_export_excel" data-export-filename="Laporan Insidental"
                        data-export-url="{{ route('maintenance-asset.laporan.report.export_incidental') }}"
                        data-params="{'start_date': '', 'end_date': '', 'room_id': ''}"
                        class="btn btn-export" disabled>
                    <i class="fa fa-file-excel"></i>
                    Export Excel
                </button>
            </div>

            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group report_qr">
                        <label><i>Ruangan</i></label>
                        <select id="room_id" class="form-select room_id"></select>
                    </div>

                    <div class="filter-group">
                        <label><i>Periode</i></label>
                        <input id="datepicker" type="text" class="form-control" readonly="readonly"
                            style="background-color:white; cursor:pointer;">
                    </div>

                    <div class="filter-group">
                        <button type="button" id="btn_show_report" class="btn btn-show-data">
                            <i class="fas fa-search me-2"></i>
                            Tampilkan Data
                        </button>
                    </div>
                </div>
            </div>

            <div class="target-report" id="target-report">
                <!-- Empty State - akan ditampilkan saat awal -->
                <div class="empty-state" id="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="empty-state-title">
                        Mulai Analisis Data Log Insidental
                    </div>
                    <div class="empty-state-description">
                        Pilih ruangan dan periode yang ingin Anda analisis, kemudian klik "Tampilkan Data" untuk melihat
                        laporan detail log insidental.
                    </div>

                    <div class="empty-state-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">Pilih Ruangan</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">Pilih Periode</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">Klik Tampilkan Data</div>
                        </div>
                    </div>
                </div>
                
                <!-- Container untuk hasil report - ID ini diperlukan untuk JavaScript -->
                <div id="report_page"></div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/app.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/report.js') }}"></script>

    <script>
        // Override the existing click handler to hide empty state
        $(document).ready(function() {
            // Remove existing click handler and add new one
            $('#btn_show_report').off('click').on('click', function() {
                $('#empty-state').hide();
                // Trigger the original functionality from report.js
                $('#btn_show_report').prop('disabled', true);
                $('#report_page').html("LOADING...");
                setTimeout(function () {
                    $.ajax({
                        type: 'get',
                        dataType: 'html',
                        async: false,
                        data: {
                            type: 'report',
                            filter: 'period',
                            start_date: moment($('#datepicker').data('daterangepicker')?.startDate.unix('x') * 1000).format("YYYY-MM-DD"),
                            end_date: moment($('#datepicker').data('daterangepicker')?.endDate.unix('x') * 1000).format("YYYY-MM-DD"),
                            room_id: $('#room_id').val(),
                        },
                        success: function (r) {
                            $('#btn_show_report').prop('disabled', false);
                            $('#report_page').html(r);
                            $('#btn_export_excel').prop('disabled', false);
                        },
                        error: function (xhr) {
                            $('#btn_show_report').prop('disabled', false);
                            console.log(xhr.responseText);
                        }
                    });
                }, 50);
            });
        });
    </script>
@endpush
