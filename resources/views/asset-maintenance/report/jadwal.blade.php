@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_report_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.asset_maintenance')
@endpush

@section('content')
    <div class="report-card">
        <div class="report-header">
            <h4>{{ $title ?? 'Laporan <PERSON>wal Maintenance' }}</h4>
        </div>

        <div class="report-body">
            <div class="d-flex justify-content-end mb-4">
                <button id="btn_export_excel" data-export-url="export-jadwal"
                        data-export-filename="Laporan Jadwal"
                        data-params="{'year': '', 'maintenance_category': '', 'maintenance_type': ''}"
                        class="btn btn-export" disabled>
                    <i class="fa fa-file-excel"></i>
                    Export Excel
                </button>
            </div>

            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label><i>Kategori</i></label>
                        <select name="maintenance_category" id="maintenance_category" class="form-select asal-perolehan">
                            <option value="ALKES">Alat Kesehatan</option>
                            <option value="NON_ALKES">Non Alat Kesehatan</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label><i>Tipe</i></label>
                        <select name="maintenance_type" id="maintenance_type" class="form-select asal-perolehan">
                            <option value="" selected>Semua Kategori</option>
                            <option value="KALIBRASI">Kalibrasi</option>
                            <option value="MAINTENANCE">Maintenance</option>
                            <option value="UJI_FUNGSI">Uji Fungsi</option>
                            <option value="UJI_KESESUAIAN">Uji Kesesuaian</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label><i>Periode</i></label>
                        <select id="yearpicker" name="yearpicker" class="form-select">
                            @php
                                $currentYear = date('Y');
                                $startYear = $currentYear - 4;
                                $endYear = $currentYear + 3;
                            @endphp
                            @for ($year = $startYear; $year <= $endYear; $year++)
                                <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>
                                    {{ $year }}
                                </option>
                            @endfor
                        </select>
                    </div>

                    <div class="filter-group">
                        <button type="button" id="btn_show_report" class="btn btn-show-data">
                            <i class="fas fa-search me-2"></i>
                            Tampilkan Data
                        </button>
                    </div>
                </div>
            </div>

            <div class="target-report" id="target-report">
                <!-- Empty State - akan ditampilkan saat awal -->
                <div class="empty-state" id="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="empty-state-title">
                        Mulai Analisis Data Jadwal Maintenance
                    </div>
                    <div class="empty-state-description">
                        Pilih kategori, tipe, dan periode yang ingin Anda analisis, kemudian klik "Tampilkan Data" untuk melihat
                        laporan detail jadwal maintenance.
                    </div>

                    <div class="empty-state-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">Pilih Kategori</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">Pilih Tipe</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">Pilih Periode</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-text">Klik Tampilkan Data</div>
                        </div>
                    </div>
                </div>
                
                <!-- Container untuk hasil report - ID ini diperlukan untuk JavaScript -->
                <div id="report_page"></div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/app.js') }}"></script>
    <script src="{{ asset('/js/app/asset-maintenance/report.js') }}"></script>

    <script>
        // Override the existing click handler to hide empty state
        $(document).ready(function() {
            // Remove existing click handler and add new one
            $('#btn_show_report').off('click').on('click', function() {
                $('#empty-state').hide();
                // Trigger the original functionality from report.js
                $('#btn_show_report').prop('disabled', true);
                $('#report_page').html("LOADING...");
                setTimeout(function () {
                    $.ajax({
                        type: 'get',
                        dataType: 'html',
                        async: false,
                        data: {
                            type: 'report',
                            filter: 'period',
                            year: $('#yearpicker').val(),
                            maintenance_category: $('#maintenance_category').val(),
                            maintenance_type: $('#maintenance_type').val(),
                        },
                        success: function (r) {
                            $('#btn_show_report').prop('disabled', false);
                            $('#report_page').html(r);
                            $('#btn_export_excel').prop('disabled', false);
                        },
                        error: function (xhr) {
                            $('#btn_show_report').prop('disabled', false);
                            console.log(xhr.responseText);
                        }
                    });
                }, 50);
            });
        });
    </script>
@endpush
