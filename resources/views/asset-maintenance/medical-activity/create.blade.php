@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.asset_maintenance')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <form action="" method="post" id="form-activity">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <div class="text-sm-left">
                                Tipe Pem<PERSON>hraan
                                <select name="maintenance_type" id="maintenance_type" class="form-select asal-perolehan">
                                    <option value="" selected disabled>Pilih Tipe</option>
                                    <option value="KALIBRASI">KALIBRASI</option>
                                    <option value="MAINTENANCE">MAINTENANCE</option>
                                    <option value="UJI_FUNGSI">UJI FUNGSI</option>
                                    <option value="UJI_KESESUAIAN">UJI KESESUAIAN</option>
                                </select>
                                <br>
                                Distributor
                                <select name="distributor" id="distributor" class="form-select distributor w-100">
                                    <option value="">-- Pilih Distributor --</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        {{-- nomor surat --}}
                    </div>
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <div class="text-sm-left">
                                Tanggal Pemelihraan
                                <input type="date" name="activity_date" class="form-control"
                                    value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3 mt-5">
                    <button type="button" id="btn-add-row" class="btn btn-outline-primary me-1"><i
                            class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped w-100 mb-3">
                        <thead>
                            <tr>
                                <th style="max-width: 300px !important;">Data Aset</th>
                                <th>Nama Barang / Kode Registarsi</th>
                                <th>Biaya Pemeliharaan</th>
                                <th>Jenis Pemeliharaan</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="table-allocation"></tbody>
                    </table>
                </div>

                <div class="row my-4">
                    <div class="col-md-3">
                        Catatan:
                        <textarea style="width:100%;;" rows="3" cols="3" name="notes" class="form-control"
                            placeholder="Catatan / Deskripsi"></textarea>
                        <br>
                        Nama Bukti Pemeliharaan:
                        <input type="text" name="document_name" class="form-control" placeholder="BAST: kode/RSUD"
                            value="">
                        <br>
                        <span style="cursor: pointer" id="file_attach"><i class="fa fa-file-pdf"></i> File Lampiran</span>
                        <input type="file" name="document" id="attachment_link" class="form-control">
                    </div>
                    <div class="col-md-6">
                    </div>
                    <div class="col-md-3">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <th>Total Biaya Pemeliharaan:</th>
                                    <td class="text-right text-primary" id="txt_maintenance_cost">0</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="form-group d-flex justify-content-end">
                    <button type="button" id="btn-save" class="btn btn-primary"><i class="fas fa-save me-1"></i>Simpan
                        Form</button>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/createMedicalActivity.js') }}"></script>
@endpush
