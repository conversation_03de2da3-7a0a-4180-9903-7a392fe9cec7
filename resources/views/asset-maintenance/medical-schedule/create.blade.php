@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.asset_maintenance')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <form action="" method="post" id="form-activity">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-3">
                        <div class="mb-6">
                        </div>
                        <div class="mt-4">
                            Tipe Pem<PERSON>hraan
                            <select name="schedule_type" id="schedule_type" class="form-select asal-perolehan">
                                <option value="" selected disabled>Pilih Tipe</option>
                                <option value="KALIBRASI">KALIBRASI</option>
                                <option value="MAINTENANCE">MAINTENANCE</option>
                                <option value="UJI_FUNGSI">UJI FUNGSI</option>
                                <option value="UJI_KESESUAIAN">UJI KESESUAIAN</option>
                            </select>
                            <div class="mt-4">
                                Barang
                                <select name="item_id" id="item_id" class="form-select item_id"></select>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        {{-- nomor surat --}}
                    </div>
                    <div class="col-sm-3">
                        <div class="mb-6">
                            <div class="text-sm-left">
                                Jadwal Pemelihraan
                                <input type="date" name="schedule_date" class="form-control"
                                    value="{{ old('tanggal_pembayaran') ?? now()->format('Y-m-d') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3 mt-5">
                    <button type="button" id="btn-add-row" class="btn btn-outline-primary me-1"><i
                            class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                </div>
                <div class="table-responsive mt-3">
                    <table class="table table-bordered table-striped w-100 mb-3">
                        <thead>
                            <tr>
                                <th style="max-width: 300px !important;">Data Aset</th>
                                <th>Nama Barang / Kode SN</th>
                                <th>Brand / Type</th>
                                <th>Ruangan</th>
                                <th>Catatan</th>
                            </tr>
                        </thead>
                        <tbody id="table-allocation"></tbody>
                    </table>
                </div>

                <div class="row my-4">
                    <div class="col-md-3">
                        Catatan:
                        <textarea style="width:100%;;" rows="3" cols="3" name="notes" class="form-control" placeholder="Catatan"></textarea>
                    </div>
                </div>

                <div class="form-group d-flex justify-content-end">
                    <button type="button" id="btn-save" class="btn btn-primary"><i class="fas fa-save me-1"></i>Simpan
                        Form</button>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/asset-maintenance/createMedicalSchedule.js') }}"></script>
@endpush
