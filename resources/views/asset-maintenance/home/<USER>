@extends("layouts.app")

@push('style')
<link href="{{ asset('/') }}css/app/custom_dashboard.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.asset_maintenance")
@endpush

@section("content")
<div class="row">
    <!-- Card 1: Total Request Perbaikan Open -->
    <div class="col-12">
        <div class="stats-card main-asset-card compact">
            <div class="row">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center mb-3">
                        <div class="stats-icon maintenance me-3 compact">
                            <i class="fa fa-tools"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stats-number compact">{{ number_format($openRequestCount) }}</div>
                            <div class="stats-description">Total request perbaikan yang masih open</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <div class="sub-stat-item compact">
                                <div class="sub-stat-icon alkes compact">
                                    <i class="fa fa-stethoscope"></i>
                                </div>
                                <div class="sub-stat-content">
                                    <div class="sub-stat-number compact">{{ number_format($alkesMaintenanceCount) }}</div>
                                    <div class="sub-stat-label compact">Pemeliharaan Alkes</div>
                                    <div class="sub-stat-progress">
                                        <div class="progress h-2px rounded-3 bg-light">
                                            <div class="progress-bar bg-warning" style="width: 75%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-2">
                            <div class="sub-stat-item compact">
                                <div class="sub-stat-icon non-alkes compact">
                                    <i class="fa fa-cogs"></i>
                                </div>
                                <div class="sub-stat-content">
                                    <div class="sub-stat-number compact">{{ number_format($nonAlkesMaintenanceCount) }}</div>
                                    <div class="sub-stat-label compact">Pemeliharaan Non-Alkes</div>
                                    <div class="sub-stat-progress">
                                        <div class="progress h-2px rounded-3 bg-light">
                                            <div class="progress-bar bg-info" style="width: 60%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 d-flex align-items-center justify-content-center">
                    <div class="asset-illustration maintenance compact">
                        <i class="fa fa-wrench"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Card 2: Jadwal Pemeliharaan Bulan Ini -->
<div class="row">
    <div class="col-12">
        <div class="data-panel">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fa fa-calendar-check me-2"></i>
                    Jadwal Pemeliharaan Bulan Ini
                </h5>
            </div>
            <div class="card-body p-0">
                @if(count($maintenanceSchedules ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th>Kode Jadwal</th>
                                <th>Tanggal</th>
                                <th>Kategori</th>
                                <th>Tipe</th>
                                <th>Item</th>
                                <th>Jumlah</th>
                                <th>Catatan</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($maintenanceSchedules as $schedule)
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ $schedule->schedule_code }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-calendar text-muted me-2"></i>
                                        <span>{{ $schedule->schedule_date }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $schedule->schedule_category }}</span>
                                </td>
                                <td>
                                    <strong>{{ $schedule->schedule_type }}</strong>
                                </td>
                                <td>
                                    <strong>{{ $schedule->item_name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ $schedule->schedule_quantity }}</span>
                                </td>
                                <td>
                                    @if($schedule->schedule_notes)
                                        <span class="text-muted">{{ Str::limit($schedule->schedule_notes, 30) }}</span>
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="empty-state">
                    <i class="fa fa-calendar-times"></i>
                    <p class="mb-0">Tidak ada jadwal pemeliharaan</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
