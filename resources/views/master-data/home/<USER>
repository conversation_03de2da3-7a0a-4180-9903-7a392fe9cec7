@extends("layouts.app")

@push('style')
<link href="{{ asset('/') }}css/app/custom_dashboard.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.master_data")
@endpush

@section("content")
<div class="row">
    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon rooms me-3">
                    <i class="fa fa-building"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalRooms ?? 0) }}</div>
                    <div class="stats-label">Total Ruangan</div>
                    <a href="{{ route('master-data.room.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon employees me-3">
                    <i class="fa fa-users"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalEmployees ?? 0) }}</div>
                    <div class="stats-label">Total Pegawai</div>
                    <a href="{{ route('master-data.employee.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon distributors me-3">
                    <i class="fa fa-truck"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalDistributors ?? 0) }}</div>
                    <div class="stats-label">Total Distributor</div>
                    <a href="{{ route('master-data.distributor.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="stats-card">
            <div class="d-flex align-items-center">
                <div class="stats-icon uoms me-3">
                    <i class="fa fa-cube"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stats-number">{{ number_format($totalUoms ?? 0) }}</div>
                    <div class="stats-label">Total UOM</div>
                    <a href="{{ route('master-data.uom.index') }}" class="stats-link">
                        Lihat Detail <i class="fa fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Data -->
<div class="row">
    <!-- Latest Rooms -->
    <div class="col-xl-6">
        <div class="data-panel">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fa fa-building me-2"></i>
                    Ruangan Terbaru
                </h5>
            </div>
            <div class="card-body p-0">
                @if(count($latestRooms ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th>Kode</th>
                                <th>Nama Ruangan</th>
                                <th>PIC</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($latestRooms as $room)
                            <tr>
                                <td><span class="badge bg-primary">{{ $room->room_code }}</span></td>
                                <td><strong>{{ $room->room_name }}</strong></td>
                                <td>{{ $room->pic?->employee_name ?? '-' }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="empty-state">
                    <i class="fa fa-building"></i>
                    <p class="mb-0">Belum ada data ruangan</p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Latest Employees -->
    <div class="col-xl-6">
        <div class="data-panel">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fa fa-users me-2"></i>
                    Pegawai Terbaru
                </h5>
            </div>
            <div class="card-body p-0">
                @if(count($latestEmployees ?? []) > 0)
                <div class="table-responsive">
                    <table class="table table-modern mb-0">
                        <thead>
                            <tr>
                                <th>NIP</th>
                                <th>Nama</th>
                                <th>Jabatan</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($latestEmployees as $employee)
                            <tr>
                                <td><span class="badge bg-info">{{ $employee->employee_identification_number }}</span></td>
                                <td><strong>{{ $employee->employee_name }}</strong></td>
                                <td>{{ $employee->employee_position }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="empty-state">
                    <i class="fa fa-users"></i>
                    <p class="mb-0">Belum ada data pegawai</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection