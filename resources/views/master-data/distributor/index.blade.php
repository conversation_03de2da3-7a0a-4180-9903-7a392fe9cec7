@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
@include('menu.master_data')
@endpush

@section('content')
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in distributor table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <a href="#" class="btn btn-primary btn-modern-rounded add-btn">
                <i class="fas fa-plus"></i>
                <span>Tambah Data</span>
            </a>
            <a href="#" class="btn btn-success btn-modern-rounded import-btn">
                <i class="fas fa-file-excel"></i>
                <span>Import Excel</span>
            </a>
            <div class="datatable-filter-icon btn-modern-rounded">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Nama Distributor</th>
                    <th>Alamat</th>
                    <th>Telepon</th>
                    <th>Email</th>
                    <th style="width: 80px;">Aksi</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="datatable-no-filter-message">
                    <i class="fas fa-filter"></i>
                    <p>No filter available</p>
                    <small>Filter options will be available soon</small>
                </div>
            </div>
        </div>
    </div>
</div>

<x-master.distributor.form></x-master.distributor.form>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/master/app.js') }}"></script>
<script src="{{ asset('/js/app/master/distributor.js') }}"></script>
@endpush