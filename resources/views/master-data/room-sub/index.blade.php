@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
    rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
@include('menu.master_data')
@endpush

@section('content')
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in sub room table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            <x-master.room-sub.form :room="$parentRoom"></x-master.room-sub.form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Kode Ruang Spesifik</th>
                    <th>Nama Ruang Spesifik</th>
                    <th>Scanner ID</th>
                    <th>Status</th>
                    <th style="width: 80px;">Aksi</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script>
    const roomId = {{ $parentRoom->id }};
</script>
<script src="{{ asset('/js/app/master/room-sub.js') }}"></script>
<script>
    $(document).ready(function () {
        // Custom search functionality
        $("#searchInput").on("keyup", function () {
            $("#datatable").DataTable().search(this.value).draw();
        });

        // Custom rows per page functionality
        $("#rowsPerPage").on("change", function () {
            $("#datatable").DataTable().page.len($(this).val()).draw();
        });

        // Update rows per page dropdown when table is drawn
        $("#datatable").on("draw.dt", function () {
            var table = $("#datatable").DataTable();
            var pageLength = table.page.len();
            $("#rowsPerPage").val(pageLength);
        });
    });
</script>
@endpush
