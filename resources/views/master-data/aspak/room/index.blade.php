@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />

<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_treeview_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.master_data')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <div class="datatable-search-input">
                    <input type="text" id="searchInput" placeholder="Search in room table">
                </div>
            </div>
            <div class="datatable-action-buttons">
                <!-- Tree Control Buttons -->
                <div class="btn-group me-3" role="group" aria-label="Tree controls">
                    <button type="button" class="btn btn-outline-secondary btn-modern-rounded me-1" id="expandAllBtn" title="Expand All">
                        <i class="fas fa-plus"></i> Expand
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-modern-rounded" id="collapseAllBtn" title="Collapse All">
                        <i class="fas fa-minus"></i> Collapse
                    </button>
                </div>

                <!-- Action Buttons -->
                <button type="button" class="btn btn-primary btn-modern-rounded me-2" id="addDataBtn">
                    <i class="fas fa-plus"></i> Tambah Data
                </button>
                <button type="button" class="btn btn-success btn-modern-rounded" data-bs-toggle="modal"
                        data-bs-target="#modal-import-excel">
                    <i class="fas fa-file-excel"></i> Import Excel
                </button>
            </div>
        </div>

    <div class="table-responsive">
        <table class="table table-sm datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Nama</th>
                    <th>Kode</th>
                    <th>Tree</th>
                    <th>Parent</th>
                    <th style="width: 80px;">Action</th>
                </tr>
            </thead>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

    

@endsection



@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('js/app/master/aspak-room.js') }}"></script>
    <script src="{{ asset('/js/app/master/app.js') }}"></script>
@endpush
