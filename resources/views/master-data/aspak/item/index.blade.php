@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css"
          rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>

    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}css/app/custom_datatable_treeview_and_filter.css" rel="stylesheet"/>
@endpush

@push('menu')
    @include('menu.master_data')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <div class="datatable-search-input">
                    <input type="text" id="searchInput" placeholder="Cari dalam tabel barang">
                </div>
            </div>
            <div class="datatable-action-buttons">
                <!-- Tree Control Buttons -->
                <div class="btn-group me-3" role="group" aria-label="Tree controls">
                    <button type="button" class="btn btn-outline-secondary btn-modern-rounded me-1" id="expandAllBtn"
                            title="Expand All">
                        <i class="fas fa-plus"></i> Expand
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-modern-rounded" id="collapseAllBtn"
                            title="Collapse All">
                        <i class="fas fa-minus"></i> Collapse
                    </button>
                </div>

                <!-- Action Buttons -->
                <button type="button" class="btn btn-primary btn-modern-rounded me-2" id="addDataBtn">
                    <i class="fas fa-plus"></i> Tambah Data Barang
                </button>
                <button type="button" class="btn btn-success btn-modern-rounded" data-bs-toggle="modal"
                        data-bs-target="#modal-import-excel">
                    <i class="fas fa-file-excel"></i> Import Excel
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-sm datatable-modern-table w-100" id="datatable">
                <thead>
                <tr>
                    <th>No</th>
                    <th>Nama</th>
                    <th>Kode</th>
                    <th>Sinonim</th>
                    <th>Tree</th>
                    <th>Parent</th>
                    <th style="width: 80px;">Action</th>
                </tr>
                </thead>
            </table>
        </div>

        <div class="datatable-custom-pagination">
            <div class="datatable-rows-per-page">
                <label>Show:</label>
                <select id="rowsPerPage">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
        </div>
    </div>

    <!-- Add/Edit Data Modal -->
    <div class="modal fade" id="addDataModal" tabindex="-1" aria-labelledby="addDataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDataModalLabel">Tambah Data Barang</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addDataForm">
                    <input type="hidden" id="edit_id" name="edit_id" value="">
                    <input type="hidden" id="form_method" name="_method" value="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="item_name" class="form-label">Nama <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="item_name" name="item_name" required>
                            <div class="invalid-feedback" id="item_name_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="item_code" class="form-label">Kode <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="item_code" name="item_code" required>
                            <div class="invalid-feedback" id="item_code_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="item_synonym" class="form-label">Sinonim <span
                                    class="optional-text">(opsional)</span></label>
                            <input type="text" class="form-control" id="item_synonym" name="item_synonym">
                            <div class="invalid-feedback" id="item_synonym_error"></div>
                        </div>

                        <div class="mb-3">
                            <label for="tree" class="form-label">Tree <span class="text-danger">*</span></label>
                            <select class="form-select" id="tree" name="tree" required>
                                <option value="">Pilih Tree</option>
                                <option value="BRANCH">BRANCH</option>
                                <option value="LEAF">LEAF</option>
                            </select>
                            <div class="invalid-feedback" id="tree_error"></div>
                        </div>

                        <div class="mb-3" id="parentGroup">
                            <label for="parent_id" class="form-label">Parent <span
                                    class="optional-text">(opsional)</span></label>
                            <select class="form-select select2" id="parent_id" name="parent_id" style="width: 100%;">
                                <option value="">Pilih Parent</option>
                            </select>
                            <div class="invalid-feedback" id="parent_id_error"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary" id="saveDataBtn">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Import Excel -->
    <div class="modal fade" id="modal-import-excel">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Import Excel ASPAK Barang</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <!-- Area untuk menampilkan pesan sukses/error -->
                    <div id="import-message-area" class="d-none mb-3">
                        <div id="import-success-message" class="alert alert-success d-none">
                            <i class="fas fa-check-circle me-1"></i>
                            <span id="success-text"></span>
                        </div>
                        <div id="import-error-message" class="alert alert-danger d-none">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <span id="error-text"></span>
                            <div id="error-details" class="mt-3"></div>
                        </div>
                    </div>

                    <form id="form-import-excel" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label class="form-label">File Excel</label>
                            <input type="file" class="form-control" name="excel_file" id="excel_file"
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">Format file yang didukung: .xlsx, .xls (Maksimal 5MB)</div>
                            <div class="d-block text-danger error-message" id="error_excel_file"></div>
                        </div>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-1"></i>Format Excel yang diperlukan:</h6>
                            <ul class="mb-0">
                                <li><strong>Kolom A (Kode):</strong> Kode Barang (wajib diisi)</li>
                                <li><strong>Kolom B (Nama):</strong> Nama Barang (wajib diisi)</li>
                                <li><strong>Kolom C (Sinonim):</strong> Sinonim Barang (opsional)</li>
                                <li><strong>Kolom D (Parent):</strong> Parent (opsional, kosongkan jika root)</li>
                                <li><strong>Kolom E (Tree):</strong> Tree (opsional, default: 1)</li>
                            </ul>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>Tips:</strong> Parent harus sudah ada sebelum child. Urutkan data dari
                                    parent ke child.
                                </small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal"
                       id="btn-cancel-import">Batal</a>
                    <a href="{{ route('master-data.aspak-item.download-template') }}" target="_blank"
                       class="btn btn-info" id="btn-download-template">
                        <i class="fas fa-download me-1"></i>Download Template
                    </a>
                    <a href="javascript:;" class="btn btn-success" id="btn-import-submit">
                        <i class="fas fa-upload me-1"></i>Import
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('js/app/master/aspak-item.js') }}"></script>
    <script src="{{ asset('/js/app/master/app.js') }}"></script>
@endpush
