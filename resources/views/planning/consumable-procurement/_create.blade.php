@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.planning")
@endpush

@section("content")
<div class="panel panel-inverse">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="" method="post" class="row d-flex justify-content-between" id="formdata">
            <input type="hidden" name="type" value="{{ request('type') }}">
            <div class="form-group col-md-4 mb-3">
                <div class="d-block">
                    <label for="program" class="form-label">Program</label>
                    <select name="program" id="program" class="form-select program"></select>

                    <span class="d-block mt-1 text-danger" id="error-program"></span>
                </div>
            </div>

            <div class="form-group col-md-4 float-end mb-3">
                <label for="tanggal" class="form-label">Tanggal Pemeliharaan</label>
                <input type="date" name="tanggal" id="tanggal" class="form-control" value="{{ now('Asia/Jakarta')->format('Y-m-d') }}">

                <span class="d-block mt-1 text-danger" id="error-tanggal"></span>
            </div>

            <div class="col-md-12 mb-3">
                <button type="button" class="btn btn-outline-primary" id="btn-add-row"><i class="fas fa-plus-circle me-1"></i>Tambah Perencanaan</button>
            </div>

            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label for="program" class="form-label">Program</label>
                                    <select name="program" id="program" class="form-control">
                                        <option value="">-- Pilih Program --</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label for="kegiatan" class="form-label">Kegiatan</label>
                                    <select name="kegiatan" id="kegiatan" class="form-control">
                                        <option value="">-- Pilih Kegiatan --</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="form-group">
                                    <label for="output" class="form-label">Output</label>
                                    <select name="output" id="output" class="form-control">
                                        <option value="">-- Pilih Output --</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row d-flex mb-3">
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-primary"><i class="fas fa-plus-circle me-1"></i>Tambah Baris</button>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped w-100">
                                        <thead class="bg-gray-400">
                                            <tr>
                                                <th>Asset</th>
                                                <th>Satuan</th>
                                                <th>Qty</th>
                                                <th>Price</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>

                                        <tbody id="table-perencanaan"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" style="min-width: 1080px !important;">
                        <thead class="bg-gray-400">
                            <tr>
                                <th>Program Output</th>
                                @if(request('type') == "other")
                                <th>Kode Asset</th>
                                @endif
                                <th>Asset</th>
                                <th width="10%">Satuan</th>
                                <th>Qty</th>
                                <th>Price</th>
                                <th width="10%">Action</th>
                            </tr>
                        </thead>

                        <tbody id="table-perencanaan"></tbody>
                    </table>
                </div>
            </div>

            <div class="form-group col-md-4 mb-3">
                <div class="d-block mb-3">
                    <label for="judul" class="form-label">Judul Perencanaan</label>
                    <input type="text" name="judul" id="judul" class="form-control">

                    <span class="d-block mt-1 text-danger" id="error-judul"></span>
                </div>

                <div class="d-block mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <input type="text" name="notes" id="notes" class="form-control">

                    <span class="d-block mt-1 text-danger" id="error-notes"></span>
                </div>

                <div class="d-block mb-3">
                    <label for="doc" class="form-label">Doc <span class="text-muted"><i>(Opsional)</i></span></label>
                    <input type="file" name="doc" id="doc" class="form-control">

                    <span class="d-block mt-1 text-danger" id="error-doc"></span>
                </div>
            </div>

            <div class="col-md-12">
                <button type="button" class="btn btn-primary float-end" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</button>
            </div> -->
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/planning/createConsumableProcurement.js') }}"></script>
@endpush