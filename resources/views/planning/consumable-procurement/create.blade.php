@extends('layouts.app')

@push('style')
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
@include('menu.planning')
@endpush

@section('content')
<div class="datatable-modern-container">
    <div class="panel-body">
        <form action="" method="post" class="row d-flex justify-content-between" id="formdata">
            <div class="form-group col-md-4 mb-3">
                <div class="d-block">
                    <label for="room" class="form-label">Ruangan</label>
                    <select name="room" id="room" class="form-select room"></select>
                    <span class="d-block mt-1 text-danger" id="error-room"></span>
                </div>
            </div>
            <div class="form-group col-md-4 float-end mb-3">
                <label for="tanggal" class="form-label">Tahun Perencanaan</label>
                <select name="tanggal" id="tanggal" class="form-select">
                    @php
                        $currentYear = now('Asia/Jakarta')->year;
                        $startYear = $currentYear - 3;
                        $endYear = $currentYear + 3;
                    @endphp
                    @for($year = $startYear; $year <= $endYear; $year++)
                        <option value="{{ $year }}-12-12" {{ $year == $currentYear ? 'selected' : '' }}>{{ $year }}</option>
                    @endfor
                </select>
                <span class="d-block mt-1 text-danger" id="error-tanggal"></span>
            </div>
            <div class="form-group col-md-12 mb-3">
                <label for="plan_title" class="form-label">Judul Perencanaan</label>
                <input type="text" name="plan_title" id="plan_title" class="form-control" placeholder="Masukkan judul perencanaan">
                <span class="d-block mt-1 text-danger" id="error-plan_title"></span>
            </div>
            <div class="form-group col-md-12 mb-3">
                <label for="plan_notes" class="form-label">Catatan Perencanaan</label>
                <textarea name="plan_notes" id="plan_notes" class="form-control" rows="3" placeholder="Masukkan catatan perencanaan (opsional)"></textarea>
                <span class="d-block mt-1 text-danger" id="error-plan_notes"></span>
            </div>
            <div class="col-md-12" id="dynamic-forms-container">
                <div class="card mb-3" id="empty-planning-form">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted mb-2">Belum Ada Form Perencanaan</h5>
                        <p class="text-muted mb-0">Klik tombol "Tambah Perencanaan" untuk membuat form baru</p>
                    </div>
                </div>
            </div>
            <div class="col-md-12 mt-3">
                <button type="button" class="btn btn-success" id="btn-save">
                    <i class="fas fa-save me-1"></i>Simpan
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Import -->
<div class="modal fade" id="modalImport" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Data Barang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Download Template</label>
                    <div>
                        <a href="#" id="downloadTemplate" class="btn btn-outline-primary">
                            <i class="fas fa-download me-1"></i>Download Template CSV
                        </a>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Upload File CSV</label>
                    <input type="file" class="form-control" id="csvFile" accept=".csv">
                    <small class="text-muted">Format: Kode Barang, Nama Barang, UOM, Qty</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="btnImport">
                    <i class="fas fa-file-import me-1"></i>Import
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/planning/createConsumableProcurement.js') }}"></script>
@endpush