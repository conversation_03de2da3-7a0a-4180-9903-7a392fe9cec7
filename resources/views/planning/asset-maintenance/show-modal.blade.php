<div class="row mb-4">
    <div class="col-md-6">
        <div class="mb-3">
            <label class="form-label">Tanggal Dibuat</label>
            <p class="form-control-static">{{ date('d/m/Y', strtotime($maintenancePlan->created_at)) }}</p>
        </div>
        <div class="mb-3">
            <label class="form-label">Program</label>
            <p class="form-control-static">{{ $maintenancePlan->maintenance_plan_title }}</p>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label class="form-label"><PERSON><PERSON></label>
            <p class="form-control-static">{{ date('Y', strtotime($maintenancePlan->maintenance_plan_date)) }}</p>
        </div>
        <div class="mb-3">
            <label class="form-label">Ruangan</label>
            <p class="form-control-static">{{ $maintenancePlan->room->room_code }} - {{ $maintenancePlan->room->room_name }}</p>
        </div>
    </div>
</div>

<div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
    <table class="table table-bordered table-hover">
        <thead class="sticky-top bg-white">
            <tr>
                <th style="width: 50px">No.</th>
                <th>Program/Kegiatan/Output</th>
                <th>Asset</th>
                <th>Satuan</th>
                <th>Quantity</th>
                <th>Kondisi</th>
                <th>Kategori Pemeliharaan</th>
            </tr>
        </thead>
        <tbody>
            @php
                $currentProgram = null;
                $no = 1;
            @endphp
            @foreach($maintenancePlan->maintenancePlanDetails as $detail)
                @php
                    $programs = $detail->maintenancePlanDetailPrograms->sortBy('level');
                    $programStructure = $programs->pluck('program_name')->implode(' / ');
                @endphp
                @if($programStructure != $currentProgram)
                    <tr class="table-light">
                        <td colspan="7">{{ $programStructure }}</td>
                    </tr>
                    @php
                        $currentProgram = $programStructure;
                    @endphp
                @endif
                <tr>
                    <td>{{ $no++ }}</td>
                    <td></td>
                    <td>{{ $detail->item->item_code }} - {{ $detail->item->item_name }}</td>
                    <td>{{ $detail->uom->uom_name }}</td>
                    <td class="text-end">{{ number_format($detail->quantity, 0, ',', '.') }}</td>
                    <td>{{ str_replace('_', ' ', $detail->condition) }}</td>
                    <td>{{ $detail->maintenanceCategory->maintenance_category_name }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>

@if($maintenancePlan->maintenance_plan_notes)
<div class="mt-4">
    <label class="form-label">Catatan:</label>
    <p class="form-control-static">{{ $maintenancePlan->maintenance_plan_notes }}</p>
</div>
@endif 