@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.planning")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="panel-heading">
        <h4 class="panel-title">{{ $title ?? "Blank page" }}</h4>
    </div>
    <div class="panel-body">
        <form action="" method="post" class="row d-flex justify-content-between" id="formdata">
            <div class="form-group col-md-4 mb-3">
                <div class="d-block">
                    <label for="ruangan" class="form-label">Ruangan</label>
                    <select name="ruangan" id="ruangan" class="form-select ruangan"></select>
                    <span class="d-block mt-1 text-danger" id="error-ruangan"></span>
                </div>
            </div>

            <div class="form-group col-md-4 float-end mb-3">
                <label for="tanggal" class="form-label">Tahun Pemeliharaan</label>
                <select name="tanggal" id="tanggal" class="form-select">
                    @php
                        $currentYear = now('Asia/Jakarta')->year;
                        $startYear = $currentYear - 3;
                        $endYear = $currentYear + 3;
                    @endphp
                    @for($year = $startYear; $year <= $endYear; $year++)
                        <option value="{{ $year }}-12-12" {{ $year == $currentYear ? 'selected' : '' }}>{{ $year }}</option>
                    @endfor
                </select>
                <span class="d-block mt-1 text-danger" id="error-tanggal"></span>
            </div>

            <div class="form-group col-md-12 mb-3">
                <label for="judul" class="form-label">Judul Pemeliharaan</label>
                <input type="text" name="judul" id="judul" class="form-control">
                <span class="d-block mt-1 text-danger" id="error-judul"></span>
            </div>

            <div class="form-group col-md-12 mb-3">
                <label for="notes" class="form-label">Catatan Pemeliharaan</label>
                <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Masukkan catatan pemeliharaan (opsional)"></textarea>
                <span class="d-block mt-1 text-danger" id="error-notes"></span>
            </div>

            <div id="program-groups">
                <div class="program-group mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <div class="form-group col-md-11">
                                    <div class="d-block">
                                        <label for="program_output_0" class="form-label">Program Output</label>
                                        <select name="program_output[]" id="program_output_0" class="form-select program-output"></select>
                                        <span class="d-block mt-1 text-danger" id="error-program_output_0"></span>
                                        
                                        <div class="program-info mt-2">
                                            <div class="program-parent small text-muted"></div>
                                            <div class="program-top-parent small text-muted"></div>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-danger btn-sm btn-delete-program" style="display: none;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered" width="100%" style="min-width: 1080px !important;">
                                        <thead class="bg-light">
                                            <tr>
                                                <th width="25%">Asset</th>
                                                <th width="15%">Satuan</th>
                                                <th width="10%">Qty</th>
                                                <th width="15%">Kondisi Barang</th>
                                                <th width="25%">Pemeliharaan</th>
                                                <th width="10%">Action</th>
                                            </tr>
                                        </thead>

                                        <tbody class="table-details">
                                            <tr class="empty-row">
                                                <td colspan="6" class="text-center py-3">
                                                    <i class="fas fa-archive fa-2x text-muted mb-3 d-block"></i>
                                                    <h5 class="text-muted mb-2">Belum Ada Data</h5>
                                                    <p class="text-muted mb-0">Klik tombol "Tambah Baris" untuk menambahkan data.</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <button type="button" class="btn btn-outline-primary btn-sm btn-add-row">
                                    <i class="fas fa-plus-circle me-1"></i>Tambah Baris
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 mb-3">
                <button type="button" class="btn btn-outline-success" id="btn-add-program">
                    <i class="fas fa-plus-circle me-1"></i>Tambah Program
                </button>
            </div>

            <div class="col-md-12 mt-3">
                <button type="button" class="btn btn-primary float-end" id="btn-save"><i class="fas fa-save me-1"></i>Simpan</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/planning/createAssetMaintenance.js') }}"></script>
@endpush