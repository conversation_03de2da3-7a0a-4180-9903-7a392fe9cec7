@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_report_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.planning")
@endpush

@section("content")
    <div class="report-card">
        <div class="report-header">
            <h4>{{ $title ?? "Laporan Planning" }}</h4>
        </div>

        <div class="report-body">
            <div class="d-flex justify-content-end mb-4">
                <button type="button" class="btn btn-export" id="btn-export">
                    <i class="fas fa-file-excel me-1"></i>Export Excel
                </button>
            </div>

            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group" style="max-width: 120px;">
                        <label><i>Tahun</i></label>
                        @php
                            $currentYear = date('Y');
                            $minYear = $currentYear - 10;
                            $maxYear = $currentYear + 2;
                        @endphp
                        <select id="year" name="year" class="form-select">
                            <option value="">Pilih Tahun</option>
                            @for($year = $maxYear; $year >= $minYear; $year--)
                                <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>{{ $year }}</option>
                            @endfor
                        </select>
                    </div>

                    <div class="filter-group">
                        <button type="button" id="btn-filter" class="btn btn-show-data">
                            <i class="fas fa-search me-2"></i>
                            Tampilkan Data
                        </button>
                    </div>
                </div>
            </div>

            <div class="target-report" id="target-report">
                <!-- Empty State - akan ditampilkan saat awal -->
                <div class="empty-state" id="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="empty-state-title">
                        Mulai Analisis Data Planning
                    </div>
                    <div class="empty-state-description">
                        Pilih tahun yang ingin Anda analisis, kemudian klik "Tampilkan Data" untuk melihat
                        laporan detail planning.
                    </div>

                    <div class="empty-state-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">Pilih Tahun</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">Klik Tampilkan Data</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/moment/moment.js"></script>
<script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/js/app/planning/plaing_plan_report.js') }}"></script>

<script>
    // Hide empty state when data is loaded
    $(document).ready(function() {
        $('#btn-filter').on('click', function() {
            $('#empty-state').hide();
        });
    });
</script>
@endpush