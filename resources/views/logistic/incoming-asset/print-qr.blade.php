<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print QR Label - <PERSON><PERSON> {{ $logistic->logistic_number }}</title>
    <style>
        @page {
            size: A4;
            margin: 10mm;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: white;
        }



        .qr-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5mm;
            padding: 5mm;
        }

        .qr-item {
            border: 1px solid #000;
            border-radius: 5px;
            padding: 2mm;
            text-align: center;
            min-height: 35mm;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            break-inside: avoid;
        }

        .qr-item .asset-info {
            margin-bottom: 1mm;
        }

        .qr-item .asset-name {
            font-size: 9px;
            font-weight: bold;
            margin-bottom: 0.5mm;
            word-wrap: break-word;
        }

        .qr-item .asset-code {
            font-size: 10px;
            font-weight: bold;
            margin-bottom: 0.5mm;
        }

        .qr-item .qr-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
        }

        .qr-item .qr-container canvas {
            max-width: 100%;
            height: auto;
        }

                @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
                         .qr-grid {
                 grid-template-columns: repeat(4, 1fr) !important;
                 gap: 3mm !important;
                 padding: 3mm !important;
             }
            
            .qr-item {
                break-inside: avoid;
                min-height: 30mm !important;
                padding: 1.5mm !important;
            }
            
            .qr-item .asset-info {
                margin-bottom: 0.5mm !important;
            }
            
            .qr-item .asset-name {
                font-size: 8px !important;
                margin-bottom: 0.3mm !important;
            }
            
            .qr-item .asset-code {
                font-size: 9px !important;
                margin-bottom: 0.3mm !important;
            }
            
            button,
            .qr-info {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="qr-info" style="position: fixed; top: 20px; left: 20px; z-index: 1000; background: #f8f9fa; padding: 10px; border-radius: 5px; border: 1px solid #dee2e6;">
        <strong>Total QR Labels: {{ $totalQrToPrint }}</strong>
    </div>
    <button onclick="window.print()" style="position: fixed; top: 20px; right: 20px; z-index: 1000; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Print</button>

    <div class="qr-grid">
        @foreach($logisticDetail as $detail)
            @for($i = 0; $i < min($detail->quantity, 50); $i++)
                <div class="qr-item">
                    <div class="asset-info">
                        <div class="asset-name">{{ $detail->asset_name }}</div>
                        <div class="asset-code">{{ $detail->asset_code }}</div>
                    </div>
                    <div class="qr-container" data-qr-text="{{ $detail->asset_code }}">
                        <!-- QR Code akan di-generate di client-side -->
                    </div>
                </div>
            @endfor
        @endforeach
    </div>

    <script src="{{ asset('/') }}plugins/qr/qrcode.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Generate QR codes for all containers
            const qrContainers = document.querySelectorAll('.qr-container');
            qrContainers.forEach(function(container) {
                const qrText = container.getAttribute('data-qr-text');
                if (qrText) {
                    new QRCode(container, {
                        text: qrText,
                        width: 50,
                        height: 50,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.H
                    });
                }
            });

            // Auto print after QR codes are generated
            setTimeout(function() {
                window.print();
            }, 1000);
        });
    </script>
</body>
</html>
