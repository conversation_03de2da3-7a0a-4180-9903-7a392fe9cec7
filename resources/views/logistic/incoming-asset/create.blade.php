@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <h4 class="mb-0">{{ $title ?? 'Blank page' }}</h4>
            </div>
            <div class="datatable-action-buttons">
                <a href="/logistik/barang-masuk" class="btn btn-outline-secondary btn-modern-rounded">
                    <i class="fas fa-arrow-left"></i>
                    <span>Kembali</span>
                </a>
            </div>
        </div>

        <form action="" method="post" id="form-activity">
            <div class="row g-3 mb-4">
                <div class="form-group col-md-4">
                    <label for="distributor" class="form-label fw-semibold">Distributor</label>
                    <select name="distributor" id="distributor" class="form-select distributor">
                        <option value="">Pilih Distributor</option>
                    </select>
                    <span class="d-block text-danger small mt-1" id="error_distributor"></span>
                </div>

                <div class="form-group col-md-4">
                    <label for="logistic_date" class="form-label fw-semibold">Tanggal Barang Masuk</label>
                    <input type="date" name="logistic_date" id="logistic_date" class="form-control" value="{{ old('logistic_date') ?? now()->format('Y-m-d') }}">
                    <span class="d-block text-danger small mt-1" id="error_logistic_date"></span>
                </div>
            </div>

            <div class="mb-3">
                <button type="button" id="btn-add-row" class="btn btn-outline-primary btn-modern-rounded">
                    <i class="fas fa-plus-circle me-2"></i>Tambah Baris
                </button>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped w-100 mb-3">
                    <thead>
                    <tr>
                        <th style="max-width: 300px !important;">Barang Habis Pakai</th>
                        <th>Detail Barang</th>
                        <th>Harga Satuan</th>
                        <th>Qty</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody id="table-allocation"></tbody>
                </table>
            </div>

            <div class="row g-3 my-4">
                <div class="form-group col-md-4">
                    <label for="notes" class="form-label fw-semibold">Catatan</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Catatan / Deskripsi"></textarea>
                    <span class="d-block text-danger small mt-1" id="error_notes"></span>
                </div>

                <div class="form-group col-md-4">
                    <label for="attachment_link" class="form-label fw-semibold">
                        File Lampiran
                        <small class="text-muted">(*jika dibutuhkan)</small>
                    </label>
                    <span style="cursor: pointer" id="file_attach" class="d-block mb-2">
                        <i class="fa fa-file-pdf"></i> Pilih File
                    </span>
                    <input type="file" name="document" id="attachment_link" class="form-control" style="display: none;">
                    <span class="d-block text-danger small mt-1" id="error_document"></span>
                </div>

                <div class="form-group col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <table class="table table-borderless mb-0">
                                <tbody>
                                <tr>
                                    <th class="fw-semibold">Total Nilai:</th>
                                    <td class="text-end text-primary fw-bold" id="txt_total_amount">0</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group d-flex justify-content-end">
                <button type="button" id="btn-save" class="btn btn-primary btn-modern-rounded">
                    <i class="fas fa-save me-2"></i>Simpan Form
                </button>
            </div>
        </form>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/logistic/createIncomingAsset.js') }}"></script>
@endpush
