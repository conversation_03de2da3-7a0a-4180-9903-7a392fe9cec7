@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push("menu")
@include("menu.logistic")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <h4 class="mb-0">{{ $title ?? "Blank page" }}</h4>
        </div>
        <div class="datatable-action-buttons">
            <a href="{{ route('logistic.asset.index') }}" class="btn btn-outline-secondary btn-modern-rounded">
                <i class="fas fa-arrow-left"></i>
                <span>Kembali</span>
            </a>
        </div>
    </div>

    <form action="" method="post" class="row g-3" id="formdata">
        <div class="form-group col-md-4">
            <label for="kode_barang" class="form-label fw-semibold">Kode Barang</label>
            <select name="kode_barang" id="kode_barang" class="form-select kode-barang">
                <option value="">Pilih Kode Barang</option>
            </select>
            <span class="d-block text-danger small mt-1" id="error_kode_barang"></span>
        </div>

        <div class="form-group col-md-4">
            <label for="stok_rekapitulasi" class="form-label fw-semibold">Stok Rekapitulasi</label>
            <select name="stok_rekapitulasi" id="stok_rekapitulasi" class="form-select stok-rekapitulasi">
                <option value="">Pilih Stok Rekapitulasi</option>
            </select>
            <span class="d-block text-danger small mt-1" id="error_stok_rekapitulasi"></span>
        </div>

        <div class="form-group col-md-4">
            <label for="satuan" class="form-label fw-semibold">Satuan</label>
            <select name="satuan" id="satuan" class="form-select satuan">
                <option value="">Pilih Satuan</option>
            </select>
            <span class="d-block text-danger small mt-1" id="error_satuan"></span>
        </div>

        <div class="form-group col-md-4">
            <label for="bidang_permintaan" class="form-label fw-semibold">Bidang Permintaan</label>
            <select name="bidang_permintaan" id="bidang_permintaan" class="form-select bidang-permintaan">
                <option value="">Pilih Bidang Permintaan</option>
                @foreach ($dataConfigStockField as $config)
                <option value="{{ $config->id }}">{{ $config->name }}</option>
                @endforeach
            </select>
            <span class="d-block text-danger small mt-1" id="error_bidang_permintaan"></span>
        </div>

        <div class="form-group col-md-4">
            <label for="nama_asset" class="form-label fw-semibold">Nama Asset Logistik</label>
            <input type="text" name="nama_asset" id="nama_asset" class="form-control" placeholder="Masukkan nama asset">
            <span class="d-block text-danger small mt-1" id="error_nama_asset"></span>
        </div>

        <div class="form-group col-md-4">
            <label for="kode_asset" class="form-label fw-semibold">Kode Asset Logistik</label>
            <input type="text" name="kode_asset" id="kode_asset" class="form-control" readonly>
            <span class="d-block text-danger small mt-1" id="error_kode_asset"></span>
        </div>

        <div class="form-group col-md-4">
            <label for="kode_register" class="form-label fw-semibold">
                Kode Register Logistik 
                <small class="text-muted">(Max 3 digit)</small>
            </label>
            <input type="number" name="kode_register" id="kode_register" class="form-control" placeholder="000" min="0" max="999">
            <span class="d-block text-danger small mt-1" id="error_kode_register"></span>
        </div>

        <div class="form-group col-12 mt-4">
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-primary btn-modern-rounded" id="btn-save">
                    <i class="fas fa-save me-2"></i>Simpan
                </button>
                <button type="button" class="btn btn-info btn-modern-rounded" id="btn-save-add">
                    <i class="fas fa-save me-2"></i>Simpan & Tambah
                </button>
                <a href="{{ route('logistic.asset.index') }}" class="btn btn-outline-danger btn-modern-rounded">
                    <i class="fas fa-undo me-2"></i>Kembali
                </a>
            </div>
        </div>
    </form>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
<script src="{{ asset('/js/app/logistic/createAsset.js') }}"></script>
@endpush