@extends("layouts.app")

@push("style")
<link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
<link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
<!-- QR Code Library -->
<style>
.qr-code-container {
    transition: all 0.3s ease;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
}

.qr-code-container:hover {
    background-color: #f8f9fa;
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.qr-code-container img {
    transition: all 0.3s ease;
}

.qr-code-container:hover img {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.qr-code-placeholder {
    transition: all 0.3s ease;
}

.qr-code-placeholder:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.qr-code-placeholder i {
    color: #007bff;
    transition: color 0.3s ease;
    font-size: 16px;
}

.qr-code-placeholder:hover i {
    color: #0056b3;
}

#qr-code-3d-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 250px;
    padding: 20px;
}

#qr-code-3d-container canvas {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

#qr-code-3d-container img {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Mobile Responsive Styles for Action Buttons */
@media (max-width: 768px) {
    .datatable-action-buttons {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }
    
    .datatable-action-buttons .btn,
    .datatable-action-buttons .btn-group,
    .datatable-action-buttons .datatable-filter-icon {
        width: 100%;
        margin: 0;
    }
    
    .datatable-action-buttons .btn {
        justify-content: center;
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .datatable-action-buttons .btn span {
        margin-left: 8px;
    }
    
    .datatable-action-buttons .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .datatable-action-buttons .btn-group .dropdown-toggle {
        width: 100%;
        border-radius: 6px !important;
        margin-bottom: 0;
    }
    
    .datatable-action-buttons .btn-group .dropdown-menu {
        width: 100%;
        position: static !important;
        transform: none !important;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 6px 6px;
        box-shadow: none;
        margin-top: 0;
    }
    
    .datatable-action-buttons .datatable-filter-icon {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .datatable-action-buttons .datatable-filter-icon:hover {
        background-color: #e9ecef;
    }
    
    .datatable-action-buttons .datatable-filter-icon i {
        font-size: 16px;
        color: #6c757d;
    }
    
    /* Hide text on very small screens, show only icons */
    @media (max-width: 480px) {
        .datatable-action-buttons .btn span {
            display: none;
        }
        
        .datatable-action-buttons .btn {
            padding: 12px;
            min-width: 44px;
        }
        
        .datatable-action-buttons .btn i {
            margin: 0;
            font-size: 16px;
        }
        
        .datatable-action-buttons .btn-group .dropdown-toggle span {
            display: none;
        }
        
        .datatable-action-buttons .btn-group .dropdown-toggle i.fa-chevron-down {
            display: none;
        }
    }
}

/* Tablet responsive adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .datatable-action-buttons {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .datatable-action-buttons .btn,
    .datatable-action-buttons .btn-group {
        flex: 1;
        min-width: 0;
    }
}
</style>
@endpush

@push("menu")
@include("menu.logistic")
@endpush

@section("content")
<div class="datatable-modern-container">
    <div class="datatable-control-bar">
        <div class="datatable-search-container">
            <div class="datatable-search-input">
                <input type="text" id="searchInput" placeholder="Search in asset logistic table">
            </div>
        </div>
        <div class="datatable-action-buttons">
            @include("logistic.asset.import-saldo")
            @include("logistic.asset.import")
            <a href="{{ route('logistic.asset.create') }}" class="btn btn-primary btn-modern-rounded add-btn">
                <i class="fas fa-plus"></i>
                <span>Tambah BHP</span>
            </a>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success btn-modern-rounded dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download"></i>
                    <span>Export Data</span>
                    <i class="fas fa-chevron-down ms-1"></i>
                </button>
                <ul class="dropdown-menu datatable-dropdown-menu">
                    <li><a class="dropdown-item datatable-dropdown-item btn-export-excel" href="javascript:;">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </a></li>
                    <li><a class="dropdown-item datatable-dropdown-item btn-export-pdf" href="javascript:;">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </a></li>
                </ul>
            </div>
            <a href="{{ route('logistic.asset.print_label') }}" class="btn btn-secondary btn-modern-rounded print-btn">
                <i class="fas fa-print"></i>
                <span>Print Label</span>
            </a>
            <div class="datatable-filter-icon btn-modern-rounded">
                <i class="fas fa-filter"></i>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Foto BHP</th>
                    <th>QR</th>
                    <th>Kode Barang</th>
                    <th>Nama Barang Habis Pakai</th>
                    <th>Satuan</th>
                    <th>Rekapitulasi / Bidang Permintaan</th>
                    <th>Status</th>
                    <th style="width: 80px;">Action</th>
                </tr>
            </thead>
            <tbody>
                <!-- Data will be loaded by DataTable -->
            </tbody>
        </table>
    </div>

    <div class="datatable-custom-pagination">
        <div class="datatable-rows-per-page">
            <label>Show:</label>
            <select id="rowsPerPage">
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
            </select>
            <span>entries</span>
        </div>
    </div>
</div>

<!-- Filter Drawer -->
<div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
    <div class="modal-dialog modal-drawer" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">Kode Barang</label>
                    <select name="filter_kode_barang" id="filter_kode_barang" class="form-select form-control filter-kode-barang-select2">
                        <option value="">Semua Kode Barang</option>
                    </select>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">Status</label>
                    <select name="filter_status" id="filter_status" class="form-select form-control filter-status-select2">
                        <option value="">Semua Status</option>
                        <option value="active">Aktif</option>
                        <option value="inactive">Tidak Aktif</option>
                    </select>
                </div>
                <div class="d-flex gap-2 mt-4">
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFilter">
                        <i class="fas fa-undo me-1"></i>Reset Filter
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" id="applyFilter">
                        <i class="fas fa-filter me-1"></i>Terapkan Filter
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detail Aset Logistik</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div id="activityBody" class="modal-body">

            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Tutup</a>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-img-preview">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Preview Foto BHP</h4>
            </div>
            <div class="modal-body text-center">
                <img src="" class="img-asset-preview img-fluid" alt="">
            </div>
            <div class="modal-footer">
                <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Photo -->
<div class="modal fade" id="modal-edit-photo" tabindex="-1" aria-labelledby="modal-edit-photo-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-edit-photo-label">Update Photo BHP</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="form-upload-photo" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <input type="hidden" id="asset-entry-id" name="asset_entry_id">
                    <div class="mb-3">
                        <label for="photo" class="form-label">Pilih Foto <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="photo" name="photo" accept="image/*" required>
                        <div class="form-text">Format yang diizinkan: JPG, JPEG, PNG. Maksimal 2MB.</div>
                        <div class="form-error-message text-danger" id="error_photo"></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Preview:</label>
                        <div class="text-center">
                            <img id="photo-preview" src="" alt="Preview" class="img-fluid" style="max-height: 400px; display: none;">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="btn-upload-photo">Upload Photo</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal QR Code 3D -->
<div class="modal fade" id="modal-qr-code" tabindex="-1" aria-labelledby="modal-qr-code-label" aria-hidden="true">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-qr-code-label">QR Code Aset Logistik</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <div id="qr-code-3d-container" class="mb-3"></div>
                    <label class="form-label fw-bold">Kode QR:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="qr-code-text" readonly>
                        <button class="btn btn-outline-secondary" type="button" id="copy-qr-code">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push("script")
<script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
<script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
<script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
<script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
<script src="{{ asset('/') }}plugins/qr/qrcode.min.js"></script>
<script src="{{ asset('/js/app/logistic/app.js') }}"></script>
<script src="{{ asset('/js/app/logistic/assetLogistic.js') }}"></script>
@endpush
