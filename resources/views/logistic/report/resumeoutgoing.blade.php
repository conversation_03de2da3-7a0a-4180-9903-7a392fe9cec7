@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="{{ asset('/') }}css/app/custom_report_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <div class="report-card">
        <div class="report-header">
            <h4>{{ $title ?? 'Laporan Resume Barang Keluar' }}</h4>
        </div>

        <div class="report-body">
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-group">
                        <label><i>Periode</i></label>
                        <input id="datepicker" type="text" class="form-control" readonly="readonly" style="background-color:white; cursor:pointer;">
                    </div>

                    <div class="filter-group">
                        <label><i>Barang</i></label>
                        <select name="asset_entry" id="asset_entry" class="form-select asset_entry">
                            <option value="">-- Pilih Distributor --</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <button type="button" id="btn-filter" class="btn btn-show-data">
                            <i class="fas fa-search me-2"></i>
                            Tampilkan Data
                        </button>
                    </div>

                    <div class="filter-group">
                        <ul class="nav nav-pills" id="viewTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="table-tab" data-bs-toggle="tab" href="#table-view" role="tab" aria-controls="table-view" aria-selected="true">Tabel</a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="chart-tab" data-bs-toggle="tab" href="#chart-view" role="tab" aria-controls="chart-view" aria-selected="false">Grafik</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="target-report" id="target-report">
                <!-- Empty State - akan ditampilkan saat awal -->
                <div class="empty-state" id="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="empty-state-title">
                        Mulai Analisis Data Resume Barang Keluar
                    </div>
                    <div class="empty-state-description">
                        Pilih periode dan barang yang ingin Anda analisis, kemudian klik "Tampilkan Data" untuk melihat
                        laporan detail resume barang keluar dalam bentuk tabel atau grafik.
                    </div>

                    <div class="empty-state-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-text">Pilih Periode</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-text">Pilih Barang</div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-text">Klik Tampilkan Data</div>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="content-area" id="content-area" style="display: none;">
                    <div class="tab-content" id="viewTabsContent">
                        <div class="tab-pane fade show active" id="table-view" role="tabpanel" aria-labelledby="table-tab">
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered table-striped w-100" id="datatable">
                                    <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Kode Ruangan</th>
                                        <th>Nama Ruangan</th>
                                        <th>Total Aset</th>
                                        <th>Total Value</th>
                                        <th>Aksi</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="chart-view" role="tabpanel" aria-labelledby="chart-tab">
                            <canvas style="max-height: 320px;" class="mt-3" id="chartContainer">
                            </canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-dialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Detail Logistik</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div class="modal-body">
                    <!-- Informasi Detail -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body py-3">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-calendar-alt text-primary me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Periode</small>
                                                    <strong id="modal-periode">-</strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-building text-success me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Kode Ruangan</small>
                                                    <strong id="modal-room-code">-</strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-door-open text-info me-2"></i>
                                                <div>
                                                    <small class="text-muted d-block">Nama Ruangan</small>
                                                    <strong id="modal-room-name">-</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Options -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="d-flex align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="groupByItemCode" onchange="toggleGroupBy()">
                                    <label class="form-check-label fw-bold" for="groupByItemCode">
                                        <i class="fas fa-layer-group text-primary me-1"></i>
                                        Group By Kode Barang
                                    </label>
                                </div>
                                <small class="text-muted ms-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Centang untuk mengelompokkan data berdasarkan kode barang dan menjumlahkan kuantitas & harga
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Tabel Detail Normal -->
                    <div class="table-responsive mt-4 mb-4" id="table-normal">
                        <table class="table table-striped" id="datatable-asset-normal">
                            <thead>
                            <tr>
                                <th>#</th>
                                <th>Nomor Logistik</th>
                                <th>Tanggal Logistik</th>
                                <th>Kode Barang</th>
                                <th>Nama Item</th>
                                <th>Nama Asset</th>
                                <th>Kuantitas</th>
                                <th>Harga Barang</th>
                                <th>Total Harga</th>
                            </tr>
                            </thead>
                        </table>
                    </div>

                    <!-- Tabel Detail Grouped -->
                    <div class="table-responsive mt-4 mb-4" id="table-grouped" style="display: none;">
                        <table class="table table-striped" id="datatable-asset-grouped">
                            <thead>
                            <tr>
                                <th>#</th>
                                <th>Kode Barang</th>
                                <th>Nama Item</th>
                                <th>Nama Asset</th>
                                <th>Total Kuantitas</th>
                                <th>Rata-rata Harga</th>
                                <th>Total Harga</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">Close</a>
                    <a href="javascript:;" class="btn btn-warning btn-export-excel" data-id=""><i class="fas fa-file-excel me-1"></i> Export Excel</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/moment/moment.js"></script>
    <script src="{{ asset('/') }}plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
    <script src="{{ asset('plugins/chart.js/dist/chart.js') }}"></script>
    <script src="{{ asset('js/app/logistic/resumeOutgoingReport.js') }}"></script>

    <script>
        // Hide empty state and show content when data is loaded
        $(document).ready(function() {
            $('#btn-filter').on('click', function() {
                $('#empty-state').hide();
                $('#content-area').show();
            });
        });
    </script>
@endpush
