@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <h4 class="mb-0">{{ $title ?? 'Blank page' }}</h4>
            </div>
            <div class="datatable-action-buttons">
                <a href="/logistik/rekapitulasi-stock" class="btn btn-outline-secondary btn-modern-rounded">
                    <i class="fas fa-arrow-left"></i>
                    <span>Kembali</span>
                </a>
            </div>
        </div>

        <form action="" method="post" id="form-activity">
            <div class="row g-3 mb-4">
                <div class="form-group col-md-4">
                    <label for="tipe_rekap" class="form-label fw-semibold">Tipe Rekap</label>
                    <select name="tipe_rekap" id="tipe_rekap" class="form-select tipe_rekap">
                        <option value="">Pilih Tipe Rekap</option>
                    </select>
                    <span class="d-block text-danger small mt-1" id="error_tipe_rekap"></span>
                </div>

                <div class="form-group col-md-4">
                    <label for="request_date" class="form-label fw-semibold">Tanggal Rekap</label>
                    <input type="date" name="request_date" id="request_date" class="form-control" value="{{ old('request_date') ?? now()->format('Y-m-d') }}">
                    <span class="d-block text-danger small mt-1" id="error_request_date"></span>
                </div>
            </div>

            <div class="row g-3 my-4">
                <div class="form-group col-md-6">
                    <label for="judul_rekap" class="form-label fw-semibold">Judul Rekap</label>
                    <input type="text" name="judul_rekap" id="judul_rekap" class="form-control" placeholder="Masukkan Judul Rekap">
                    <span class="d-block text-danger small mt-1" id="error_judul_rekap"></span>
                </div>

                <div class="form-group col-md-6">
                    <label for="total_nilai_rekapitulasi" class="form-label fw-semibold">Total Nilai Rekapitulasi</label>
                    <input type="text" name="total_nilai_rekapitulasi" id="total_nilai_rekapitulasi" class="form-control" placeholder="0" min="0">
                    <span class="d-block text-danger small mt-1" id="error_total_nilai_rekapitulasi"></span>
                </div>
            </div>

            <div class="row g-3 my-4">
                <div class="form-group col-md-6">
                    <label for="notes" class="form-label fw-semibold">Informasi Rekap</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Catatan / Deskripsi"></textarea>
                    <span class="d-block text-danger small mt-1" id="error_notes"></span>
                </div>

                <div class="form-group col-md-6">
                    <label for="attachment_link" class="form-label fw-semibold">
                        File Laporan Rekapitulasi
                        <small class="text-muted">(*jika dibutuhkan)</small>
                    </label>
                    <span style="cursor: pointer" id="file_attach" class="d-block mb-2">
                        <i class="fa fa-file-excel"></i> Pilih File
                    </span>
                    <input type="file" name="document" id="attachment_link" class="form-control" style="display: none;">
                    <span class="d-block text-danger small mt-1" id="error_document"></span>
                </div>
            </div>

            <div class="form-group d-flex justify-content-end">
                <button type="button" id="btn-save" class="btn btn-primary btn-modern-rounded">
                    <i class="fas fa-save me-2"></i>Simpan Form
                </button>
            </div>
        </form>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/js/app/logistic/createStockRecap.js') }}"></script>
@endpush
