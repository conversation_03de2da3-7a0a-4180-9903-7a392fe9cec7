@extends('layouts.app')

@push('style')
    <link href="{{ asset('/') }}plugins/datatables.net-bs4/css/dataTables.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css" rel="stylesheet" />
    <link href="{{ asset('/') }}plugins/select2/dist/css/select2.min.css" rel="stylesheet" />
    
    <link href="{{ asset('/') }}css/app/custom_datatable_and_filter.css" rel="stylesheet" />
@endpush

@push('menu')
    @include('menu.logistic')
@endpush

@section('content')
    <div class="datatable-modern-container">
        <div class="datatable-control-bar">
            <div class="datatable-search-container">
                <div class="">
                    <div class="row">
                        <div class="col-md-5 col-sm-12">
                            <div style="font-size: 12px; color: #6c757d; margin-bottom: 4px;">Tipe Rekapitulasi</div>
                            <select name="tipe_rekap" id="tipe_rekap" class="form-select tipe_rekap" style="width: 320px;">
                                <option value="">Semua Tipe Rekap</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="datatable-action-buttons">
                <a href="{{ route('logistic.stock-recap.create') }}" class="btn btn-primary btn-modern-rounded">
                    <i class="fas fa-plus"></i>
                    <span>Buat Rekap</span>
                </a>
                <div class="datatable-filter-icon btn-modern-rounded">
                    <i class="fas fa-filter"></i>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-sm table-striped datatable-modern-table w-100" id="datatable">
                <thead>
                    <tr>
                        <th>Tipe Rekapitulasi</th>
                        <th>Tanggal Rekapitulasi</th>
                        <th>Judul</th>
                        <th>Nilai Rekapitulasi</th>
                        <th style="width: 80px;">Action</th>
                    </tr>
                </thead>
            </table>
        </div>

        <div class="datatable-custom-pagination">
            <div class="datatable-rows-per-page">
                <label>Show:</label>
                <select id="rowsPerPage">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span>entries</span>
            </div>
        </div>
    </div>

    <!-- Filter Drawer -->
    <div class="modal fade" id="filterDrawer" tabindex="-1" role="dialog" aria-labelledby="filterDrawerLabel" aria-hidden="true">
        <div class="modal-dialog modal-drawer" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filterDrawerLabel">Filter Options</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="closeFilterDrawer">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label class="form-label">Tanggal Mulai</label>
                        <input type="date" name="filter_date_start" id="filter_date_start" class="form-control">
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">Tanggal Akhir</label>
                        <input type="date" name="filter_date_end" id="filter_date_end" class="form-control">
                    </div>
                    <div class="d-flex gap-2 mt-4">
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFilter">
                            <i class="fas fa-undo me-1"></i>Reset Filter
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" id="applyFilter">
                            <i class="fas fa-filter me-1"></i>Terapkan Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-dialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Detail Rekap</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                </div>
                <div id="activityBody" class="modal-body"></div>
                <div class="modal-footer">
                    <a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal" id="btn-cancel">Tutup</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="{{ asset('/') }}plugins/datatables.net/js/jquery.dataTables.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('/') }}plugins/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js"></script>
    <script src="{{ asset('/') }}plugins/sweetalert/dist/sweetalert.min.js"></script>
    <script src="{{ asset('/') }}plugins/select2/dist/js/select2.min.js"></script>
    <script src="{{ asset('/') }}plugins/autoNumeric/autoNumeric.min.js"></script>
    <script src="{{ asset('/js/app/logistic/app.js') }}"></script>
    <script src="{{ asset('/js/app/logistic/stockRecap.js') }}"></script>
@endpush
