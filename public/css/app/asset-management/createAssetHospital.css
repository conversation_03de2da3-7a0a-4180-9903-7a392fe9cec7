:root {
    --primary-color: #1e3c72;
    --primary-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --secondary-color: #f8fafc;
    --border-color: #e2e8f0;
    --text-muted: #64748b;
    --text-dark: #334155;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --border-radius: 12px;
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.modern-card:hover {
    box-shadow: var(--shadow-md);
}

.modern-card-body {
    padding: 0.75rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.section-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.section-title {
    flex: 1;
}

.section-title h3 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-dark);
    line-height: 1.2;
}

.section-subtitle {
    color: var(--text-muted);
    font-size: 0.7rem;
    margin: 0.2rem 0 0 0;
    line-height: 1.3;
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.7rem;
    margin-bottom: 0.2rem;
}

.form-control, .form-select {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    background-color: #fff;
    height: 32px;
}

.form-control[type="number"],
.form-control[type="text"],
.form-control[type="date"] {
    height: 32px;
}

textarea.form-control {
    height: auto;
    min-height: 60px;
    resize: vertical;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px rgb(30 60 114 / 0.1);
    outline: none;
}

.form-control:read-only {
    background-color: var(--secondary-color);
    border-color: #cbd5e1;
}

.helper-text {
    color: var(--text-muted);
    font-size: 0.65rem;
    margin-top: 0.15rem;
    line-height: 1.3;
}

.required-indicator {
    color: var(--danger-color);
    font-weight: 600;
}
.btn-modern {
    border-radius: 4px;
    padding: 0.4rem 0.8rem;
    font-weight: 500;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    height: 32px;
}

.btn-primary.btn-modern {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary.btn-modern {
    background: #f1f5f9;
    color: var(--text-dark);
    border: 2px solid var(--border-color);
}

.btn-secondary.btn-modern:hover {
    background: #e2e8f0;
    border-color: #cbd5e1;
}

.btn-success.btn-modern {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.btn-success.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-buttons {
    display: flex;
    gap: 0.4rem;
    justify-content: flex-end;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.items-section {
    background: var(--secondary-color);
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 1rem;
}

.subsections {
    margin-top: 0.5rem;
}

.subsection {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: #fff;
    margin-bottom: 0.75rem;
    overflow: hidden;
}

.subsection-toggle {
    width: 100%;
    border: none;
    background: transparent;
    padding: 0.4rem 0.5rem;
    text-align: left;
}

.subsection-toggle:hover {
    background: var(--secondary-color);
}

.subsection-header {
    margin: 0;
    border-bottom: none;
}

.subsection .toggle-icon {
    margin-left: auto;
    color: var(--text-muted);
    transition: transform 0.2s ease;
}

.subsection.is-open .toggle-icon {
    transform: rotate(180deg);
}

.subsection-content {
    padding: 0.5rem 0.5rem 0.75rem;
    border-top: 1px solid var(--border-color);
    background: #fff;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

@media (min-width: 1200px) {
    .form-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.form-group {
    display: flex;
    flex-direction: column;
}

.textarea-field {
    grid-column: 1 / -1;
}

/* Select2 Modern Styling - Match Form Controls */
.select2-container--default .select2-selection--single {
    height: 32px !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 4px !important;
    padding: 0 !important;
    background-color: #fff !important;
    line-height: 32px !important; /* match container height for vertical centering */
    position: relative;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 32px !important;
    padding-left: 0.6rem !important;
    padding-right: 2.2rem !important; /* space for clear + caret */
    color: var(--text-dark) !important;
    font-size: 0.75rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    position: absolute !important;
    right: 1.6rem !important; /* ensure spacing from caret */
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #999 !important;
    font-size: 0.9rem !important;
    line-height: 1 !important;
    padding: 0 !important;
    margin: 0 !important;
    cursor: pointer !important;
    z-index: 10 !important;
}

.select2-container--default .select2-selection--single .select2-selection__clear:hover {
    color: var(--danger-color) !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100% !important; /* fill the selection height */
    top: 0 !important;
    right: 0.5rem !important;
    width: 14px !important; /* smaller width for slimmer caret area */
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #999 transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 3px 3px 0 3px !important; /* smaller triangle */
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important; /* perfectly center caret */
    margin: 0 !important;
}

/* Arrow rotation on open */
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #999 transparent !important;
    border-width: 0 3px 3px 3px !important; /* keep size consistent when rotated */
}

.select2-container--default.select2-container--focus .select2-selection--single {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 1px rgb(30 60 114 / 0.1) !important;
}

.select2-dropdown {
    border: 1px solid var(--border-color) !important;
    border-radius: 4px !important;
    box-shadow: var(--shadow-md) !important;
    font-size: 0.75rem !important;
    z-index: 9999 !important;
}

.select2-results__option {
    padding: 0.4rem 0.6rem !important;
    font-size: 0.75rem !important;
}

.select2-results__option--highlighted {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* Fix for placeholder styling */
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--text-muted) !important;
}

/* Panel Override for Modern Look */
.panel {
    border: none;
    box-shadow: none;
    background: transparent;
}

.panel-heading {
    display: none;
}

.panel-body {
    padding: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .modern-card-body {
        padding: 0.5rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.4rem;
    }

    .section-icon {
        width: 28px;
        height: 28px;
    }

    .simple-table-card {
        overflow-x: auto;
    }

    .simple-table {
        min-width: 600px;
    }
}

/* Simple Table Styling */
.simple-table-card {
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.simple-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    font-size: 0.75rem;
}

.simple-table th {
    background: #f8fafc;
    border-bottom: 1px solid var(--border-color);
    padding: 0.6rem 0.75rem;
    font-weight: 600;
    color: var(--text-dark);
    text-align: left;
    font-size: 0.7rem;
}

.simple-table td {
    padding: 0.6rem 0.75rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.simple-table tr:hover {
    background: #f8fafc;
}

.simple-table tr:last-child td {
    border-bottom: none;
}

.simple-code {
    background: #e0f2fe;
    color: #0277bd;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-weight: 600;
    font-size: 0.7rem;
    font-family: monospace;
}

.simple-serial {
    background: #f1f8e9;
    color: #388e3c;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-weight: 600;
    font-size: 0.7rem;
    font-family: monospace;
}

.simple-footer {
    background: #f8fafc;
    border-top: 1px solid var(--border-color);
    padding: 0.5rem 0.75rem;
    text-align: center;
}

/* Action Button Styling */
.simple-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    border-radius: 3px;
    line-height: 1.2;
    min-width: 32px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.simple-table .btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.simple-table .btn-outline-primary:hover {
    color: white;
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Highlight for newly added row */
.new-row-highlight {
    background-color: #fff3cd;
    border: 2px solid #ffc107;
    transition: all 0.3s ease;
}
