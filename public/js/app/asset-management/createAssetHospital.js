// Centralized endpoints and small helpers
const ENDPOINTS = {
    distributor: '/dropdown/distributor',
    item: '/dropdown/item',
    uom: '/dropdown/uom',
    aspakItemLeaves: '/master-data/aspak/item/dropdown/leaves',
    aspakRoom: '/master-data/aspak/room/dropdown/room',
    siapActivity: '/master-data/siap-bmd/program/dropdown/activity',
    siapSubActivity: '/master-data/siap-bmd/program/dropdown/sub-activity',
    formSubmit: '/manajemen-aset/data-aset/store'
};

// Debounces function calls to limit execution frequency
function debounce(fn, wait) {
    let t;
    return function (...args) {
        clearTimeout(t);
        t = setTimeout(() => fn.apply(this, args), wait);
    };
}

// Initializes Select2 dropdown with AJAX data loading
function initSelect2Ajax($el, { url, placeholder, mapItem, extraData }) {
    return $el.select2({
        placeholder: placeholder || 'Pilih data',
        allowClear: true,
        width: '100%',
        escapeMarkup: function (m) { return m; },
        ajax: {
            url,
            dataType: 'json',
            delay: 250,
            data: function (params) {
                const base = { q: params.term, page: params.page || 1 };
                const extra = typeof extraData === 'function' ? (extraData() || {}) : (extraData || {});
                return Object.assign(base, extra);
            },
            processResults: function (data) {
                return {
                    results: (data.data || []).map(mapItem),
                    pagination: { more: data.current_page < data.last_page }
                };
            }
        }
    });
}

// Initializes page on document ready
$(document).ready(function () {
    initializePageData();
    initializeForm();
    initializeSubsections();
    initializeSelect2Handlers();
    initializeDatePickers();
    initializeAspakSync();
    initializeSiapBmdSync();
    initializeSiapBmdDataUmumSync();
    initializeAspakDataUmumSync();

    // Add first row
    addItemRow();

    // Event handlers for item management
    $('#addItemBtn').on('click', addItemRow);
    $(document).on('click', '.remove-item-btn', removeItemRow);
    $(document).on('click', '.copy-add-item-btn', copyAndAddItemRow);
    $(document).on('input change', '.item-unit-price', debounce(calculateTotals, 150));

    // Handles form submission confirmation dialog
    $('#submitBtn').on('click', function (e) {
        e.preventDefault();
        const el = document.getElementById('confirmSubmissionModal');
        (bootstrap.Modal.getInstance(el) || new bootstrap.Modal(el)).show();
    });
    $('#confirmSubmitBtn').on('click', function () {
        const el = document.getElementById('confirmSubmissionModal');
        const modal = bootstrap.Modal.getInstance(el);
        if (modal) modal.hide();
        handleFormSubmit($.Event('submit'));
    });

    // Prevents native form submission
    $('#formdata').off('submit').on('submit', function (e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });

    // Clears error messages when feedback modal closes
    $('#formFeedbackModal').on('hidden.bs.modal', function () {
        $('#errorMessagesList').empty();
        $('#errorMessagesSection').addClass('d-none');
    });

    // Optional: quick autofill
    if (window.location.hash === '#autofill') {
        setTimeout(autofillForm, 100);
    }
});

// Utility functions
// Returns current date in YYYY-MM-DD format
function getTodayDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// Initializes date picker fields with restrictions
function initializeDatePickers() {
    const $fields = $('#entry_date, #payment_date');
    if (!$fields.length || typeof $.fn.datepicker !== 'function') return;

    // Ensure not disabled; block manual typing via events (no readonly attr)
    $fields.prop('disabled', false).attr('autocomplete', 'off');

    $fields.datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        orientation: 'bottom'
    }).on('changeDate', function () {
        // Propagate to existing change listeners used for derived fields
        $(this).trigger('change');
    });

    // Block manual input and paste/drop
    $fields.on('keydown', function (e) {
        // allow Tab for navigation
        if (e.key === 'Tab' || e.keyCode === 9) return;
        e.preventDefault();
    });
    $fields.on('paste drop', function (e) { e.preventDefault(); });

    // Open picker on focus/click for smoother UX
    $fields.on('focus click', function () { $(this).datepicker('show'); });
}

// Development helper: populates form with sample data
function autofillForm() {
    const today = getTodayDate();
    $('#entry_date').val(today).trigger('change');
    $('#payment_date').val(today).trigger('change');
    $('#contract_number').val('TEST_NO_KONTRAK');
    $('#job_description').val('TEST_URAIAN_PEKERJAAN');
    $('#payment_ba_number').val('NO_BA_PEMBAYARAN');
    $('#acquisition_source').val('apbn').trigger('change');
    $('#contract_type').val('BENTUK_KONTRAK');
    $('#description').val('KETARANGAN');

    const distributorSelect = $('#distributor_id');
    if (distributorSelect.find('option[value="1"]').length === 0) {
        distributorSelect.append(new Option('CV. Areta', '1', true, true));
    }
    distributorSelect.val('1').trigger('change');

    $('#aspak_distributor').val('CV. Areta');
    $('#aspak_maintenance_officer').val('PJ_PEMELIHARAAN_ASPAK');

    $('#siap_bmd_rkbmd_code').val(rkbmdPrefix);
    $('#siap_bmd_hospital_name').val(hospitalName);
    $('#siap_bmd_kusa_name').val("NAMA_KUSA");

    const siapBmdActivitySelect = $('#siap_bmd_activity');
    if (siapBmdActivitySelect.find('option[value="2"]').length === 0) {
        siapBmdActivitySelect.append(new Option('*********.01 - PERENCANAAN, PENGANGGARAN, DAN EVALUASI KINERJA PERANGKAT DAERAH', '2', true, true));
    }
    siapBmdActivitySelect.val('2').trigger('change');

    const siapBmdSubActivitySelect = $('#siap_bmd_sub_activity');
    if (siapBmdSubActivitySelect.find('option[value="5"]').length === 0) {
        siapBmdSubActivitySelect.append(new Option('*********.01.0003 - Koordinasi dan Penyusunan Dokumen Perubahan RKA-SKPD', '5', true, true));
    }
    siapBmdSubActivitySelect.val('5').trigger('change');

    const firstItemRow = $('.item-row').first();
    const itemIndex = firstItemRow.data('index');

    const itemCodeSelect = firstItemRow.find('.item-code');
    if (itemCodeSelect.find('option[value="10"]').length === 0) {
        itemCodeSelect.append(new Option('********.01.01.001 - Aspal', '10', true, true));
    }
    itemCodeSelect.val('10').trigger('change');

    firstItemRow.find(`[name="items[${itemIndex}][item_name]"]`).val('Aspal');
    firstItemRow.find(`[name="items[${itemIndex}][contract_item_name]"]`).val('Aspal');
    firstItemRow.find(`[name="items[${itemIndex}][brand]"]`).val('MERK');
    firstItemRow.find(`[name="items[${itemIndex}][type_model]"]`).val('MODEL');
    firstItemRow.find(`[name="items[${itemIndex}][material]"]`).val('BAHAN');
    firstItemRow.find(`[name="items[${itemIndex}][size]"]`).val('UKURAN');
    firstItemRow.find(`[name="items[${itemIndex}][unit_price]"]`).val('10000');
    firstItemRow.find(`[name="items[${itemIndex}][serial_number]"]`).val('SN');
    firstItemRow.find(`[name="items[${itemIndex}][rfid_tag]"]`).val('RFID');
    firstItemRow.find(`[name="items[${itemIndex}][frame_number]"]`).val('RANGKA');
    firstItemRow.find(`[name="items[${itemIndex}][engine_number]"]`).val('NO_MESIN');
    firstItemRow.find(`[name="items[${itemIndex}][akd_akl_number]"]`).val('NO_AKD_AKL');
    firstItemRow.find(`[name="items[${itemIndex}][police_number]"]`).val('NO_POLISI');
    firstItemRow.find(`[name="items[${itemIndex}][bpkb_number]"]`).val('NO_BPKB');
    firstItemRow.find(`[name="items[${itemIndex}][condition]"]`).val('RUSAK_RINGAN');
    firstItemRow.find(`[name="items[${itemIndex}][general_specification]"]`).val('SPESIFIKASI_UMUM');
    firstItemRow.find(`[name="items[${itemIndex}][asset_description]"]`).val('KETERANGAN_ASET');

    const uomSelect = firstItemRow.find(`[name="items[${itemIndex}][uom_id]"]`);
    if (uomSelect.find('option[value="2"]').length === 0) {
        uomSelect.append(new Option('Buah', '2', true, true));
    }
    uomSelect.val('2').trigger('change');

    const aspakItemSelect = firstItemRow.find(`[name="items[${itemIndex}][aspak_item_id]"]`);
    if (aspakItemSelect.find('option[value="3"]').length === 0) {
        aspakItemSelect.append(new Option('010101 - Stetoskop', '3', true, true));
    }
    aspakItemSelect.val('3').trigger('change');

    const aspakServiceRoomSelect = firstItemRow.find(`[name="items[${itemIndex}][aspak_service_room_id]"]`);
    if (aspakServiceRoomSelect.find('option[value="3"]').length === 0) {
        aspakServiceRoomSelect.append(new Option('010101 - Ruangan Klinik Sp. Penyakit Dalam', '3', true, true));
    }
    aspakServiceRoomSelect.val('3').trigger('change');

    firstItemRow.find(`[name="items[${itemIndex}][aspak_merk]"]`).val('MERK');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_merk_tipe]"]`).val('MERK_TIPE');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_kondisi_alat]"]`).val('RUSAK_RINGAN');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_alat]"]`).val('ALAT_ASPAK');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_product_origin]"]`).val('DOMESTIC');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_description]"]`).val('KETERANGAN_ASPAK');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_image_url]"]`).val('https://dummyimage.com/640x4:3/');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_power_source]"]`).val('AC');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_electricity]"]`).val('400');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_tersambung_ups]"]`).prop('checked', true);
    firstItemRow.find(`[name="items[${itemIndex}][aspak_information_tkdn]"]`).val('TKDN_ASPAK');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_risk_class]"]`).val('RESIKO_ASPAK');
    firstItemRow.find(`[name="items[${itemIndex}][aspak_technical_lifetime_year]"]`).val('UMUR_TEKNIS_ASPAK');


    // SIAP BMD
    firstItemRow.find(`[name="items[${itemIndex}][siap_bmd_item_code]"]`).val('********.01.01.001');
    firstItemRow.find(`[name="items[${itemIndex}][siap_bmd_asset_name]"]`).val('Aspal');
    firstItemRow.find(`[name="items[${itemIndex}][siap_bmd_common_name]"]`).val('Aspal');
    firstItemRow.find(`[name="items[${itemIndex}][siap_bmd_model]"]`).val('MODEL');
    firstItemRow.find(`[name="items[${itemIndex}][siap_bmd_specification]"]`).val('SPESIFIKASI_UMUM');
    firstItemRow.find(`[name="items[${itemIndex}][siap_bmd_placement_description]"]`).val('KETERANGAN_ASET');

    calculateTotals();
}

// Synchronizes ASPAK header fields with form data
function initializeAspakSync() {
    let aspakDistributorManuallyEdited = false;

    $('#aspak_distributor').on('input', function () { aspakDistributorManuallyEdited = true; });

    $('#distributor_id').on('select2:select', function (e) {
        if (!aspakDistributorManuallyEdited) {
            $('#aspak_distributor').val(e.params.data.text);
        }
    });
    $('#distributor_id').on('select2:clear', function () {
        if (!aspakDistributorManuallyEdited) {
            $('#aspak_distributor').val('');
        }
    });
    $('#formdata').on('submit', function () { aspakDistributorManuallyEdited = false; });

    $('#entry_date').on('change', function () {
        const entryDate = $(this).val();
        $('#aspak_procurement_year').val(entryDate ? new Date(entryDate).getFullYear() : '');
    });

    $('#acquisition_source').on('change', function () {
        const selectedText = $(this).find('option:selected').text();
        $('#aspak_procurement_source').val($(this).val() ? selectedText : '');
    });

    $('#total_price').on('input', function () { $('#aspak_acquisition_price').val($(this).val()); });

    initializeAspakValues();
}

// Initializes ASPAK field values from form data
function initializeAspakValues() {
    const distributorText = $('#distributor_id option:selected').text();
    if (distributorText && distributorText !== 'Pilih Distributor') {
        $('#aspak_distributor').val(distributorText);
    }
    const entryDate = $('#entry_date').val();
    if (entryDate) $('#aspak_procurement_year').val(new Date(entryDate).getFullYear());
    const acquisitionSource = $('#acquisition_source').val();
    if (acquisitionSource) $('#aspak_procurement_source').val($('#acquisition_source option:selected').text());
    const totalPrice = $('#total_price').val();
    if (totalPrice) $('#aspak_acquisition_price').val(totalPrice);
}

// Synchronizes SIAP BMD header fields with form data
function initializeSiapBmdSync() {
    $('#entry_date').on('change', function () {
        const entryDate = $(this).val();
        $('#siap_bmd_year').val(entryDate ? new Date(entryDate).getFullYear() : '');
    });
    $('#total_price').on('input change', function () { $('#siap_bmd_price').val($(this).val()); });
    $(document).on('input change', '.item-quantity', updateSiapBmdQuantity);
    $(document).on('click', '.remove-item-btn', function () { setTimeout(updateSiapBmdQuantity, 100); });
    initializeSiapBmdValues();
}

// Initializes SIAP BMD field values from form data
function initializeSiapBmdValues() {
    const entryDate = $('#entry_date').val();
    if (entryDate) $('#siap_bmd_year').val(new Date(entryDate).getFullYear());
    const totalPrice = $('#total_price').val();
    if (totalPrice) $('#siap_bmd_price').val(totalPrice);
    updateSiapBmdQuantity();
}

// Updates SIAP BMD quantity based on item quantities
function updateSiapBmdQuantity() {
    let totalQuantity = 0;
    $('.item-quantity').each(function () { totalQuantity += parseInt($(this).val()) || 0; });
    $('#siap_bmd_quantity').val(totalQuantity);
}

// Synchronizes SIAP BMD item fields with general data fields
function initializeSiapBmdDataUmumSync() {
    $(document).on('select2:select', '.item-code', function (e) {
        const itemRow = $(this).closest('.item-row');
        const idx = itemRow.data('index');
        const text = e.params && e.params.data ? e.params.data.text : $(this).find('option:selected').text();
        const parts = text.split(' - ');
        const code = parts[0] || '';
        const name = parts.slice(1).join(' - ') || '';
        itemRow.find(`[name="items[${idx}][siap_bmd_item_code]"]`).val(code);
        itemRow.find(`[name="items[${idx}][siap_bmd_asset_name]"]`).val(name);
        itemRow.find(`[name="items[${idx}][siap_bmd_common_name]"]`).val(name);
    });
    $(document).on('input', '[name^="items["][name$="][item_name]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][siap_bmd_asset_name]"]`).val($(this).val());
    });
    $(document).on('input', '[name^="items["][name$="][contract_item_name]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][siap_bmd_common_name]"]`).val($(this).val());
    });
    $(document).on('input', '[name^="items["][name$="][type_model]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][siap_bmd_model]"]`).val($(this).val());
    });
    $(document).on('input', '[name^="items["][name$="][general_specification]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][siap_bmd_specification]"]`).val($(this).val());
    });
    $(document).on('input', '[name^="items["][name$="][asset_description]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][siap_bmd_placement_description]"]`).val($(this).val());
    });
}

// Synchronizes ASPAK item fields with general data fields
function initializeAspakDataUmumSync() {
    $(document).on('input', '[name^="items["][name$="][brand]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][aspak_merk]"]`).val($(this).val());
    });
    $(document).on('change', '[name^="items["][name$="][condition]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][aspak_kondisi_alat]"]`).val($(this).val());
    });
    $(document).on('input', '[name^="items["][name$="][type_model]"]', function () {
        const row = $(this).closest('.item-row');
        const idx = row.data('index');
        row.find(`[name="items[${idx}][aspak_merk_tipe]"]`).val($(this).val());
    });
    $(document).on('select2:select', '.aspak-nama-barang', function () { /* keep ID; capture text at submit if needed */ });
    $(document).on('select2:select', '.aspak-lokasi', function () { /* keep ID; capture text at submit if needed */ });
}

// Initializes collapsible subsection toggles
function initializeSubsections() {
    $(document).on('click', '.subsection-toggle', function () {
        const $btn = $(this);
        const expanded = $btn.attr('aria-expanded') === 'true';
        const controls = $btn.attr('aria-controls');
        const $content = $('#' + controls);
        const $container = $btn.closest('.subsection');

        if (expanded) {
            $btn.attr('aria-expanded', 'false');
            $container.removeClass('is-open');
            $content.stop(true, true).slideUp(150);
        } else {
            $btn.attr('aria-expanded', 'true');
            $container.addClass('is-open');
            $content.stop(true, true).slideDown(150);
        }
    });
}

// Initializes form components and dropdowns
function initializeForm() {
    // Static select2
    $('#acquisition_source').select2({
        placeholder: 'Pilih Asal Perolehan', allowClear: true, width: '100%',
        escapeMarkup: function (m) { return m; },
        templateResult: function (d) { return d.text; },
        templateSelection: function (d) { return d.text; }
    });

    // Distributor
    initSelect2Ajax($('#distributor_id'), {
        url: ENDPOINTS.distributor,
        placeholder: 'Pilih Distributor',
        mapItem: function (item) { return { id: item.id, text: item.distributor_name }; }
    });

    // Currency + numeric inputs
    initializeCurrencyInputs();
    initializeNumericOnlyInputs();

    // SIAP BMD dropdowns
    initializeSiapBmdDropdowns();
}

// Restricts input to numeric characters only
function initializeNumericOnlyInputs() {
    $(document).on('keypress', '.item-unit-price', function (e) {
        const ch = String.fromCharCode(e.which || e.keyCode);
        if (!/[0-9]/.test(ch)) e.preventDefault();
    });
}

// Loads initial page data from form attributes
function initializePageData() {
    const $form = $('#formdata');
    if ($form.length) {
        const hospital = $form.data('hospital-name');
        const prefix = $form.data('rkbmd-prefix');
        if (typeof hospital !== 'undefined') { window.hospitalName = hospital; }
        if (typeof prefix !== 'undefined') { window.rkbmdPrefix = prefix; }
    }
}

// Initializes Select2 event handlers for custom interactions
function initializeSelect2Handlers() {
    $(document).on('click', '.select2-selection__clear', function (e) {
        e.stopPropagation();
        const $select = $(this).closest('.select2-container').prev('select');
        $select.val(null).trigger('change');
        return false;
    });
    $(document).on('click', '.select2-selection__arrow', function (e) {
        e.stopPropagation();
        const $container = $(this).closest('.select2-container');
        const $select = $container.prev('select');
        if ($container.hasClass('select2-container--open')) { $select.select2('close'); } else { $select.select2('open'); }
        return false;
    });
    $(document).on('select2:select', '.form-select', function () {
        $(this).next('.select2-container').find('.select2-selection__clear').show();
    });
}

// Initializes SIAP BMD activity and sub-activity dropdowns
function initializeSiapBmdDropdowns() {
    initSelect2Ajax($('#siap_bmd_activity'), {
        url: ENDPOINTS.siapActivity,
        placeholder: 'Pilih Kegiatan',
        mapItem: function (item) { return { id: item.id, text: item.text }; }
    });
    initSelect2Ajax($('#siap_bmd_sub_activity'), {
        url: ENDPOINTS.siapSubActivity,
        placeholder: 'Pilih Sub Kegiatan',
        mapItem: function (item) { return { id: item.id, text: item.text }; },
        extraData: function () { return { activity_id: $('#siap_bmd_activity').val() }; }
    }).prop('disabled', true);

    $('#siap_bmd_activity').on('select2:select', function () { $('#siap_bmd_sub_activity').prop('disabled', false); });
    $('#siap_bmd_activity').on('select2:clear', function () { $('#siap_bmd_sub_activity').prop('disabled', true).val(null).trigger('change'); });
}

// Initializes currency input formatting
function initializeCurrencyInputs() {
    $(document).on('input', '.currency-input', function () { formatCurrency(this); });
}
// Formats input value as Indonesian Rupiah currency
function formatCurrency(input) {
    let value = input.value.replace(/[^\d]/g, '');
    if (!value) { input.value = ''; return; }
    value = parseInt(value, 10).toLocaleString('id-ID');
    if (input.classList.contains('no-prefix')) { input.value = value; } else { input.value = 'Rp ' + value; }
}
// Extracts numeric value from formatted currency string
function getCurrencyValue(currencyString) {
    if (!currencyString) return 0;
    return parseInt(currencyString.replace(/[^\d]/g, '')) || 0;
}

// Converts dot notation to bracket notation for form fields
function convertDotToBracketNotation(fieldName) {
    if (!fieldName.includes('.')) return fieldName;
    const parts = fieldName.split('.');
    let result = parts[0];
    for (let i = 1; i < parts.length; i++) { result += `[${parts[i]}]`; }
    return result;
}
// Sets nested object value using bracket notation path
function setNestedValue(obj, path, value) {
    const keys = path.replace(/\]/g, '').split('[');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!key) continue;
        if (!(key in current)) {
            const nextKey = keys[i + 1];
            current[key] = isNaN(nextKey) ? {} : [];
        }
        current = current[key];
    }
    const lastKey = keys[keys.length - 1];
    if (lastKey !== '') current[lastKey] = value;
}

// Adds a new item row to the form
function addItemRow() {
    const itemIndex = $('.item-row').length;
    const itemHtml = `
        <div class="item-row border rounded p-3 mb-3" data-index="${itemIndex}">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="text-secondary mb-0"><i class="fas fa-box me-2"></i>Barang ${itemIndex + 1}</h6>
                <div>
                    <button type="button" class="btn btn-success btn-sm me-2 copy-add-item-btn" data-row-index="${itemIndex}">
                        <i class="fas fa-copy me-1"></i>Salin dan Tambah
                    </button>
                    <button type="button" class="btn btn-danger btn-sm remove-item-btn" ${itemIndex === 0 ? 'style="display:none;"' : ''}>
                        <i class="fas fa-trash me-1"></i>Hapus
                    </button>
                </div>
            </div>

            <!-- Column 1: Item codes, names -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][item_id]" class="form-label">Kode Barang <span class="required-indicator">*</span></label>
                    <select class="form-select item-code" name="items[${itemIndex}][item_id]" required data-placeholder="Pilih Kode Barang">
                        <option value="">Pilih Kode Barang</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][item_name]" class="form-label">Nama Barang <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][item_name]" required placeholder="Masukkan nama barang">
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][contract_item_name]" class="form-label">Nama Barang di Kontrak <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][contract_item_name]" placeholder="Masukkan nama barang di kontrak" required>
                    <div class="invalid-feedback"></div>
                </div>
            </div>

            <!-- Merk and No. AKD/AKL row -->
            <div class="row">
                <div class="col-lg-6 col-md-6 mb-3">
                    <label for="items[${itemIndex}][brand]" class="form-label">Merk <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][brand]" placeholder="Masukkan merk" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-6 col-md-6 mb-3">
                    <label for="items[${itemIndex}][akd_akl_number]" class="form-label">No. AKD/AKL <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][akd_akl_number]" placeholder="Masukkan nomor AKD/AKL" required>
                    <div class="invalid-feedback"></div>
                </div>
            </div>

            <!-- Column 2: Brand, type, specifications -->
            <div class="row">
                <div class="col-lg-6 col-md-6 mb-3">
                    <label for="items[${itemIndex}][type_model]" class="form-label">Tipe/Model <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][type_model]" placeholder="Masukkan tipe/model" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-6 col-md-6 mb-3">
                    <label for="items[${itemIndex}][general_specification]" class="form-label">Spesifikasi Umum</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][general_specification]" placeholder="Masukkan spesifikasi umum">
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][material]" class="form-label">Bahan <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][material]" placeholder="Masukkan bahan" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][size]" class="form-label">Ukuran <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][size]" placeholder="Masukkan ukuran" required>
                    <div class="invalid-feedback"></div>
                </div>
            </div>

            <!-- Column 3: Material, size, unit, prices -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][uom_id]" class="form-label">Satuan Barang <span class="required-indicator">*</span></label>
                    <select class="form-select item-uom" name="items[${itemIndex}][uom_id]" required data-placeholder="Pilih Satuan">
                        <option value="">Pilih Satuan</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][unit_price]" class="form-label">Harga Satuan <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control currency-input no-prefix item-unit-price" name="items[${itemIndex}][unit_price]" required placeholder="Masukkan harga satuan">
                    <div class="invalid-feedback"></div>
                </div>
            </div>

            <!-- Column 4: Identification numbers -->
            <div class="row">
                <div class="col-12 mb-3">
                    <label for="items[${itemIndex}][asset_description]" class="form-label">Keterangan Asset</label>
                    <textarea class="form-control" name="items[${itemIndex}][asset_description]" rows="2" placeholder="Masukkan keterangan asset"></textarea>
                    <div class="invalid-feedback"></div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][serial_number]" class="form-label">No. SN <span class="required-indicator">*</span></label>
                    <input type="text" class="form-control" name="items[${itemIndex}][serial_number]" placeholder="Masukkan nomor serial" required>
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][rfid_tag]" class="form-label">Tag RFID</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][rfid_tag]" placeholder="Masukkan tag RFID">
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][frame_number]" class="form-label">No. Rangka</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][frame_number]" placeholder="Masukkan nomor rangka">
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][engine_number]" class="form-label">No. Mesin</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][engine_number]" placeholder="Masukkan nomor mesin">
                    <div class="invalid-feedback"></div>
                </div>
            </div>

            <!-- Column 5: Additional identification -->
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][police_number]" class="form-label">No. Polisi</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][police_number]" placeholder="Masukkan nomor polisi">
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][bpkb_number]" class="form-label">No. BPKB</label>
                    <input type="text" class="form-control" name="items[${itemIndex}][bpkb_number]" placeholder="Masukkan nomor BPKB">
                    <div class="invalid-feedback"></div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <label for="items[${itemIndex}][condition]" class="form-label">Kondisi <span class="required-indicator">*</span></label>
                    <select class="form-select" name="items[${itemIndex}][condition]" required>
                        <option value="BAIK" selected>BAIK</option>
                        <option value="RUSAK_RINGAN">RUSAK_RINGAN</option>
                        <option value="RUSAK_BERAT">RUSAK_BERAT</option>
                    </select>
                    <div class="invalid-feedback"></div>
                </div>
            </div>

            <!-- Sub Sections for Item -->
            <div class="subsections mt-3">
                <!-- ASPAK Sub Section for Item -->
                <div class="subsection" id="item-aspak-section-${itemIndex}">
                    <button type="button" class="subsection-toggle" aria-expanded="false" aria-controls="item-aspak-content-${itemIndex}">
                        <div class="section-header subsection-header">
                            <div class="section-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="section-title">
                                <h3>ASPAK</h3>
                                <p class="section-subtitle">Data ASPAK untuk barang ini</p>
                            </div>
                            <i class="toggle-icon fas fa-chevron-down"></i>
                        </div>
                    </button>
                    <div class="subsection-content" id="item-aspak-content-${itemIndex}" style="display: none;">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_item_id]">Nama Barang</label>
                                <select class="form-select aspak-nama-barang" name="items[${itemIndex}][aspak_item_id]" data-placeholder="Pilih Nama Barang">
                                    <option value="">Pilih Nama Barang</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_service_room_id]">Lokasi</label>
                                <select class="form-select aspak-lokasi" name="items[${itemIndex}][aspak_service_room_id]" data-placeholder="Pilih Lokasi">
                                    <option value="">Pilih Lokasi</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_merk]">Merk Alat</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][aspak_merk]" placeholder="Masukkan merk" readonly disabled>
                                <small class="helper-text">Duplikat dari Merk di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][aspak_merk]_error"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_merk_tipe]">Merk Tipe</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][aspak_merk_tipe]" placeholder="Masukkan merk tipe" readonly disabled>
                                <small class="helper-text">Duplikat dari Tipe/Model di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][aspak_merk_tipe]_error"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_alat]">Alat</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][aspak_alat]" placeholder="Masukkan nama alat">
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_kondisi_alat]">Kondisi Alat</label>
                                <select class="form-select" name="items[${itemIndex}][aspak_kondisi_alat]" readonly disabled>
                                    <option value="BAIK" selected>BAIK</option>
                                    <option value="RUSAK_RINGAN">RUSAK_RINGAN</option>
                                    <option value="RUSAK_BERAT">RUSAK_BERAT</option>
                                </select>
                                <small class="helper-text">Duplikat dari Kondisi di informasi umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][aspak_kondisi_alat]_error"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_product_origin]">Produk</label>
                                <select class="form-select" name="items[${itemIndex}][aspak_product_origin]">
                                    <option value="">Pilih Produk</option>
                                    <option value="DOMESTIC">Dalam Negeri</option>
                                    <option value="FOREIGN">Luar Negeri</option>
                                </select>
                            </div>
                            <div class="form-group textarea-field">
                                <label class="form-label" for="items[${itemIndex}][aspak_description]">Keterangan</label>
                                <textarea class="form-control" name="items[${itemIndex}][aspak_description]" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_image_url]">Link Gambar</label>
                                <input type="url" class="form-control" name="items[${itemIndex}][aspak_image_url]" placeholder="https://...">
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_power_source]">Sumber daya</label>
                                <select class="form-select" name="items[${itemIndex}][aspak_power_source]">
                                    <option value="">Pilih Sumber Daya</option>
                                    <option value="AC">AC</option>
                                    <option value="DC">DC</option>
                                    <option value="None">None</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_electricity]">Daya (Watt)</label>
                                <input type="number" class="form-control" name="items[${itemIndex}][aspak_electricity]" placeholder="Contoh: 500">
                            </div>
                            <div class="form-group" style="display:flex;align-items:center;gap:.5rem;">
                                <input type="checkbox" class="form-check-input" id="items-${itemIndex}-aspak_connected_ups" name="items[${itemIndex}][aspak_tersambung_ups]" value="1">
                                <label class="form-label" for="items-${itemIndex}-aspak_connected_ups" style="margin:0;">Tersambung ke UPS</label>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_information_tkdn]">Informasi TKDN</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][aspak_information_tkdn]" placeholder="Masukkan informasi TKDN">
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_risk_class]">Kelas Risiko</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][aspak_risk_class]" placeholder="Masukkan kelas risiko">
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][aspak_technical_lifetime_year]">Umur Teknis (tahun)</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][aspak_technical_lifetime_year]" placeholder="Contoh: 3">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SIAP BMD Sub Section for Item -->
                <div class="subsection" id="item-siapbmd-section-${itemIndex}">
                    <button type="button" class="subsection-toggle" aria-expanded="false" aria-controls="item-siapbmd-content-${itemIndex}">
                        <div class="section-header subsection-header">
                            <div class="section-icon" style="background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="section-title">
                                <h3>SIAP BMD</h3>
                                <p class="section-subtitle">Data SIAP BMD untuk barang ini</p>
                            </div>
                            <i class="toggle-icon fas fa-chevron-down"></i>
                        </div>
                    </button>
                    <div class="subsection-content" id="item-siapbmd-content-${itemIndex}" style="display: none;">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][siap_bmd_item_code]">Kode Barang</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][siap_bmd_item_code]" placeholder="Otomatis dari Kode Barang" readonly>
                                <small class="helper-text">Duplikat dari Kode Barang di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][siap_bmd_item_code]_error"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][siap_bmd_asset_name]">Nama Aset</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][siap_bmd_asset_name]" placeholder="Otomatis dari Nama Barang" readonly>
                                <small class="helper-text">Duplikat dari Nama Barang di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][siap_bmd_asset_name]_error"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][siap_bmd_common_name]">Nama Umum</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][siap_bmd_common_name]" placeholder="Otomatis dari Nama Barang di Kontrak" readonly>
                                <small class="helper-text">Duplikat dari Nama Barang di Kontrak di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][siap_bmd_common_name]_error"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label" for="items[${itemIndex}][siap_bmd_model]">Model</label>
                                <input type="text" class="form-control" name="items[${itemIndex}][siap_bmd_model]" placeholder="Otomatis dari Tipe/Model" readonly>
                                <small class="helper-text">Duplikat dari Tipe/Model di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][siap_bmd_model]_error"></div>
                            </div>
                            <div class="form-group textarea-field">
                                <label class="form-label" for="items[${itemIndex}][siap_bmd_specification]">Spesifikasi</label>
                                <textarea class="form-control" name="items[${itemIndex}][siap_bmd_specification]" rows="2" placeholder="Otomatis dari Spesifikasi Umum" readonly></textarea>
                                <small class="helper-text">Duplikat dari Spesifikasi Umum di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][siap_bmd_specification]_error"></div>
                            </div>
                            <div class="form-group textarea-field">
                                <label class="form-label" for="items[${itemIndex}][siap_bmd_placement_description]">Deskripsi</label>
                                <textarea class="form-control" name="items[${itemIndex}][siap_bmd_placement_description]" rows="2" placeholder="Otomatis dari Keterangan Asset" readonly></textarea>
                                <small class="helper-text">Duplikat dari Keterangan Asset di informasi Umum</small>
                                <div class="invalid-feedback" id="items[${itemIndex}][siap_bmd_placement_description]_error"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#itemsContainer').append(itemHtml);

    initializeItemDropdowns(itemIndex);

    const newItemRow = $(`.item-row[data-index="${itemIndex}"]`);
    const itemCode = '';
    const itemName = newItemRow.find(`[name="items[${itemIndex}][item_name]"]`).val();
    const commonName = newItemRow.find(`[name="items[${itemIndex}][contract_item_name]"]`).val();
    const typeModel = newItemRow.find(`[name="items[${itemIndex}][type_model]"]`).val();
    const specification = newItemRow.find(`[name="items[${itemIndex}][general_specification]"]`).val();
    const assetDescription = newItemRow.find(`[name="items[${itemIndex}][asset_description]"]`).val();

    newItemRow.find(`[name="items[${itemIndex}][siap_bmd_item_code]"]`).val(itemCode || '');
    newItemRow.find(`[name="items[${itemIndex}][siap_bmd_asset_name]"]`).val(itemName || '');
    newItemRow.find(`[name="items[${itemIndex}][siap_bmd_common_name]"]`).val(commonName || '');
    newItemRow.find(`[name="items[${itemIndex}][siap_bmd_model]"]`).val(typeModel || '');
    newItemRow.find(`[name="items[${itemIndex}][siap_bmd_specification]"]`).val(specification || '');
    newItemRow.find(`[name="items[${itemIndex}][siap_bmd_placement_description]"]`).val(assetDescription || '');

    updateItemNumbers();
    calculateTotals();
}

// Initializes dropdowns for a specific item row
function initializeItemDropdowns(itemIndex) {
    const $row = $(`.item-row[data-index="${itemIndex}"]`);

    initSelect2Ajax($row.find('.item-code'), {
        url: ENDPOINTS.item,
        placeholder: 'Pilih Kode Barang',
        mapItem: function (item) { return { id: item.id, text: item.item_code + ' - ' + item.item_name }; }
    }).on('select2:select', function (e) {
        const selectedItem = e.params.data;
        const name = (selectedItem.text || '').split(' - ').slice(1).join(' - ');
        $row.find(`[name="items[${itemIndex}][item_name]"]`).val(name);
        $row.find(`[name="items[${itemIndex}][contract_item_name]"]`).val(name);
    });

    initSelect2Ajax($row.find('.item-uom'), {
        url: ENDPOINTS.uom,
        placeholder: 'Pilih Satuan',
        mapItem: function (item) { return { id: item.id, text: item.uom_name }; }
    });

    initSelect2Ajax($row.find('.aspak-nama-barang'), {
        url: ENDPOINTS.aspakItemLeaves,
        placeholder: 'Pilih Nama Barang',
        mapItem: function (item) { return { id: item.id, text: item.aspak_tool_name }; }
    });

    initSelect2Ajax($row.find('.aspak-lokasi'), {
        url: ENDPOINTS.aspakRoom,
        placeholder: 'Pilih Lokasi',
        mapItem: function (item) { return { id: item.id, text: item.text }; }
    });
}

// Removes an item row from the form
function removeItemRow() {
    const itemRow = $(this).closest('.item-row');
    itemRow.remove();
    updateItemNumbers();
    calculateTotals();
    if ($('.item-row').length > 1) { $('.item-row:first .remove-item-btn').show(); } else { $('.item-row:first .remove-item-btn').hide(); }
}

// Updates item row numbering and field names
function updateItemNumbers() {
    $('.item-row').each(function (index) {
        $(this).attr('data-index', index);
        $(this).find('h6').html(`<i class="fas fa-box me-2"></i>Barang ${index + 1}`);
        $(this).find('input, select, textarea').each(function () {
            const name = $(this).attr('name');
            if (name && name.includes('items[')) {
                const newName = name.replace(/items\[\d+\]/, `items[${index}]`);
                $(this).attr('name', newName);
            }
        });
    });
}

// Calculates and updates total items and prices
function calculateTotals() {
    const totalItems = $('.item-row').length;
    let totalPrice = 0;
    $('.item-unit-price').each(function () { totalPrice += getCurrencyValue($(this).val()); });
    const formattedPrice = 'Rp ' + totalPrice.toLocaleString('id-ID');
    $('#total_items').val(totalItems);
    $('#total_price').val(formattedPrice);
    $('#aspak_acquisition_price').val(formattedPrice);
    $('#siap_bmd_price').val(formattedPrice);
    updateSiapBmdQuantity();
}

// Handles form submission with validation and AJAX
function handleFormSubmit(e) {
    e.preventDefault();
    clearValidationErrors();

    const submitBtn = $('#submitBtn');
    const originalText = submitBtn.html();
    setFormLoading(true);
    submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Menyimpan...').prop('disabled', true);

    const formData = {};
    $('#formdata :input').each(function () {
        const name = $(this).attr('name');
        const type = $(this).attr('type');
        const value = $(this).val();
        if (!name || $(this).hasClass('calculated-field')) return;
        if (name === 'aspak_acquisition_price' || name === 'siap_bmd_price') return;
        if (name === 'aspak_procurement_year' || name === 'aspak_procurement_source' || name === 'siap_bmd_year') return;
        if (name.includes('[aspak_merk]') || name.includes('[aspak_kondisi_alat]') || name.includes('[aspak_merk_tipe]') || name.includes('[siap_bmd_')) return;

        if (type === 'checkbox') {
            if ($(this).is(':checked')) {
                setNestedValue(formData, name, $(this).val() || true);
            } else if (!formData[name]) {
                setNestedValue(formData, name, false);
            }
        } else if (type === 'radio') {
            if ($(this).is(':checked')) setNestedValue(formData, name, value);
        } else {
            setNestedValue(formData, name, value);
        }
    });

    $('.currency-input').each(function () {
        const name = $(this).attr('name');
        const value = getCurrencyValue($(this).val());
        if (!name || $(this).hasClass('calculated-field')) return;
        if (name === 'aspak_procurement_year' || name === 'aspak_procurement_source' || name === 'siap_bmd_year') return;
        if (name === 'aspak_acquisition_price' || name === 'siap_bmd_price') return;
        if (name.includes('[aspak_merk]') || name.includes('[aspak_kondisi_alat]') || name.includes('[aspak_merk_tipe]') || name.includes('[siap_bmd_')) return;
        setNestedValue(formData, name, value);
    });

    const csrfToken = $('input[name=_token]').val() || $('meta[name="csrf-token"]').attr('content');
    $.ajax({
        url: ENDPOINTS.formSubmit,
        method: 'POST',
        data: JSON.stringify(formData),
        contentType: 'application/json',
        headers: { 'Accept': 'application/json', 'X-CSRF-TOKEN': csrfToken },
        success: function (response) {
            if (response.success) {
                showSuccessMessage(response.message || 'Data berhasil disimpan!');
                setTimeout(function () { window.location.href = response.redirect || '/asset-management/asset-hospital'; }, 1500);
            } else {
                showErrorMessage(response.message || 'Terjadi kesalahan saat menyimpan data.');
            }
        },
        error: function (xhr) {
            if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                displayValidationErrors(xhr.responseJSON.errors);
                showErrorMessage('Validasi gagal. Mohon periksa kolom yang ditandai.');
            } else {
                const message = (xhr.responseJSON && xhr.responseJSON.message) ? xhr.responseJSON.message : 'Terjadi kesalahan saat menyimpan data.';
                showErrorMessage(message);
            }
        },
        complete: function () {
            submitBtn.html(originalText).prop('disabled', false);
            setFormLoading(false);
        }
    });
}

// Clears all validation error states
function clearValidationErrors() {
    $('.is-invalid').removeClass('is-invalid');
    $('[aria-invalid="true"]').removeAttr('aria-invalid');
    $('.invalid-feedback').text('');
    $('.select2-selection').removeClass('is-invalid');
}

// Displays server-side validation errors on form fields
function displayValidationErrors(errors) {
    let firstInvalid = null;
    const errorMessages = [];
    $.each(errors, function (field, messages) {
        const bracketField = convertDotToBracketNotation(field);
        let input = $(`[name="${field}"]`);
        if (input.length === 0) input = $(`[name="${bracketField}"]`);
        if (input.length === 0) {
            const nested = $(`[name="${bracketField.replace(/\]/g, '\\]').replace(/\[/g, '\\[')}"]`);
            if (nested.length) input = nested;
        }
        if (input.length) {
            showFieldError(input, messages[0]);
            if (!firstInvalid) firstInvalid = input;
            errorMessages.push(messages[0]);
        }
    });

    const $errorList = $('#errorMessagesList');
    $errorList.empty();
    if (errorMessages.length > 0) {
        $.each(errorMessages, function (_, message) { $errorList.append(`<li>${message}</li>`); });
        $('#errorMessagesSection').removeClass('d-none');
    } else {
        $('#errorMessagesSection').addClass('d-none');
    }

    if (firstInvalid && firstInvalid.length) {
        $('html, body').animate({ scrollTop: firstInvalid.offset().top - 80 }, 250);
        firstInvalid.trigger('focus');
    }
}

// Shows validation error for a specific field
function showFieldError(field, message) {
    field.addClass('is-invalid');
    field.attr('aria-invalid', 'true');
    field.siblings('.invalid-feedback').text(message);
    if (field.hasClass('select2-hidden-accessible')) {
        field.next('.select2').find('.select2-selection').addClass('is-invalid');
    }
}

// Displays success message in modal
function showSuccessMessage(message) { showAlert('success', message); }
// Displays error message in modal
function showErrorMessage(message) { showAlert('danger', message); }

// Shows alert modal with specified type and message
function showAlert(type, message) {
    let title = 'Notification';
    if (type === 'success') title = 'Success';
    else if (type === 'danger' || type === 'error') title = 'Error';
    else if (type === 'warning') title = 'Warning';

    $('#formFeedbackModalLabel').text(title);
    $('#modalMessageContent').text(message);

    if (type !== 'danger' && type !== 'error') {
        $('#errorMessagesSection').addClass('d-none');
        $('#errorMessagesList').empty();
    }

    $('#formFeedbackModal .modal-content').removeClass('modal-success modal-danger modal-warning');
    if (type === 'success') $('#formFeedbackModal .modal-content').addClass('modal-success');
    else if (type === 'danger' || type === 'error') $('#formFeedbackModal .modal-content').addClass('modal-danger');
    else if (type === 'warning') $('#formFeedbackModal .modal-content').addClass('modal-warning');

    const modalElement = document.getElementById('formFeedbackModal');
    (bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement)).show();
}

// Copies current item row data and adds a new row below it
function copyAndAddItemRow() {
    const currentRow = $(this).closest('.item-row');
    const currentIndex = currentRow.data('index');

    // Add new row at end
    addItemRow();

    // Get the new row
    const newRow = $('.item-row').last();

    // Move new row after current row
    currentRow.after(newRow);

    // Scroll to new row
    newRow[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Apply highlight
    newRow.addClass('new-row-highlight');

    // Remove highlight after 3 seconds
    setTimeout(() => {
        newRow.removeClass('new-row-highlight');
    }, 3000);

    // Copy values from current to new, except serial_number
    currentRow.find('input, select, textarea').each(function() {
        const name = $(this).attr('name');
        if (name && name.includes('serial_number')) return; // skip
        const value = $(this).val();
        const newName = name.replace(/items\[\d+\]/, `items[${newRow.data('index')}]`);
        const newField = newRow.find(`[name="${newName}"]`);
        if (newField.length) {
            if (newField.hasClass('select2-hidden-accessible')) {
                const selectedOption = $(this).find('option:selected');
                if (selectedOption.length) {
                    const text = selectedOption.text();
                    const val = selectedOption.val();
                    newField.append(new Option(text, val, true, true));
                    newField.val(val).trigger('change');
                }
            } else {
                newField.val(value);
            }
        }
    });

    // Copy checkbox states
    currentRow.find('input[type="checkbox"]').each(function() {
        const name = $(this).attr('name');
        if (name) {
            const newName = name.replace(/items\[\d+\]/, `items[${newRow.data('index')}]`);
            const newCheckbox = newRow.find(`[name="${newName}"]`);
            if (newCheckbox.length) {
                newCheckbox.prop('checked', $(this).is(':checked'));
            }
        }
    });

    // Update numbers
    updateItemNumbers();

    // Initialize dropdowns for new row
    initializeItemDropdowns(newRow.data('index'));

    // Calculate totals
    calculateTotals();
}

// Toggles form loading state and disables/enables form elements
function setFormLoading(isLoading) {
    const $form = $('#formdata');
    const $overlay = $('#formLoadingOverlay');
    if (isLoading) {
        $overlay.removeClass('d-none');
        $form.attr('aria-busy', 'true');
        $form.find('input, select, textarea, button').prop('disabled', true);
        $form.find('button[onclick]').prop('disabled', false);
        $form.find('select').each(function () { if ($(this).data('select2')) $(this).prop('disabled', true).trigger('change.select2'); });
    } else {
        $overlay.addClass('d-none');
        $form.removeAttr('aria-busy');
        $form.find('input, select, textarea, button').prop('disabled', false);
        $form.find('select').each(function () { if ($(this).data('select2')) $(this).prop('disabled', false).trigger('change.select2'); });
    }
}
