// Centralized endpoints and small helpers
const ENDPOINTS = {
    distributor: '/dropdown/distributor',
    item: '/dropdown/item',
    uom: '/dropdown/uom',
    aspakItemLeaves: '/master-data/aspak/item/dropdown/leaves',
    aspakRoom: '/master-data/aspak/room/dropdown/room',
    siapActivity: '/master-data/siap-bmd/program/dropdown/activity',
    siapSubActivity: '/master-data/siap-bmd/program/dropdown/sub-activity'
};

// Debounce utility to limit function calls
function debounce(fn, wait) {
    let t;
    return function (...args) {
        clearTimeout(t);
        t = setTimeout(() => fn.apply(this, args), wait);
    };
}

// Initializes Select2 with AJAX for dynamic dropdowns
function initSelect2Ajax($el, {url, placeholder, mapItem, extraData}) {
    return $el.select2({
        placeholder: placeholder || 'Pilih data',
        allowClear: true,
        width: '100%',
        escapeMarkup: function (m) {
            return m;
        },
        ajax: {
            url,
            dataType: 'json',
            delay: 250,
            data: function (params) {
                const base = {q: params.term, page: params.page || 1};
                const extra = typeof extraData === 'function' ? (extraData() || {}) : (extraData || {});
                return Object.assign(base, extra);
            },
            processResults: function (data) {
                return {
                    results: (data.data || []).map(mapItem),
                    pagination: {more: data.current_page < data.last_page}
                };
            }
        }
    });
}

$(document).ready(function () {
    initializePageData();
    initializeForm();
    initializeSubsections();
    initializeSelect2Handlers();
    initializeDatePickers();
    initializeAspakSync();
    initializeSiapBmdSync();
    initializeSiapBmdDataUmumSync();
    initializeAspakDataUmumSync();

    // Events

    // Confirm flow
    // Handles submit button click to show confirmation modal
    $('#submitBtn').on('click', function (e) {
        e.preventDefault();
        const el = document.getElementById('confirmSubmissionModal');
        (bootstrap.Modal.getInstance(el) || new bootstrap.Modal(el)).show();
    });
    // Handles confirmation button to proceed with submission
    $('#confirmSubmitBtn').on('click', function () {
        const el = document.getElementById('confirmSubmissionModal');
        const modal = bootstrap.Modal.getInstance(el);
        if (modal) modal.hide();
        handleFormSubmit($.Event('submit'));
    });

    // Block native submit
    // Prevents default form submission
    $('#formdata').off('submit').on('submit', function (e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    });

    // Clear error list when closing feedback modal
    // Clears error messages on modal close
    $('#formFeedbackModal').on('hidden.bs.modal', function () {
        $('#errorMessagesList').empty();
        $('#errorMessagesSection').addClass('d-none');
    });
});

// Returns today's date in YYYY-MM-DD format
function getTodayDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// Sets up datepickers with restricted input
function initializeDatePickers() {
    const $fields = $('#entry_date, #payment_date');
    if (!$fields.length || typeof $.fn.datepicker !== 'function') return;

    // Ensure not disabled; block manual typing via events (no readonly attr)
    $fields.prop('disabled', false).attr('autocomplete', 'off');

    $fields.datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        orientation: 'bottom'
    }).on('changeDate', function () {
        // Propagate to existing change listeners used for derived fields
        $(this).trigger('change');
    });

    // Block manual input and paste/drop
    $fields.on('keydown', function (e) {
        // allow Tab for navigation
        if (e.key === 'Tab' || e.keyCode === 9) return;
        e.preventDefault();
    });
    $fields.on('paste drop', function (e) {
        e.preventDefault();
    });

    // Open picker on focus/click for smoother UX
    $fields.on('focus click', function () {
        $(this).datepicker('show');
    });
}

// Syncs ASPAK fields based on distributor and date changes
function initializeAspakSync() {
    let aspakDistributorManuallyEdited = false;

    $('#aspak_distributor').on('input', function () {
        aspakDistributorManuallyEdited = true;
    });

    $('#distributor_id').on('select2:select', function (e) {
        if (!aspakDistributorManuallyEdited) {
            $('#aspak_distributor').val(e.params.data.text);
        }
    });
    $('#distributor_id').on('select2:clear', function () {
        if (!aspakDistributorManuallyEdited) {
            $('#aspak_distributor').val('');
        }
    });
    $('#formdata').on('submit', function () {
        aspakDistributorManuallyEdited = false;
    });

    $('#entry_date').on('change', function () {
        const entryDate = $(this).val();
        $('#aspak_procurement_year').val(entryDate ? new Date(entryDate).getFullYear() : '');
    });

    $('#acquisition_source').on('change', function () {
        const selectedText = $(this).find('option:selected').text();
        $('#aspak_procurement_source').val($(this).val() ? selectedText : '');
    });

}

// Initializes ASPAK values from existing form data
function initializeAspakValues() {
    const distributorText = $('#distributor_id option:selected').text();
    if (distributorText && distributorText !== 'Pilih Distributor') {
        $('#aspak_distributor').val(distributorText);
    }
    const entryDate = $('#entry_date').val();
    if (entryDate) $('#aspak_procurement_year').val(new Date(entryDate).getFullYear());
    const acquisitionSource = $('#acquisition_source').val();
    if (acquisitionSource) $('#aspak_procurement_source').val($('#acquisition_source option:selected').text());
}

// Syncs SIAP BMD year from entry date
function initializeSiapBmdSync() {
    $('#entry_date').on('change', function () {
        const entryDate = $(this).val();
        $('#siap_bmd_year').val(entryDate ? new Date(entryDate).getFullYear() : '');
    });
    initializeSiapBmdValues();
}

// Sets SIAP BMD year from entry date
function initializeSiapBmdValues() {
    const entryDate = $('#entry_date').val();
    if (entryDate) $('#siap_bmd_year').val(new Date(entryDate).getFullYear());
}

// Syncs SIAP BMD fields from item data inputs
function initializeSiapBmdDataUmumSync() {
    $(document).on('input', '[name="item_name"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="siap_bmd_asset_name"]`).val($(this).val());
    });
    $(document).on('input', '[name="contract_item_name"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="siap_bmd_common_name"]`).val($(this).val());
    });
    $(document).on('input', '[name="type_model"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="siap_bmd_model"]`).val($(this).val());
    });
    $(document).on('input', '[name="general_specification"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="siap_bmd_specification"]`).val($(this).val());
    });
    $(document).on('input', '[name="asset_description"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="siap_bmd_placement_description"]`).val($(this).val());
    });
}

// Syncs ASPAK fields from item data inputs
function initializeAspakDataUmumSync() {
    $(document).on('input', '[name="brand"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="aspak_merk"]`).val($(this).val());
    });
    $(document).on('change', '[name="condition"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="aspak_kondisi_alat"]`).val($(this).val());
    });
    $(document).on('input', '[name="type_model"]', function () {
        const row = $(this).closest('.item-row');
        row.find(`[name="aspak_merk_tipe"]`).val($(this).val());
    });
    $(document).on('select2:select', '.aspak-nama-barang', function () { /* keep ID; capture text at submit if needed */
    });
    $(document).on('select2:select', '.aspak-lokasi', function () { /* keep ID; capture text at submit if needed */
    });
}

// Handles subsection toggle functionality
function initializeSubsections() {
    $(document).on('click', '.subsection-toggle', function () {
        const $btn = $(this);
        const expanded = $btn.attr('aria-expanded') === 'true';
        const controls = $btn.attr('aria-controls');
        const $content = $('#' + controls);
        const $container = $btn.closest('.subsection');

        if (expanded) {
            $btn.attr('aria-expanded', 'false');
            $container.removeClass('is-open');
            $content.stop(true, true).slideUp(150);
        } else {
            $btn.attr('aria-expanded', 'true');
            $container.addClass('is-open');
            $content.stop(true, true).slideDown(150);
        }
    });
}

// Initializes form elements and dropdowns
function initializeForm() {
    // Static select2
    $('#acquisition_source').select2({
        placeholder: 'Pilih Asal Perolehan', allowClear: true, width: '100%',
        escapeMarkup: function (m) {
            return m;
        },
        templateResult: function (d) {
            return d.text;
        },
        templateSelection: function (d) {
            return d.text;
        }
    });

    // Distributor
    initSelect2Ajax($('#distributor_id'), {
        url: ENDPOINTS.distributor,
        placeholder: 'Pilih Distributor',
        mapItem: function (item) {
            return {id: item.id, text: item.distributor_name};
        }
    });

    // Currency + numeric inputs
    initializeCurrencyInputs();
    initializeNumericOnlyInputs();

    // SIAP BMD dropdowns
    initializeSiapBmdDropdowns();

    // Initialize item dropdowns for the single item row
    initializeItemDropdowns(0);
}

// Restricts input to numeric characters
function initializeNumericOnlyInputs() {
    $(document).on('keypress', '.item-unit-price', function (e) {
        const ch = String.fromCharCode(e.which || e.keyCode);
        if (!/[0-9]/.test(ch)) e.preventDefault();
    });
}

// Extracts and sets global data from form attributes
function initializePageData() {
    const $form = $('#formdata');
    if ($form.length) {
        const hospital = $form.data('hospital-name');
        const prefix = $form.data('rkbmd-prefix');
        if (typeof hospital !== 'undefined') {
            window.hospitalName = hospital;
        }
        if (typeof prefix !== 'undefined') {
            window.rkbmdPrefix = prefix;
        }
    }
}

// Custom handlers for Select2 interactions
function initializeSelect2Handlers() {
    $(document).on('click', '.select2-selection__clear', function (e) {
        e.stopPropagation();
        const $select = $(this).closest('.select2-container').prev('select');
        $select.val(null).trigger('change');
        return false;
    });
    $(document).on('click', '.select2-selection__arrow', function (e) {
        e.stopPropagation();
        const $container = $(this).closest('.select2-container');
        const $select = $container.prev('select');
        if ($container.hasClass('select2-container--open')) {
            $select.select2('close');
        } else {
            $select.select2('open');
        }
        return false;
    });
    $(document).on('select2:select', '.form-select', function () {
        $(this).next('.select2-container').find('.select2-selection__clear').show();
    });
}

// Initializes SIAP BMD activity and sub-activity dropdowns
function initializeSiapBmdDropdowns() {
    initSelect2Ajax($('#siap_bmd_activity'), {
        url: ENDPOINTS.siapActivity,
        placeholder: 'Pilih Kegiatan',
        mapItem: function (item) {
            return {id: item.id, text: item.text};
        }
    });
    initSelect2Ajax($('#siap_bmd_sub_activity'), {
        url: ENDPOINTS.siapSubActivity,
        placeholder: 'Pilih Sub Kegiatan',
        mapItem: function (item) {
            return {id: item.id, text: item.text};
        },
        extraData: function () {
            return {activity_id: $('#siap_bmd_activity').val()};
        }
    }).prop('disabled', true);

    // Enables sub-activity dropdown on activity selection
    $('#siap_bmd_activity').on('select2:select', function () {
        $('#siap_bmd_sub_activity').prop('disabled', false);
    });
    // Disables and clears sub-activity on activity clear
    $('#siap_bmd_activity').on('select2:clear', function () {
        $('#siap_bmd_sub_activity').prop('disabled', true).val(null).trigger('change');
    });
}

// Sets up currency input formatting
function initializeCurrencyInputs() {
    $(document).on('input', '.currency-input', function () {
        formatCurrency(this);
    });
}

// Formats input value as Indonesian Rupiah
function formatCurrency(input) {
    let value = input.value.replace(/[^\d]/g, '');
    if (!value) {
        input.value = '';
        return;
    }
    value = parseInt(value, 10).toLocaleString('id-ID');
    if (input.classList.contains('no-prefix')) {
        input.value = value;
    } else {
        input.value = 'Rp ' + value;
    }
}

// Extracts numeric value from currency string
function getCurrencyValue(currencyString) {
    if (!currencyString) return 0;
    return parseInt(currencyString.replace(/[^\d]/g, '')) || 0;
}

// Converts dot notation to bracket notation for form fields
function convertDotToBracketNotation(fieldName) {
    if (!fieldName.includes('.')) return fieldName;
    const parts = fieldName.split('.');
    let result = parts[0];
    for (let i = 1; i < parts.length; i++) {
        result += `[${parts[i]}]`;
    }
    return result;
}

// Sets value in nested object using path
function setNestedValue(obj, path, value) {
    const keys = path.replace(/\]/g, '').split('[');
    let current = obj;
    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!key) continue;
        if (!(key in current)) {
            const nextKey = keys[i + 1];
            current[key] = isNaN(nextKey) ? {} : [];
        }
        current = current[key];
    }
    const lastKey = keys[keys.length - 1];
    if (lastKey !== '') current[lastKey] = value;
}

// Initializes dropdowns for item rows
function initializeItemDropdowns(itemIndex) {
    const $row = $(`.item-row[data-index="${itemIndex}"]`);

    // Initialize only the remaining dropdowns since item-code is now a text input
    initSelect2Ajax($row.find('.item-uom'), {
        url: ENDPOINTS.uom,
        placeholder: 'Pilih Satuan',
        mapItem: function (item) {
            return {id: item.id, text: item.uom_name};
        }
    });

    initSelect2Ajax($row.find('.aspak-nama-barang'), {
        url: ENDPOINTS.aspakItemLeaves,
        placeholder: 'Pilih Nama Barang',
        mapItem: function (item) {
            return {id: item.id, text: item.aspak_tool_name};
        }
    });

    initSelect2Ajax($row.find('.aspak-lokasi'), {
        url: ENDPOINTS.aspakRoom,
        placeholder: 'Pilih Lokasi',
        mapItem: function (item) {
            return {id: item.id, text: item.text};
        }
    });
}


// Processes form submission with validation and AJAX
function handleFormSubmit(e) {
    e.preventDefault();
    clearValidationErrors();

    // Ensure item_id and item_name are disabled to prevent submission
    $('input[name="item_id"]').prop('disabled', true);
    $('input[name="item_name"]').prop('disabled', true);

    const submitBtn = $('#submitBtn');
    const originalText = submitBtn.html();
    setFormLoading(true);
    submitBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Menyimpan...').prop('disabled', true);

    const formData = {};
    const excludedSiapBmdFields = ['siap_bmd_asset_name', 'siap_bmd_common_name', 'siap_bmd_item_code', 'siap_bmd_model', 'siap_bmd_placement_description', 'siap_bmd_specification'];
    $('#formdata :input').each(function () {
        const name = $(this).attr('name');
        const type = $(this).attr('type');
        const value = $(this).val();
        if (!name || $(this).hasClass('calculated-field')) return;
        if (name === 'aspak_procurement_year' || name === 'aspak_procurement_source' || name === 'siap_bmd_year') return;
        if (name.includes('aspak_merk') || name.includes('aspak_kondisi_alat') || name.includes('aspak_merk_tipe')) return;
        if (excludedSiapBmdFields.includes(name)) return;

        // Handle the items object structure for the single item
        if (!name.includes('items[') && !name.includes('asset_ids[') && !name.includes('asset_entry_id')) {
            // For non-item fields, add directly to formData
            if (type === 'checkbox') {
                if ($(this).is(':checked')) {
                    setNestedValue(formData, name, $(this).val() || true);
                } else if (!formData[name]) {
                    setNestedValue(formData, name, false);
                }
            } else if (type === 'radio') {
                if ($(this).is(':checked')) setNestedValue(formData, name, value);
            } else {
                setNestedValue(formData, name, value);
            }
        }
    });

    // Handle item-specific fields
    const itemData = {};
    $('.item-row').each(function () {
        const $row = $(this);
        $row.find(':input').each(function () {
            const name = $(this).attr('name');
            const type = $(this).attr('type');
            const value = $(this).val();
            if (!name || $(this).hasClass('calculated-field')) return;
            if (name === 'item_id' || name === 'item_name') return;
            if (name.includes('items[') || name.includes('asset_ids[') || name.includes('asset_entry_id')) return;
            if (excludedSiapBmdFields.includes(name)) return;

            // For item fields, add to itemData
            if (type === 'checkbox') {
                if ($(this).is(':checked')) {
                    setNestedValue(itemData, name, $(this).val() || true);
                } else if (!itemData[name]) {
                    setNestedValue(itemData, name, false);
                }
            } else if (type === 'radio') {
                if ($(this).is(':checked')) setNestedValue(itemData, name, value);
            } else {
                setNestedValue(itemData, name, value);
            }
        });

        // Ensure currency inputs in item rows (e.g., unit_price) are numeric
        $row.find('.currency-input').each(function () {
            const name = $(this).attr('name');
            if (!name || $(this).hasClass('calculated-field')) return;
            if (excludedSiapBmdFields.includes(name)) return;
            const value = getCurrencyValue($(this).val());
            setNestedValue(itemData, name, value);
        });
    });

    // Add item data to formData under items key
    if (Object.keys(itemData).length > 0) {
        formData.items = itemData;
    }

    // Convert non-item currency inputs to numeric (skip those inside item rows)
    $('.currency-input').each(function () {
        if ($(this).closest('.item-row').length) return; // handled above
        const name = $(this).attr('name');
        const value = getCurrencyValue($(this).val());
        if (!name || $(this).hasClass('calculated-field')) return;
        if (name === 'aspak_procurement_year' || name === 'aspak_procurement_source' || name === 'siap_bmd_year') return;
        if (name.includes('aspak_merk') || name.includes('aspak_kondisi_alat') || name.includes('aspak_merk_tipe')) return;
        if (excludedSiapBmdFields.includes(name)) return;

        // For non-item fields, add directly to formData
        setNestedValue(formData, name, value);
    });

    // Sends AJAX request to update asset data
    const csrfToken = $('input[name=_token]').val() || $('meta[name="csrf-token"]').attr('content');
    $.ajax({
        url: window.updateUrl,
        method: 'PUT',
        data: JSON.stringify(formData),
        contentType: 'application/json',
        headers: {
            'Accept': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        success: function (response) {
            if (response.success) {
                showSuccessMessage(response.message || 'Data berhasil disimpan!');
                setTimeout(function () {
                    window.location.href = response.redirect || '/asset-management/asset-hospital';
                }, 1500);
            } else {
                showErrorMessage(response.message || 'Terjadi kesalahan saat menyimpan data.');
            }
        },
        error: function (xhr) {
            if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                displayValidationErrors(xhr.responseJSON.errors);
                showErrorMessage('Validasi gagal. Mohon periksa kolom yang ditandai.');
            } else {
                const message = (xhr.responseJSON && xhr.responseJSON.message) ? xhr.responseJSON.message : 'Terjadi kesalahan saat menyimpan data.';
                showErrorMessage(message);
            }
        },
        complete: function () {
            submitBtn.html(originalText).prop('disabled', false);
            setFormLoading(false);
        }
    });
}

// Removes validation error indicators
function clearValidationErrors() {
    $('.is-invalid').removeClass('is-invalid');
    $('[aria-invalid="true"]').removeAttr('aria-invalid');
    $('.invalid-feedback').text('');
    $('.select2-selection').removeClass('is-invalid');
}

// Displays validation errors on form fields
function displayValidationErrors(errors) {
    let firstInvalid = null;
    const errorMessages = [];
    $.each(errors, function (field, messages) {
        const bracketField = convertDotToBracketNotation(field);
        let input = $(`[name="${field}"]`);
        if (input.length === 0) input = $(`[name="${bracketField}"]`);
        if (input.length === 0) {
            // Try to find the field without the items[0] prefix
            const simplifiedField = field.replace(/^items\[\d+\]\./, '');
            input = $(`[name="${simplifiedField}"]`);
        }
        if (input.length === 0) {
            const nested = $(`[name="${bracketField.replace(/\]/g, '\\]').replace(/\[/g, '\\[')}"]`);
            if (nested.length) input = nested;
        }
        if (input.length) {
            showFieldError(input, messages[0]);
            if (!firstInvalid) firstInvalid = input;
            errorMessages.push(messages[0]);
        }
    });

    const $errorList = $('#errorMessagesList');
    $errorList.empty();
    if (errorMessages.length > 0) {
        $.each(errorMessages, function (_, message) {
            $errorList.append(`<li>${message}</li>`);
        });
        $('#errorMessagesSection').removeClass('d-none');
    } else {
        $('#errorMessagesSection').addClass('d-none');
    }

    if (firstInvalid && firstInvalid.length) {
        $('html, body').animate({scrollTop: firstInvalid.offset().top - 80}, 250);
        firstInvalid.trigger('focus');
    }
}

// Marks field as invalid with error message
function showFieldError(field, message) {
    field.addClass('is-invalid');
    field.attr('aria-invalid', 'true');
    field.siblings('.invalid-feedback').text(message);
    if (field.hasClass('select2-hidden-accessible')) {
        field.next('.select2').find('.select2-selection').addClass('is-invalid');
    }
}

// Displays success alert
function showSuccessMessage(message) {
    showAlert('success', message);
}

// Displays error alert
function showErrorMessage(message) {
    showAlert('danger', message);
}

// Shows modal alert with message
function showAlert(type, message) {
    let title = 'Notification';
    if (type === 'success') title = 'Success';
    else if (type === 'danger' || type === 'error') title = 'Error';
    else if (type === 'warning') title = 'Warning';

    $('#formFeedbackModalLabel').text(title);
    $('#modalMessageContent').text(message);

    if (type !== 'danger' && type !== 'error') {
        $('#errorMessagesSection').addClass('d-none');
        $('#errorMessagesList').empty();
    }

    $('#formFeedbackModal .modal-content').removeClass('modal-success modal-danger modal-warning');
    if (type === 'success') $('#formFeedbackModal .modal-content').addClass('modal-success');
    else if (type === 'danger' || type === 'error') $('#formFeedbackModal .modal-content').addClass('modal-danger');
    else if (type === 'warning') $('#formFeedbackModal .modal-content').addClass('modal-warning');

    const modalElement = document.getElementById('formFeedbackModal');
    (bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement)).show();
}

// Toggles loading state for form
function setFormLoading(isLoading) {
    const $form = $('#formdata');
    const $overlay = $('#formLoadingOverlay');
    if (isLoading) {
        $overlay.removeClass('d-none');
        $form.attr('aria-busy', 'true');
        $form.find('input, select, textarea, button').prop('disabled', true);
        $form.find('button[onclick]').prop('disabled', false);
        $form.find('select').each(function () {
            if ($(this).data('select2')) $(this).prop('disabled', true).trigger('change.select2');
        });
    } else {
        $overlay.addClass('d-none');
        $form.removeAttr('aria-busy');
        $form.find('input, select, textarea, button').prop('disabled', false);
        $form.find('select').each(function () {
            if ($(this).data('select2')) $(this).prop('disabled', false).trigger('change.select2');
        });
    }
}
