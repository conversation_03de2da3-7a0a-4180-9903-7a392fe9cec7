// Global variables
let selectedAssets = [];
let assetLabels = {}; // { [id]: { data, qrCodeString, roomCode } }

// Initialize page
$(document).ready(function () {
    // Initialize form and update print button
    initializeForm();
    updatePrintButton();

    // Initialize print mode event handler
    initializePrintModeHandler();

    // Initialize reset form button
    initializeResetButton();
});

// Initialize print mode handler
function initializePrintModeHandler() {
    // Handle print mode change
    $('input[name="print-mode"]').on('change', function() {
        // Re-render all existing assets with new mode
        refreshAllAssetContent();
    });
}

// Initialize form functionality
function initializeForm() {
    // Initialize Select2 for allocation status
    $('#allocation-status').select2({
        placeholder: 'Pilih Status Alokasi',
        allowClear: true,
        width: '100%'
    });

    // Initialize Select2 for room
    $('#room').select2({
        ajax: {
            url: '/dropdown/room',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term || '',
                    page: params.page || 1,
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.room_code + ' - ' + item.room_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: false,
        },
        placeholder: 'Pilih Ruangan',
        minimumInputLength: 0,
        allowClear: true,
        width: '100%'
    });

    // Initialize Select2 for override room
    $('#override-room').select2({
        ajax: {
            url: '/dropdown/room',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term || '',
                    page: params.page || 1,
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: $.map(data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.room_code + ' - ' + item.room_name,
                        };
                    }),
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: false,
        },
        placeholder: 'Pilih Ruangan (Opsional)',
        minimumInputLength: 0,
        allowClear: true,
        width: '100%'
    });

    // Initialize Select2 for asset with auto-add functionality
    $('#asset').select2({
        placeholder: 'Pilih Aset',
        allowClear: true,
        width: '100%',
        disabled: true // Keep asset dropdown disabled by default
    }).on('select2:select', function(e) {
        // Auto-add asset when selected
        const assetId = e.params.data.id;
        const assetText = e.params.data.text;

        if (!assetId) {
            return;
        }

        // Handle "Pilih Semua" option
        if (assetId === 'select_all') {
            handleSelectAllAssets();
            return;
        }

        // Check if asset already added
        if (selectedAssets.includes(assetId)) {
            swal({
                    title: 'Peringatan',
                    text: 'Aset sudah ditambahkan ke list',
                    icon: 'warning',
                    button: 'OK'
                });
            // Reset only asset field
            $('#asset').val(null).trigger('change');
            return;
        }

        // Add to selected assets
        selectedAssets.push(assetId);

        // Add to UI list
        addAssetToList(assetId, assetText);

        // Reset only asset field
        $('#asset').val(null).trigger('change');

        // Update print button
        updatePrintButton();
    });
}

// Handle allocation status change
$('#allocation-status').on('change', function () {
    const status = $(this).val();
    const roomContainer = $('#room-container');
    const allocationContainer = $('#allocation-status-container');
    const assetContainer = $('#asset-container');
    const roomSelect = $('#room');
    const assetSelect = $('#asset');

    // Reset dependent fields
    roomSelect.val(null).trigger('change');
    assetSelect.val(null).trigger('change');
    
    // Disable asset dropdown by default
    assetSelect.prop('disabled', true);

    if (status === 'allocated') {
        roomContainer.show();
        roomSelect.prop('required', true);
        // Reset to normal layout
        allocationContainer.removeClass('col-md-6').addClass('col-md-4');
        assetContainer.removeClass('col-md-6').addClass('col-md-4');
        // Keep asset dropdown disabled until room is selected
        assetSelect.prop('disabled', true);
    } else if (status === 'not_allocated') {
        roomContainer.hide();
        roomSelect.prop('required', false);
        // Adjust layout to make allocation and asset side by side
        allocationContainer.removeClass('col-md-4').addClass('col-md-6');
        assetContainer.removeClass('col-md-4').addClass('col-md-6');
        // Enable asset dropdown for not allocated status
        assetSelect.prop('disabled', false);
        // Load assets for not allocated status
        loadAssets();
    } else {
        roomContainer.hide();
        roomSelect.prop('required', false);
        // Reset to normal layout when no status selected
        allocationContainer.removeClass('col-md-6').addClass('col-md-4');
        assetContainer.removeClass('col-md-6').addClass('col-md-4');
        // Keep asset dropdown disabled when no status selected
        assetSelect.prop('disabled', true);
    }
    
    // Update override room field state
    updateOverrideRoomField();
});

// Handle room change
$('#room').on('change', function () {
    const roomId = $(this).val();
    const assetSelect = $('#asset');
    const allocationStatus = $('#allocation-status').val();
    
    $('#asset').val(null).trigger('change');
    
    // Enable asset dropdown when room is selected for allocated status
    if (allocationStatus === 'allocated' && roomId) {
        assetSelect.prop('disabled', false);
    } else if (allocationStatus === 'not_allocated') {
        assetSelect.prop('disabled', false);
    } else {
        assetSelect.prop('disabled', true);
    }
    
    loadAssets();
    
    // Update override room field state
    updateOverrideRoomField();
});

// Load assets based on allocation status and room
function loadAssets() {
    const allocationStatus = $('#allocation-status').val();
    const roomId = $('#room').val();
    const assetSelect = $('#asset');

    if (!allocationStatus) {
        assetSelect.empty().trigger('change');
        return;
    }

    // Clear current options
    assetSelect.empty().trigger('change');

    // Initialize Select2 with AJAX
    assetSelect.select2({
        ajax: {
            url: '/dropdown/asset',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                let requestData = {
                    q: params.term || '',
                    category_type: 'EQUIPMENT',
                    page: params.page || 1,
                };

                if (allocationStatus === 'allocated' && roomId) {
                    requestData.roomId = roomId;
                } else if (allocationStatus === 'not_allocated') {
                    requestData.menu = 'alocation';
                }

                // Exclude already selected assets
                if (selectedAssets.length > 0) {
                    requestData.selectedItems = selectedAssets
                }

                return requestData;
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                // Add "Pilih Semua" option at the beginning if there are results and it's the first page
                let results = $.map(data.data, function (item) {
                    return {
                        id: item.id,
                        text: item.register_code + ' - ' + item.item_name,
                    };
                });

                // Add "Pilih Semua" option only on first page and if there are results
                if (params.page === 1 && results.length > 0) {
                    results.unshift({
                        id: 'select_all',
                        text: '🔘 Pilih Semua Aset',
                        disabled: false
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data.current_page < data.last_page,
                    },
                };
            },
            cache: false,
        },
        placeholder: 'Pilih Aset',
        minimumInputLength: 0,
        allowClear: true,
        width: '100%',
        disabled: assetSelect.prop('disabled') // Preserve disabled state
    });
}

// Note: Asset auto-add functionality moved to select2:select event handler

// Handle "Pilih Semua" functionality
function handleSelectAllAssets() {
    const allocationStatus = $('#allocation-status').val();
    const roomId = $('#room').val();

    if (!allocationStatus) {
        swal({
            title: 'Peringatan',
            text: 'Silakan pilih status alokasi terlebih dahulu',
            icon: 'warning',
            button: 'OK'
        });
        return;
    }

    // For allocated status, ensure room is selected
    if (allocationStatus === 'allocated' && !roomId) {
        swal({
            title: 'Peringatan',
            text: 'Silakan pilih ruangan terlebih dahulu',
            icon: 'warning',
            button: 'OK'
        });
        return;
    }

    // Show loading state
    const btnAddAsset = $('#btn-add-asset');
    const originalText = btnAddAsset.html();
    btnAddAsset.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Memuat...');

    // Prepare request data
    let requestData = {
        category_type: 'EQUIPMENT',
        per_page: 1000 // Get all assets
    };

    if (allocationStatus === 'allocated' && roomId) {
        requestData.roomId = roomId;
    } else if (allocationStatus === 'not_allocated') {
        requestData.menu = 'alocation';
    }

    // Exclude already selected assets
    if (selectedAssets.length > 0) {
        requestData.selectedItems = selectedAssets;
    }

    // Fetch all assets
    $.ajax({
        url: '/dropdown/asset',
        type: 'GET',
        data: requestData,
        dataType: 'json',
        success: function(response) {
            if (response.data && response.data.length > 0) {
                let addedCount = 0;

                // Add each asset that's not already selected
                response.data.forEach(function(asset) {
                    if (!selectedAssets.includes(asset.id.toString())) {
                        selectedAssets.push(asset.id.toString());
                        const assetText = asset.register_code + ' - ' + asset.item_name;
                        addAssetToList(asset.id, assetText);
                        addedCount++;
                    }
                });

                if (addedCount > 0) {
                    swal({
                    title: 'Berhasil',
                    text: `Berhasil menambahkan ${addedCount} aset ke dalam list`,
                    icon: 'success',
                    button: 'OK'
                });
                } else {
                    swal({
                    title: 'Informasi',
                    text: 'Semua aset sudah ditambahkan ke dalam list',
                    icon: 'info',
                    button: 'OK'
                });
                }

                // Reset only asset field
                $('#asset').val(null).trigger('change');

                // Update print button
                updatePrintButton();
            } else {
                swal({
                title: 'Informasi',
                text: 'Tidak ada aset yang ditemukan sesuai filter',
                icon: 'info',
                button: 'OK'
            });
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching assets:', error);
            swal({
                title: 'Error',
                text: 'Terjadi kesalahan saat mengambil data aset',
                icon: 'error',
                button: 'OK'
            });
        },
        complete: function() {
            // Restore button state
            btnAddAsset.prop('disabled', false).html(originalText);
  }
        });
}

// Reset form
function resetForm() {
    $('#allocation-status').val(null).trigger('change');
    $('#room').val(null).trigger('change');
    $('#override-room').val(null).trigger('change');
    $('#asset').val(null).trigger('change');
    $('#asset').empty();
    // Ensure asset dropdown is disabled after reset
    $('#asset').prop('disabled', true);
    // Ensure override room is disabled after reset
    $('#override-room').prop('disabled', true);
}

// Reset only asset field
function resetAssetField() {
    $('#asset').val(null).trigger('change');
    // Ensure asset dropdown state is consistent with allocation status and room selection
    const allocationStatus = $('#allocation-status').val();
    const roomId = $('#room').val();
    
    if (allocationStatus === 'allocated' && !roomId) {
        $('#asset').prop('disabled', true);
    } else if (allocationStatus === 'not_allocated' || (allocationStatus === 'allocated' && roomId)) {
        $('#asset').prop('disabled', false);
    } else {
        $('#asset').prop('disabled', true);
    }
}

// Enable override room field based on allocation status and room selection
function updateOverrideRoomField() {
    const allocationStatus = $('#allocation-status').val();
    const roomId = $('#room').val();
    
    if (allocationStatus === 'allocated') {
        // For "Sudah Dialokasikan", enable override room only when room is selected
        if (roomId) {
            $('#override-room').prop('disabled', false);
        } else {
            $('#override-room').prop('disabled', true).val(null).trigger('change');
        }
    } else if (allocationStatus === 'not_allocated') {
        // For "Belum Dialokasikan", enable override room immediately
        $('#override-room').prop('disabled', false);
    } else {
        // No allocation status selected, disable override room
        $('#override-room').prop('disabled', true).val(null).trigger('change');
    }
}

// Initialize reset button functionality
function initializeResetButton() {
    $('#btn-reset-form').on('click', function() {
        resetForm();
    });
}

// Add asset to list UI
function addAssetToList(assetId, assetText) {
    // Hide empty state
    $('#empty-state').hide();

    // Load asset label and render it
    loadAssetLabel(assetId);
}

// Remove asset from list
$(document).on('click', '.btn-remove-asset', function () {
    const assetId = $(this).data('asset-id');

    // Remove from selected assets array
    selectedAssets = selectedAssets.filter(id => parseInt(id) !== parseInt(assetId));

    // Remove from UI (both old format and new format)
    $(`.asset-item[data-asset-id="${assetId}"]`).remove();
    $(`.asset-label-item[data-asset-id="${assetId}"]`).remove();

    // Remove from asset labels
    delete assetLabels[assetId];

    // Show empty state if no assets
    if (selectedAssets.length === 0) {
        $('#empty-state').show();
    }

    // Update print button
    updatePrintButton();
});

// Re-render all assets when print mode changes
function refreshAllAssetContent() {
    // Clear all existing content
    $('.asset-grid-item').remove();

    // Re-render all assets with new mode
    selectedAssets.forEach(assetId => {
        if (assetLabels[assetId]) {
            renderAssetContent(assetId);
        }
    });
}

// Get current print mode from radio button
function getCurrentPrintMode() {
    return $('input[name="print-mode"]:checked').val() || 'label';
}

// Helper: format year from date string (YYYY-MM-DD)
function formatYear(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    if (Number.isNaN(d.getTime())) {
        // fallback: take first 4 chars if valid
        return (dateStr || '').toString().slice(0, 4);
    }
    return String(d.getFullYear());
}

// Helper: format number to Indonesian thousands with dot
function formatRupiah(num) {
    if (num === null || num === undefined) return '0';
    const n = Math.round(Number(num) || 0);
    return n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Build label HTML (outer structure matches backend)
function buildLabelHTML(assetId, data) {
    const header = (window.SENSI && window.SENSI.reportHeaderHospital) || '';
    const sourceSupply = data?.asset_entry?.source_supply || '';
    const receivedYear = formatYear(data?.asset_entry?.received_date);
    const registerCode = data?.register_code || '';
    const itemCode = data?.item?.item_code || '';
    const itemName = data?.item?.item_name || '';
    // Use the room code stored with the asset label
    const roomCode = assetLabels[assetId]?.roomCode || '-';
    
    const unitPrice = formatRupiah(data?.unit_price);
    const distributor = data?.asset_entry?.distributor?.distributor_name || '';

    return `
        <div class="" style="min-width: 90mm !important; max-width: 90mm !important; min-height: 50mm !important">
            <div class="w-100 border border-2 border-dark rounded d-flex">
                <div class="w-25 text-center py-2 border-end border-2 border-dark d-flex flex-column align-items-center">
                    <img src="/img/logo/logo-kalbar.png" alt="" width="45">
                    <div class="d-block mt-1 text-center">
                        <h6 class="fs-10px">${sourceSupply} <br> ${receivedYear} </h6>
                    </div>
                    <div class="d-block qr-wrapper mt-auto mb-auto">
                        <div id="qr-small-${assetId}"></div>
                    </div>
                </div>
                <div class="w-75 py-2 text-center">
                     <div class="d-block fw-800 fs-18px">${header}</div>
                     <div class="d-block">11.01.61.00.110202.00001</div>
                     <div class="d-block"><strong>${registerCode}</strong></div>
                     <div class="d-block border-bottom border-top border-3 border-dark fs-16px fw-bolder">${itemCode}</div>
                     <div class="d-block fw-bold">${itemName}</div>
                     <div class="d-block" style="padding-top: 1px; padding-bottom: 1px;">${roomCode}</div>
                     <div class="d-block"><strong>Rp. ${unitPrice}</strong> ${distributor}</div>
                </div>
            </div>
        </div>
    `;
}

// Build QR-only card HTML (outer structure matches backend)
function buildQRCodeHTML(assetId, data) {
    const itemName = data?.item?.item_name || '';
    const qrCodeText = data?.qr_code || '';
    return `
        <div style="display: flex; justify-content: center; width: 100%;">
            <div class="border border-1 border-dark rounded p-2 text-center" style="display: flex; flex-direction: column; height: 100%; align-items: center; width: 100%;">
                <div class="d-block mt-auto mb-auto" style="display: flex; justify-content: center; width: 100%;">
                    <div id="qr-large-${assetId}" style="display: flex; justify-content: center;"></div>
                </div>
                <div class="d-block mt-2" style="text-align: center; width: 100%;">
                    <div class="d-block fw-bold">${itemName}</div>
                    <div class="d-block">${qrCodeText}</div>
                </div>
            </div>
        </div>
    `;
}

// Render asset content based on print mode
function renderAssetContent(assetId) {
    const printMode = getCurrentPrintMode();
    const entry = assetLabels[assetId];
    if (!entry) {
        console.error('Asset data not found for ID:', assetId);
        return;
    }

    // Remove existing content for this asset
    $(`.asset-label-item[data-asset-id="${assetId}"]`).remove();

    const html = printMode === 'qrcode'
        ? buildQRCodeHTML(assetId, entry.data)
        : buildLabelHTML(assetId, entry.data);

    const wrapper = `
        <div class="asset-grid-item asset-label-item" data-asset-id="${assetId}" style="position: relative;">
            <div class="content-wrapper">
                ${html}
            </div>
            <button type="button" class="btn btn-sm btn-danger btn-remove-asset"
                    data-asset-id="${assetId}"
                    style="position: absolute; top: 10px; right: 10px; z-index: 10; opacity: 0.9;">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

    $('#asset-grid-container').append(wrapper);

    // Generate QR codes into the placeholders
    try {
        if (printMode === 'qrcode') {
            const el = document.getElementById(`qr-large-${assetId}`);
            if (el) new QRCode(el, { text: entry.qrCodeString, width: 60, height: 60, colorDark: '#000000', colorLight: '#ffffff' });
        } else {
            const el = document.getElementById(`qr-small-${assetId}`);
            if (el) new QRCode(el, { text: entry.qrCodeString, width: 60, height: 60, colorDark: '#000000', colorLight: '#ffffff' });
        }
    } catch (e) {
        console.error('Failed to render QR:', e);
    }
}

// Load asset label for printing
function loadAssetLabel(assetId) {
    $.ajax({
        url: `/manajemen-aset/data-aset/${assetId}/show`,
        type: 'GET',
        success: function (response) {
            // Determine the room code to use at the time of adding the asset
            let roomCode;
            const overrideRoom = $('#override-room').select2('data');
            
            if (overrideRoom && overrideRoom.length > 0 && overrideRoom[0].id) {
                // Use override room code if available
                roomCode = overrideRoom[0].text.split(' - ')[0]; // Extract room code from "code - name" format
            } else {
                // Use default room code from asset data
                roomCode = (response.data?.room && response.data.room.room_code) ? response.data.room.room_code : '-';
            }
            
            // Store data, qr string, and room code; rendering happens on frontend
            assetLabels[assetId] = {
                data: response.data,
                qrCodeString: response.qrCodeString,
                roomCode: roomCode
            };

            renderAssetContent(assetId);
        },
        error: function (xhr, status, error) {
            console.error('Error loading label:', error);
        }
    });
}

// Update print button state
function updatePrintButton() {
    const printBtn = $('#btn-print');
    const clearAllButton = $('#btn-clear-all');

    if (selectedAssets.length > 0) {
        printBtn.prop('disabled', false);
        clearAllButton.prop('disabled', false);
    } else {
        printBtn.prop('disabled', true);
        clearAllButton.prop('disabled', true);
    }
}

function clearAllAssets() {
    // Konfirmasi sebelum menghapus semua
    if (selectedAssets.length === 0) {
        return;
    }

    swal({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus semua aset dari daftar print label?',
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Batal',
                    value: null,
                    visible: true,
                    className: 'btn btn-secondary',
                    closeModal: true
                },
                confirm: {
                    text: 'Ya, Hapus Semua!',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
            // Kosongkan array dan object
            selectedAssets.length = 0;
            Object.keys(assetLabels).forEach(key => delete assetLabels[key]);

            // Hapus semua item dari DOM
            $('.asset-grid-item').remove();

            // Tampilkan kembali empty state
            $('#empty-state').show();

            // Update status tombol
            updatePrintButton();
        }
    });
}

// Get CSS styles based on print mode
function getPrintStyles(printMode) {
    const commonStyles = `
        @page { size: A4; margin: 10mm; }

        body { margin: 0; padding: 0; background: #fff; }

        /* Custom utility classes used by preview */
        .fs-10px { font-size: 10px; }
        .fs-16px { font-size: 16px; }
        .fs-18px { font-size: 18px; }
        .fw-800  { font-weight: 800; }

        /* Print container */
        #asset-grid-container {
            display: grid;
            width: 100%;
            align-items: start;
            justify-items: stretch;
            box-sizing: border-box;
            gap: 5mm;
            padding: 1mm;
        }

        /* Prevent label breaks */
        .asset-grid-item, .asset-label-item { break-inside: avoid; }

        @media print { body { -webkit-print-color-adjust: exact; print-color-adjust: exact; } }
    `;

    if (printMode === 'qrcode') {
        return commonStyles + `
            #asset-grid-container { grid-template-columns: repeat(4, 1fr); }
            .card-body img {
                width: 1.5cm !important;
                height: 1.5cm !important;
                max-width: 1.5cm !important;
                max-height: 1.5cm !important;
            }
        `;
    }

    // Label mode (default)
    return commonStyles + `#asset-grid-container { grid-template-columns: repeat(2, 1fr); }`;
}

// Handle print button click
$('#btn-print').on('click', function () {
    if (selectedAssets.length === 0) {
        alert('Tidak ada aset yang dipilih untuk dicetak');
        return;
    }

    // Get current print mode
    const printMode = getCurrentPrintMode();

    // Generate fresh print content with properly rendered QR codes
    let printContent = '';
    selectedAssets.forEach(assetId => {
        if (assetLabels[assetId]) {
            const data = assetLabels[assetId].data;
            if (printMode === 'qrcode') {
                printContent += buildQRCodeHTML(assetId, data);
            } else {
                printContent += buildLabelHTML(assetId, data);
            }
        }
    });

    if (!printContent || selectedAssets.length === 0) {
        alert('Tidak ada konten yang tersedia untuk dicetak');
        return;
    }

    // Get appropriate styles based on print mode
    const printStyles = getPrintStyles(printMode);

    // Open print window
    let printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Print ${printMode === 'qrcode' ? 'QR Codes' : 'Labels'}</title>
            <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
            <link href="/css/vendor.min.css" rel="stylesheet" />
            <link href="/css/default/app.min.css" rel="stylesheet" />
            <style>
                ${printStyles}
            </style>
        </head>
        <body>
            <div id="asset-grid-container">
                ${printContent}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();

    // Expose QRCode to the new window context to render into its DOM
    try { printWindow.QRCode = window.QRCode; } catch (e) {}

    // Generate QR codes in the print window after it's loaded
    printWindow.addEventListener('load', function() {
        selectedAssets.forEach(assetId => {
            if (assetLabels[assetId]) {
                const qrCodeString = assetLabels[assetId].qrCodeString;
                try {
                    if (printMode === 'qrcode') {
                        const el = printWindow.document.getElementById(`qr-large-${assetId}`);
                        if (el) new QRCode(el, { text: qrCodeString, width: 60, height: 60, colorDark: '#000000', colorLight: '#ffffff' });
                    } else {
                        const el = printWindow.document.getElementById(`qr-small-${assetId}`);
                        if (el) new QRCode(el, { text: qrCodeString, width: 60, height: 60, colorDark: '#000000', colorLight: '#ffffff' });
                    }
                } catch (e) {
                    console.error('Failed to render QR in print window:', e);
                }
            }
        });

        // Print after a short delay to ensure QR codes are rendered
        setTimeout(function () {
            printWindow.print();
        }, 500);
    });
});

// Handle clear all button click
$('#btn-clear-all').on('click', function () {
    clearAllAssets();
});
