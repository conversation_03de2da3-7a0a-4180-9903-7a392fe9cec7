$(document).ready(function() {
    // Inisialisasi Select2 untuk filter ruangan
    $(".target_ruangan").select2({
        ajax: {
            url: "/dropdown/room",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {q: params.term, page: params.page || 1};
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: $.map(data.data, function (item) {
                        return {id: item.id, text: item.room_code + ' - ' + item.room_name};
                    }),
                    pagination: {more: data.current_page < data.last_page}
                };
            },
            cache: true
        },
        placeholder: '<PERSON><PERSON><PERSON>',
        minimumInputLength: 0,
        allowClear: true,
        width: "320px",
        dropdownParent: $(".target_ruangan").parent()
    }).on("select2:select", function (e) {
        $("#datatable").DataTable().ajax.reload();
    });

    // Inisialisasi DataTable
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/pemeliharaan-aset/isidental/keputusan-list",
            type: 'GET',
            data: function (d) {
                d.room_id = $('.target_ruangan').val();
                d.filter_date_start = $("#filter_date_start").val();
                d.filter_date_end = $("#filter_date_end").val();
            }
        },
        columns: [
            { data: "request_code", name: "request_code" },
            {
                name: "request_date", 
                data: "request_date", 
                render: function (v, t, r) {
                    return `${v}<br><span class="text-danger">${r.room_name}</span>`;
                }
            },
            {
                name: "asset_name", 
                data: "asset_name", 
                render: function (v, t, r) {
                    return `${v}<br><span class="text-danger">${r.qr_code} / ${r.serial_number}</span>`;
                }
            },
            { data: "request_issue", name: "request_issue" },
            { data: "latest_status", name: "latest_status" },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    // Extract detail route
                    var detailRouteMatch = data.match(/data-route="([^"]+)"/);
                    var detailRoute = detailRouteMatch ? detailRouteMatch[1] : '';
                    
                    // Extract onclick functions
                    var acceptRequestMatch = data.match(/onclick="updateForm\(`accept_request`,([^)]+)\)"/);
                    var assignRequestMatch = data.match(/onclick="updateForm\(`assign_request`,([^)]+)\)"/);
                    var addBhpMatch = data.match(/onclick="updateForm\(`add_bhp`,([^)]+)\)"/);
                    var callVendorMatch = data.match(/onclick="updateForm\(`call_vendor`,([^)]+)\)"/);
                    var finalDecisionMatch = data.match(/onclick="updateForm\(`final_decision`,([^)]+)\)"/);
                    
                    var acceptRequestId = acceptRequestMatch ? acceptRequestMatch[1] : '';
                    var assignRequestId = assignRequestMatch ? assignRequestMatch[1] : '';
                    var addBhpId = addBhpMatch ? addBhpMatch[1] : '';
                    var callVendorId = callVendorMatch ? callVendorMatch[1] : '';
                    var finalDecisionId = finalDecisionMatch ? finalDecisionMatch[1] : '';
                    
                    return `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu datatable-dropdown-menu">
                                    <li><a class="dropdown-item datatable-dropdown-item btn-detail" href="javascript:;" data-route="${detailRoute}"><i class="fas fa-search me-2"></i>Detail Request</a></li>
                                    ${acceptRequestId ? `<li><a class="dropdown-item datatable-dropdown-item" href="javascript:;" onclick="updateForm('accept_request',${acceptRequestId})"><i class="fas fa-check me-2"></i>Terima Request</a></li>` : ''}
                                    ${assignRequestId ? `<li><a class="dropdown-item datatable-dropdown-item" href="javascript:;" onclick="updateForm('assign_request',${assignRequestId})"><i class="fas fa-users me-2"></i>Penugasan</a></li>` : ''}
                                    ${addBhpId ? `<li><a class="dropdown-item datatable-dropdown-item" href="javascript:;" onclick="updateForm('add_bhp',${addBhpId})"><i class="fas fa-boxes me-2"></i>Penyediaan BHP</a></li>` : ''}
                                    ${callVendorId ? `<li><a class="dropdown-item datatable-dropdown-item" href="javascript:;" onclick="updateForm('call_vendor',${callVendorId})"><i class="fas fa-phone me-2"></i>Pemanggilan Vendor</a></li>` : ''}
                                    <li><hr class="dropdown-divider"></li>
                                    ${finalDecisionId ? `<li><a class="dropdown-item datatable-dropdown-item" href="javascript:;" onclick="updateForm('final_decision',${finalDecisionId})"><i class="fas fa-gavel me-2"></i>Keputusan Akhir</a></li>` : ''}
                                </ul>
                            </div>
                        `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[1, 'desc']],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Apply filter button
    $("#applyFilter").on("click", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Reset filter button
    $("#resetFilter").on("click", function () {
        $("#filter_date_start").val("");
        $("#filter_date_end").val("");
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });
});

// Event handler untuk detail
$("#datatable").on("click", ".btn-detail", function () {
    let route = $(this).data("route");
    update = true;
    changeActionForm(route);
    $('#activityBody').html("Loading...");
    $("#modal-dialog").modal("show");
    
    $.ajax({
        url: route,
        type: "GET",
        dataType: 'html',
        success: function (response) {
            $('#activityBody').html(response);
        },
        error: function (xhr) {
            $('#activityBody').html('<div class="alert alert-danger">Error loading data</div>');
            errorMessage(xhr.responseJSON?.message || 'Error loading data');
        },
    });
});
