let idUpdateIncidental = 0;
let typeUpdate = "";

function updateForm(type, id) {
    idUpdateIncidental = id;
    typeUpdate = type;

    if (type === "inspection_request") {
        inspectionRequest(type, id);
    }

    if (type === "vendor_result") {
        $("#modal-vendor-result").modal("show");
        $("#save-vendor-result").prop("disabled", false);
        $('#vendor_result_notice').val("");
        $('#final_vendor_result_select').val("VENDOR_FIXED");
    }

    if (type === "request_bhp") {
        $("#modal-request-bhp").modal("show");
        $("#save-request-bhp").prop("disabled", false);
        $('#request_bhp_notice').val("");
    }

    if (type === "inspection_result") {
        $("#inspection_result_select").val("ASSIGN_FIXED").trigger("change");
        $('.btn-remove-inspection-result').click();
        $("#modal-inspection-result").modal("show");
        $("#btn-add-inspection-result").click();
        $('#inspection_result_notice').val("");
        $("#save-inspection-result").prop("disabled", false);
    }
}

$('#inspection_result_select').change(function () {
    if ($(this).val() === "ASSIGN_FIXED") {
        $(".show-bhp").show();
    } else {
        $(".show-bhp").hide();
    }
});

/* inspection_result */
$("#save-inspection-result").on("click", function () {
    const data = [];
    $("#table-inspection-result tr").each(function () {
        const name = $(this).find(".inspection-result-name").val();
        const price = $(this).find(".inspection-result-price").val();
        const qty = $(this).find(".inspection-result-qty").val();
        if (name || price || qty) {
            if (name) {
                data.push({
                    name: name || '',
                    price: parseInt(price.replace(/,/g, '')) || 0,
                    qty: parseInt(qty.replace(/,/g, '')) || 0,
                });
            }
        }
    });
    $("#save-inspection-result").prop("disabled", true);
    $.ajax({
        url: `kegiatan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            inspection_result_notice: $('#inspection_result_notice').val(),
            inspection_result_select: $('#inspection_result_select').val(),
            data_inspection_result: data,
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-inspection-result").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-inspection-result").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});

$("#table-inspection-result").on("click", ".btn-remove-inspection-result", function () {
    $(this).closest("tr").remove();
});

$("#btn-add-inspection-result").on("click", function () {
    let newRow = `
            <tr>
                <td><input type="text" name="name[]" class="form-control inspection-result-name" value="" placeholder="Nama"></td>
                <td><input type="text" name="price[]" class="form-control inspection-result-price" value="0" placeholder="Harga"></td>
                <td><input type="text" name="qty[]" class="form-control inspection-result-qty" value="0" placeholder="Kuantitas"></td>
                <td class="text-center"><button type="button" class="btn btn-danger btn-sm btn-remove-inspection-result"><i class="fa fa-times"></i></button></td>
            </tr>
        `;
    let $newRow = $(newRow);
    $("#table-inspection-result").append($newRow);

    $newRow.find('.inspection-result-price, .inspection-result-qty').each(function () {
        new AutoNumeric(this, {digitGroupSeparator: ',', decimalPlaces: 0});
    });
});
/* END of ADD In */


/* Request BHP Result */
$("#save-request-bhp").on("click", function () {
    $("#save-request-bhp").prop("disabled", true);
    $.ajax({
        url: `kegiatan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            request_bhp_notice: $('#request_bhp_notice').val(),
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-request-bhp").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-request-bhp").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});
/* End of Request inspection_result */

/* Vendor Result */
$("#save-vendor-result").on("click", function () {
    $("#save-vendor-result").prop("disabled", true);
    $.ajax({
        url: `kegiatan/detail/${idUpdateIncidental}`,
        type: "PUT",
        data: {
            _method: "PUT",
            type_update: typeUpdate,
            vendor_result_notice: $('#vendor_result_notice').val(),
            final_vendor_result_select: $('#final_vendor_result_select').val(),
        },
        success: function (response) {
            successMessage(response.message);
            $("#modal-vendor-result").modal("hide");
            $("#datatable").DataTable().ajax.reload();
        },
        error: function (xhr) {
            $("#save-vendor-result").prop("disabled", false);
            errorMessage(xhr?.responseJSON?.message || "Server error")
        }
    });
});
/* End of Vendor Result */

/* ACCEPT FUNCTION */
function inspectionRequest(type, id) {
    swal({
        title: 'Konfirmasi Request dan Lakukan Pengecekan?',
        text: 'Anda yakin akan melakukan konfirmasi terhadap proses request, dan akan melakukan pengecekan',
        buttons: {
            cancel: {text: 'Batal', visible: true, className: 'btn btn-default'},
            confirm: {text: 'Konfirmasi', className: 'btn btn-primary'}
        }
    }).then((isConfirm) => {
        if (isConfirm) {
            $.ajax({
                url: `kegiatan/detail/${id}`,
                type: "PUT",
                data: {
                    _method: "PUT",
                    type_update: type
                },
                success: function (response) {
                    successMessage(response.message);
                    $("#datatable").DataTable().ajax.reload();
                },
                error: function (xhr) {
                    errorMessage(xhr?.responseJSON?.message || "Server error")
                }
            })
        }
    });
}

/* END ACCEPT FUNCTION */
