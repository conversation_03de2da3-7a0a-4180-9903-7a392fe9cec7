$(document).ready(function () {
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "/pemeliharaan-aset/alkes/jadwal-list",
            data: function (d) {
                d.filter_kode_barang = $("#filter_kode_barang").val();
                d.filter_tanggal_mulai = $("#filter_tanggal_mulai").val();
                d.filter_tanggal_akhir = $("#filter_tanggal_akhir").val();
            },
        },
        order: [[1, 'desc']],
        columns: [
            {data: "DT_RowIndex", name: "DT_RowIndex", orderable: false, searchable: false},
            {
                name: "schedule_code", data: "schedule_code", render: function (v, t, r) {
                    return `<span class="text-primary font-weight-bold">${v}</span>`;
                }
            },
            {
                name: "schedule_date", data: "schedule_date", render: function (v, t, r) {
                    return `<span class="text-success">${v}</span>`;
                }
            },
            {
                name: "schedule_type", data: "schedule_type", render: function (v, t, r) {
                    let badgeClass = 'bg-primary';
                    if (v === 'KALIBRASI') badgeClass = 'bg-warning';
                    else if (v === 'MAINTENANCE') badgeClass = 'bg-info';
                    else if (v === 'UJI_FUNGSI') badgeClass = 'bg-success';
                    else if (v === 'UJI_KESESUAIAN') badgeClass = 'bg-secondary';
                    
                    return `<span class="badge ${badgeClass}">${v}</span>`;
                }
            },
            {data: "item_name", name: "item_name"},
            {data: "schedule_quantity", name: "schedule_quantity"},
            {data: "schedule_notes", name: "schedule_notes"},
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    // Extract routes from the original action HTML
                    var detailMatch = data.match(/data-route="([^"]+)"/);
                    var deleteMatch = data.match(/data-route="([^"]+)"/g);
                    var detailRoute = detailMatch ? detailMatch[0].replace('data-route="', '').replace('"', '') : '';
                    var deleteRoute = '';
                    
                    if (deleteMatch && deleteMatch.length > 1) {
                        deleteRoute = deleteMatch[1].replace('data-route="', '').replace('"', '');
                    } else if (deleteMatch && deleteMatch.length === 1) {
                        deleteRoute = deleteMatch[0].replace('data-route="', '').replace('"', '');
                    }
                    
                    return `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu datatable-dropdown-menu">
                                <li><a class="dropdown-item datatable-dropdown-item btn-detail" href="#modal-dialog" data-bs-toggle="modal" data-route="${detailRoute}">
                                    <i class="fas fa-search me-2"></i>Lihat Detail
                                </a></li>
                                <li><a class="dropdown-item datatable-dropdown-item btn-delete" href="javascript:;" data-route="${deleteRoute}">
                                    <i class="fas fa-trash me-2"></i>Hapus Data
                                </a></li>
                            </ul>
                        </div>
                    `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...'
        }
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
        
        // Initialize Select2 for filter kode barang when modal is shown
        setTimeout(function() {
            // Destroy existing select2 instances first
            if ($(".filter-kode-barang-select2").hasClass("select2-hidden-accessible")) {
                $(".filter-kode-barang-select2").select2('destroy');
            }

            // Initialize Select2 for filter kode barang
            $(".filter-kode-barang-select2").select2({
                placeholder: "Pilih Kode Barang",
                allowClear: true,
                minimalInputLength: 0,
                dropdownParent: $("#filterDrawer"),
                ajax: {
                    url: "/dropdown/item",
                    data: function (params) {
                        return {
                            category_type: "EQUIPMENT",
                            category_sub_type: "ALAT_KESEHATAN",
                            q: params.term,
                            page: params.page || 1,
                        };
                    },
                    dataType: "json",
                    delay: 250,
                    processResults: function (data, params) {
                        params.page = params.page || 1;

                        return {
                            results: $.map(data.data, function(item) {
                                return {
                                    id: item.id,
                                    text: item.item_code + " - " + item.item_name,
                                    item_code: item.item_code
                                };
                            }),
                            pagination: {
                                more: data.current_page < data.last_page
                            }
                        };
                    },
                }
            });
        }, 200);
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    // Clean up select2 when modal is hidden
    $("#filterDrawer").on("hidden.bs.modal", function () {
        if ($(".filter-kode-barang-select2").hasClass("select2-hidden-accessible")) {
            $(".filter-kode-barang-select2").select2('destroy');
        }
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Apply filter button
    $("#applyFilter").on("click", function () {
        // Get values from select2
        var kodeBarangValue = $(".filter-kode-barang-select2").val();
        
        // Set values to original select elements
        $("#filter_kode_barang").val(kodeBarangValue);
        
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Reset filter button
    $("#resetFilter").on("click", function () {
        // Reset select2 values
        if ($(".filter-kode-barang-select2").hasClass("select2-hidden-accessible")) {
            $(".filter-kode-barang-select2").val("").trigger("change");
        }
        
        // Reset original select values
        $("#filter_kode_barang").val("");
        $("#filter_tanggal_mulai").val("");
        $("#filter_tanggal_akhir").val("");
        
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    $("#datatable").on("click", ".btn-detail", function () {
        let route = $(this).data("route");
        update = true;
        changeActionForm(route);
        $('#activityBody').html("Loading...");
        $.ajax({
            url: route,
            type: "GET",
            dataType: 'html',
            success: function (response) {
                $('#activityBody').html(response);
            },
            error: function (xhr) {
                errorMessage(xhr.responseJSON.message);
            },
        })
    });

    $("#datatable").on("click", ".btn-delete", function() {
        let route = $(this).data("route");

        swal({
            title: "Konfirmasi",
            text: "Anda yakin akan menghapus data ini?",
            icon: "warning",
            buttons: {
                cancel: {
                    text: 'Batal',
                    value: null,
                    visible: true,
                    className: 'btn btn-default',
                    closeModal: true,
                },
                confirm: {
                    text: 'Ya',
                    value: true,
                    visible: true,
                    className: 'btn btn-primary',
                    closeModal: true
                }
            }
        }).then((isConfirm) => {
            if (isConfirm) {
                $.ajax({
                    url: route,
                    type: "DELETE",
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        $("#datatable").DataTable().ajax.reload();
                        successMessage(response.message);
                    },
                    error: function (xhr) {
                        errorMessage(xhr.responseJSON.message);
                    },
                });
            }
        });
    });
});
