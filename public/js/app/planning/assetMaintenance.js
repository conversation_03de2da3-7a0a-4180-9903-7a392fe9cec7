$(document).ready(function() {
    // Inisialisasi Select2 untuk filter ruangan
    $(".target_ruangan").select2({
        ajax: {
            url: "/dropdown/room-with-access-with-trigger",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                
                // Jika auto_select true, set nilai default
                if (data.auto_select && data.default_room) {
                    let defaultRoom = data.default_room;
                    let option = new Option(
                        defaultRoom.room_code + ' - ' + defaultRoom.room_name, 
                        defaultRoom.id, 
                        true, 
                        true
                    );
                    $(".target_ruangan")
                        .append(option)
                        .trigger('change')
                        .trigger('select2:select');
                }

                return {
                    results: $.map(data.data.data, function (item) {
                        return {
                            id: item.id,
                            text: item.room_code + ' - ' + item.room_name
                        };
                    }),
                    pagination: {
                        more: data.data.current_page < data.data.last_page
                    }
                };
            },
            cache: true
        },
        placeholder: '<PERSON><PERSON><PERSON>',
        minimumInputLength: 0,
        allowClear: true,
        width: "320px",
        dropdownParent: $(".target_ruangan").parent()
    }).on("select2:select", function (e) {
        $("#datatable").DataTable().ajax.reload();
    });

    // Inisialisasi DataTable
    $("#datatable").DataTable({
        processing: true,
        serverSide: true,
        bDestroy: true,
        searching: false,
        ordering: true,
        paging: true,
        pageLength: 10,
        lengthChange: false,
        info: true,
        autoWidth: false,
        responsive: true,
        ajax: {
            url: "/perencanaan/pemeliharaan-barang-list",
            type: 'GET',
            data: function (d) {
                d.room = $('.target_ruangan').val();
                d.status = $("#status").val();
                d.year = $("#year").val();
            }
        },
        columns: [
            { data: "judul", name: "maintenance_plan_title" },
            { data: "ruangan", name: "room.room_name" },
            { data: "created_at", name: "created_at" },
            { data: "tahun", name: "maintenance_plan_date" },
            { data: "total_qty", name: "total_qty" },
            { data: "status", name: "status" },
            {
                data: "action",
                name: "action",
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    // Extract detail route
                    var detailRouteMatch = data.match(/btn-show[^>]*data-route="([^"]+)"/);
                    var detailRoute = detailRouteMatch ? detailRouteMatch[1] : '';
                    
                    // Extract delete route
                    var deleteRouteMatch = data.match(/btn-delete[^>]*data-route="([^"]+)"/);
                    var deleteRoute = deleteRouteMatch ? deleteRouteMatch[1] : '';
                    
                    // Extract ID for export
                    var idMatch = data.match(/data-id="([^"]+)"/);
                    var id = idMatch ? idMatch[1] : '';
                    
                    return `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary-modern btn-modern-rounded dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu datatable-dropdown-menu">
                                    <li><a class="dropdown-item datatable-dropdown-item btn-show" href="javascript:;" data-route="${detailRoute}" data-id="${id}"><i class="fas fa-search me-2"></i>Detail</a></li>
                                    ${deleteRoute ? `<li><a class="dropdown-item datatable-dropdown-item btn-delete" href="javascript:;" data-route="${deleteRoute}"><i class="fas fa-trash me-2"></i>Hapus</a></li>` : ''}
                                </ul>
                            </div>
                        `;
                }
            },
        ],
        dom: 'rt<"d-flex justify-content-between align-items-center"ip>',
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[2, 'desc']],
        language: {
            processing: '<div class="spinner-border spinner-border-sm" role="status"></div> Loading...',
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ data",
            info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
            infoFiltered: "(disaring dari _MAX_ data keseluruhan)",
            infoPostFix: "",
            loadingRecords: "Memuat...",
            zeroRecords: "Tidak ditemukan data yang sesuai",
            emptyTable: "Tidak ada data yang tersedia",
            paginate: {
                first: "Pertama",
                previous: "Sebelumnya",
                next: "Selanjutnya",
                last: "Terakhir"
            },
            aria: {
                sortAscending: ": aktifkan untuk mengurutkan kolom ke atas",
                sortDescending: ": aktifkan untuk mengurutkan kolom ke bawah"
            }
        },
        drawCallback: function(settings) {
            if ($("#datatable").DataTable().data().count() === 0) {
                $("#datatable_paginate").hide();
            } else {
                $("#datatable_paginate").show();
            }
        }
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Apply filter button
    $("#applyFilter").on("click", function () {
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Reset filter button
    $("#resetFilter").on("click", function () {
        $("#status").val("all").trigger('change');
        $("#year").val(new Date().getFullYear()).trigger('change');
        $("#datatable").DataTable().ajax.reload();
        $("#filterDrawer").modal("hide");
    });

    // Custom rows per page functionality
    $("#rowsPerPage").on("change", function () {
        $("#datatable").DataTable().page.len($(this).val()).draw();
    });

    // Update rows per page dropdown when table is drawn
    $("#datatable").on("draw.dt", function () {
        var table = $("#datatable").DataTable();
        var pageLength = table.page.len();
        $("#rowsPerPage").val(pageLength);
    });

    // Trigger initial load if needed
    $.ajax({
        url: "/dropdown/room-with-access-with-trigger",
        type: 'GET',
        success: function(data) {
            if (data.auto_select && data.default_room) {
                let defaultRoom = data.default_room;
                let option = new Option(
                    defaultRoom.room_code + ' - ' + defaultRoom.room_name, 
                    defaultRoom.id, 
                    true, 
                    true
                );
                $(".target_ruangan")
                    .append(option)
                    .trigger('change')
                    .trigger('select2:select');
            }
        }
    });
});

// Event handlers untuk detail, delete, dan export
$("#datatable").on("click", ".btn-show", function () {
    let route = $(this).data("route");
    let id = $(this).data("id");
    
    // Tampilkan loading state
    $('#target-body').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
    $('#modal-dialog').modal('show');

    // Set ID untuk tombol export
    $('#btn-export').data('id', id);

    $.ajax({
        url: route,
        type: "GET",
        dataType: 'html',
        success: function (response) {
            $('#target-body').html(response);
        },
        error: function() {
            $('#target-body').html('<div class="alert alert-danger text-center">Terjadi kesalahan saat memuat data</div>');
        }
    });
});

$("#datatable").on("click", ".btn-delete", function() {
    let url = $(this).data("route");

    swal({
        title: "Apakah Anda yakin?",
        text: "Data pemeliharaan akan dihapus secara permanen!",
        icon: "warning",
        buttons: {
            cancel: {
                text: "Batal",
                value: null,
                visible: true,
                className: "btn btn-default",
                closeModal: true,
            },
            confirm: {
                text: "Ya, Hapus!",
                value: true,
                visible: true,
                className: "btn btn-danger",
                closeModal: true
            }
        }
    }).then((result) => {
        if (result) {
            $.ajax({
                url: url,
                type: "DELETE",
                data: {
                    "_token": $('meta[name="csrf-token"]').attr("content")
                },
                success: function(response) {
                    if (response.status === "success") {
                        swal("Berhasil!", response.message, "success");
                        $("#datatable").DataTable().ajax.reload();
                    } else {
                        swal("Error!", response.message, "error");
                    }
                },
                error: function(xhr) {
                    swal("Error!", "Terjadi kesalahan saat menghapus data", "error");
                }
            });
        }
    });
});

// Event handler untuk tombol export
$(document).on('click', '#btn-export', function() {
    let id = $(this).data('id');
    window.location.href = `/perencanaan/pemeliharaan-barang/${id}/export`;
});
