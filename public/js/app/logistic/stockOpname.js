$(document).ready(function () {
    // Inisialisasi Select2
    $(".stock_recap").select2({
        placeholder: '<PERSON><PERSON><PERSON>',
        allowClear: true,
        width: '100%'
    });

    $(".total_keluar").select2({
        placeholder: 'Pilih Total Keluar',
        allowClear: true,
        width: '100%'
    });

    $('#datepicker').daterangepicker({
        locale: {format: 'LL'},
        startDate: moment().startOf("day"),
        endDate: moment().endOf("day"),
        ranges: {
            'Hari Ini': [moment().startOf('day'), moment().endOf('day')],
            '<PERSON><PERSON><PERSON>': [moment().subtract(6, 'days'), moment()],
            'Bulan <PERSON>': [moment().startOf('month'), moment().endOf('month')],
            '1 Bulan Lalu': [moment().subtract(1, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
            '2 Bulan Lalu': [moment().subtract(2, 'months').startOf('month'), moment().subtract(2, 'months').endOf('month')],
            '3 Bulan Lalu': [moment().subtract(3, 'months').startOf('month'), moment().subtract(3, 'months').endOf('month')],
            '3 Bulan Berjalan': [moment().subtract(3, 'months').startOf('month'), moment()],
        }
    });

    // Load data default saat halaman dimuat
    loadDefaultReport();

    // Custom search functionality
    $("#searchInput").on("keyup", function () {
        // Implementasi search untuk konten yang ada di target-report
        var searchTerm = $(this).val().toLowerCase();
        $("#target-report table tbody tr").each(function() {
            var rowText = $(this).text().toLowerCase();
            if (rowText.indexOf(searchTerm) > -1) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Filter drawer functionality
    $(".datatable-filter-icon").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("show");
        
        // Initialize Select2 for filter when modal is shown
        setTimeout(function() {
            // Destroy existing select2 instances first
            if ($(".stock_recap").hasClass("select2-hidden-accessible")) {
                $(".stock_recap").select2('destroy');
            }
            if ($(".total_keluar").hasClass("select2-hidden-accessible")) {
                $(".total_keluar").select2('destroy');
            }

            // Initialize Select2 for stock recap
            $(".stock_recap").select2({
                placeholder: "Pilih Jenis Barang",
                allowClear: true,
                width: '100%',
                dropdownParent: $("#filterDrawer")
            });

            // Initialize Select2 for total keluar
            $(".total_keluar").select2({
                placeholder: "Pilih Total Keluar",
                allowClear: true,
                width: '100%',
                dropdownParent: $("#filterDrawer")
            });
        }, 200);
    });

    // Close filter drawer
    $("#closeFilterDrawer").on("click", function (e) {
        e.preventDefault();
        $("#filterDrawer").modal("hide");
    });

    // Clean up select2 when modal is hidden
    $("#filterDrawer").on("hidden.bs.modal", function () {
        if ($(".stock_recap").hasClass("select2-hidden-accessible")) {
            $(".stock_recap").select2('destroy');
        }
        if ($(".total_keluar").hasClass("select2-hidden-accessible")) {
            $(".total_keluar").select2('destroy');
        }
    });

    $("#filterDrawer").on("click", function (e) {
        if (e.target === this) {
            $("#filterDrawer").modal("hide");
        }
    });

    // Apply filter button
    $("#applyFilter").on("click", function () {
        loadReport();
        $("#filterDrawer").modal("hide");
    });

    // Reset filter button
    $("#resetFilter").on("click", function () {
        // Reset datepicker
        $('#datepicker').data('daterangepicker').setStartDate(moment().startOf("day"));
        $('#datepicker').data('daterangepicker').setEndDate(moment().endOf("day"));
        
        // Reset select2 values
        if ($(".stock_recap").hasClass("select2-hidden-accessible")) {
            $(".stock_recap").val("ALL").trigger("change");
        }
        if ($(".total_keluar").hasClass("select2-hidden-accessible")) {
            $(".total_keluar").val("ALL").trigger("change");
        }
        
        // Load default report
        loadDefaultReport();
        
        $("#filterDrawer").modal("hide");
    });

    // Export Excel functionality
    $(".btn-export-excel").on("click", function() {
        let periode = $("#datepicker").val();
        let stockRecapId = $("#stock_recap").val();
        let totalKeluar = $("#total_keluar").val();
        
        if (periode) {
            window.location.href = `/logistik/laporan/stock-opname/export-excel?periode=${periode}&stock_recap_id=${stockRecapId == "ALL" ? "" : stockRecapId}&total_keluar=${totalKeluar == "ALL" ? "" : totalKeluar}`;
        } else {
            alert("Silakan pilih periode terlebih dahulu.");
        }
    });

    // Export PDF functionality
    $(".btn-export-pdf").on("click", function() {
        let periode = $("#datepicker").val();
        let stockRecapId = $("#stock_recap").val();
        let totalKeluar = $("#total_keluar").val();
        
        if (periode) {
            window.location.href = `/logistik/laporan/stock-opname/export-pdf?periode=${periode}&stock_recap_id=${stockRecapId == "ALL" ? "" : stockRecapId}&total_keluar=${totalKeluar == "ALL" ? "" : totalKeluar}`;
        } else {
            alert("Silakan pilih periode terlebih dahulu.");
        }
    });

    // Tambahkan event listener untuk perubahan select2 (untuk kompatibilitas)
    $(".stock_recap").on('change', function() {
        // Kosongkan target-report jika diperlukan
        // $("#target-report").empty();
    });

    // Legacy btn-filter functionality (untuk kompatibilitas)
    $("#btn-filter").on("click", function () {
        loadReport();
    });

    // Function to update report header
    function updateReportHeader(periode) {
        // Update periode text
        $("#periodText").text(periode);
        
        // Update timestamp
        var now = moment();
        var timestamp = now.format('YYYY-MM-DD') + ' : ' + now.format('HH:mm:ss');
        $("#timestampText").text(timestamp);
    }

    // Function to load default report
    function loadDefaultReport() {
        let periode = $("#datepicker").val();
        let stockRecapId = "ALL";
        let totalKeluar = "ALL";

        // Update header
        updateReportHeader(periode);

        $.ajax({
            type: "get",
            dataType: "html",
            async: false,
            data: {
                type: "report",
                periode: periode,
                stock_recap_id: (stockRecapId == "ALL") ? null : stockRecapId,
                total_keluar: (totalKeluar == "ALL") ? null : totalKeluar
            },
            success: function (response) {
                $("#target-report").html(response);
            },
        });
    }

    // Function to load report with current filter values
    function loadReport() {
        let periode = $("#datepicker").val();
        let stockRecapId = $("#stock_recap").val();
        let totalKeluar = $("#total_keluar").val();

        // Update header
        updateReportHeader(periode);

        $.ajax({
            type: "get",
            dataType: "html",
            async: false,
            data: {
                type: "report",
                periode: periode,
                stock_recap_id: (stockRecapId == "ALL") ? null : stockRecapId,
                total_keluar: (totalKeluar == "ALL") ? null : totalKeluar
            },
            success: function (response) {
                $("#target-report").html(response);
            },
        });
    }
});
