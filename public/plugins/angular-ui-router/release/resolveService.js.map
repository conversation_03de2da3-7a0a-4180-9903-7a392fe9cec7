{"version": 3, "file": "resolveService.js", "sources": ["angular-ui-router/src/legacy/resolveService.ts"], "sourcesContent": ["/** @publicapi @module ng1 */ /** */\nimport { StateObject, PathNode, ResolveContext, Obj, mapObj, resolvablesBuilder } from '@uirouter/core';\nimport * as angular from 'angular';\n\n/**\n * Implementation of the legacy `$resolve` service for angular 1.\n */\nconst $resolve = {\n  /**\n   * Asynchronously injects a resolve block.\n   *\n   * This emulates most of the behavior of the ui-router 0.2.x $resolve.resolve() service API.\n   *\n   * ### Not bundled by default\n   *\n   * This API is no longer not part of the standard `@uirouter/angularjs` bundle.\n   * For users of the prebuilt bundles, add the `release/resolveService.min.js` UMD bundle.\n   * For bundlers (webpack, browserify, etc), add `@uirouter/angularjs/lib/legacy/resolveService`.\n   *\n   * ---\n   *\n   * Given an object `invocables`, where keys are strings and values are injectable functions,\n   * injects each function, and waits for the resulting promise to resolve.\n   * When all resulting promises are resolved, returns the results as an object.\n   *\n   * #### Example:\n   * ```js\n   * let invocables = {\n   *   foo: [ '$http', ($http) =>\n   *            $http.get('/api/foo').then(resp => resp.data) ],\n   *   bar: [ 'foo', '$http', (foo, $http) =>\n   *            $http.get('/api/bar/' + foo.barId).then(resp => resp.data) ]\n   * }\n   * $resolve.resolve(invocables)\n   *     .then(results => console.log(results.foo, results.bar))\n   * // Logs foo and bar:\n   * // { id: 123, barId: 456, fooData: 'foo data' }\n   * // { id: 456, barData: 'bar data' }\n   * ```\n   *\n   * @param invocables an object which looks like an [[StateDeclaration.resolve]] object; keys are resolve names and values are injectable functions\n   * @param locals key/value pre-resolved data (locals)\n   * @param parent a promise for a \"parent resolve\"\n   */\n  resolve: (invocables: { [key: string]: Function }, locals = {}, parent?: Promise<any>) => {\n    const parentNode = new PathNode(new StateObject(<any>{ params: {}, resolvables: [] }));\n    const node = new PathNode(new StateObject(<any>{ params: {}, resolvables: [] }));\n    const context = new ResolveContext([parentNode, node]);\n\n    context.addResolvables(resolvablesBuilder(<any>{ resolve: invocables }), node.state);\n\n    const resolveData = (parentLocals: Obj) => {\n      const rewrap = (_locals: Obj) => resolvablesBuilder(<any>{ resolve: mapObj(_locals, (local) => () => local) });\n      context.addResolvables(rewrap(parentLocals), parentNode.state);\n      context.addResolvables(rewrap(locals), node.state);\n\n      const tuples2ObjR = (acc: Obj, tuple: { token: any; value: any }) => {\n        acc[tuple.token] = tuple.value;\n        return acc;\n      };\n      return context.resolvePath().then((results) => results.reduce(tuples2ObjR, {}));\n    };\n\n    return parent ? parent.then(resolveData) : resolveData({});\n  },\n};\n\n/** @hidden */\nexport const resolveFactory = () => $resolve;\n\n// The old $resolve service\nangular.module('ui.router').factory('$resolve', <any>resolveFactory);\n"], "names": ["PathNode", "StateObject", "ResolveContext", "resolvablesBuilder", "mapObj", "angular.module"], "mappings": ";;;;;;;;;;;;IAAA;IAIA;;;IAGA,IAAM,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAqCf,OAAO,EAAE,UAAC,UAAuC,EAAE,MAAW,EAAE,MAAqB;YAAlC,uBAAA,EAAA,WAAW;YAC5D,IAAM,UAAU,GAAG,IAAIA,aAAQ,CAAC,IAAIC,gBAAW,CAAM,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACvF,IAAM,IAAI,GAAG,IAAID,aAAQ,CAAC,IAAIC,gBAAW,CAAM,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;YACjF,IAAM,OAAO,GAAG,IAAIC,mBAAc,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;YAEvD,OAAO,CAAC,cAAc,CAACC,uBAAkB,CAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAErF,IAAM,WAAW,GAAG,UAAC,YAAiB;gBACpC,IAAM,MAAM,GAAG,UAAC,OAAY,IAAK,OAAAA,uBAAkB,CAAM,EAAE,OAAO,EAAEC,WAAM,CAAC,OAAO,EAAE,UAAC,KAAK,IAAK,OAAA,cAAM,OAAA,KAAK,GAAA,GAAA,CAAC,EAAE,CAAC,GAAA,CAAC;gBAC/G,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC/D,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEnD,IAAM,WAAW,GAAG,UAAC,GAAQ,EAAE,KAAiC;oBAC9D,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC/B,OAAO,GAAG,CAAC;iBACZ,CAAC;gBACF,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC,GAAA,CAAC,CAAC;aACjF,CAAC;YAEF,OAAO,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;SAC5D;KACF,CAAC;IAEF;QACa,cAAc,GAAG,cAAM,OAAA,QAAQ,IAAC;IAE7C;AACAC,kBAAc,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,EAAO,cAAc,CAAC;;;;;;;;;;;;"}