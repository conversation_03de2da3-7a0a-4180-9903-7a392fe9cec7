(function(g){"function"===typeof define&&define.amd?define(["jquery","datatables.net","datatables.net-buttons"],function(e){return g(e,window,document)}):"object"===typeof exports?module.exports=function(e,f){e||(e=window);if(!f||!f.fn.dataTable)f=require("datatables.net")(e,f).$;f.fn.dataTable.Buttons||require("datatables.net-buttons")(e,f);return g(f,e,e.document)}:g(jQuery,window,document)})(function(g,e,f,h){e=g.fn.dataTable;g.extend(e.ext.buttons,{colvis:function(a,b){return{extend:"collection",
text:function(b){return b.i18n("buttons.colvis","Column visibility")},className:"buttons-colvis",buttons:[{extend:"columnsToggle",columns:b.columns,columnText:b.columnText}]}},columnsToggle:function(a,b){return a.columns(b.columns).indexes().map(function(a){return{extend:"columnToggle",columns:a,columnText:b.columnText}}).toArray()},columnToggle:function(a,b){return{extend:"columnVisibility",columns:b.columns,columnText:b.columnText}},columnsVisibility:function(a,b){return a.columns(b.columns).indexes().map(function(a){return{extend:"columnVisibility",
columns:a,visibility:b.visibility,columnText:b.columnText}}).toArray()},columnVisibility:{columns:h,text:function(a,b,c){return c._columnText(a,c)},className:"buttons-columnVisibility",action:function(a,b,c,d){a=b.columns(d.columns);b=a.visible();a.visible(d.visibility!==h?d.visibility:!(b.length&&b[0]))},init:function(a,b,c){var d=this;b.attr("data-cv-idx",c.columns);a.on("column-visibility.dt"+c.namespace,function(b,e){!e.bDestroying&&e.nTable==a.settings()[0].nTable&&d.active(a.column(c.columns).visible())}).on("column-reorder.dt"+
c.namespace,function(){1===a.columns(c.columns).count()&&(d.text(c._columnText(a,c)),d.active(a.column(c.columns).visible()))});this.active(a.column(c.columns).visible())},destroy:function(a,b,c){a.off("column-visibility.dt"+c.namespace).off("column-reorder.dt"+c.namespace)},_columnText:function(a,b){var c=a.column(b.columns).index(),d=a.settings()[0].aoColumns[c].sTitle;d||(d=a.column(c).header().innerHTML);d=d.replace(/\n/g," ").replace(/<br\s*\/?>/gi," ").replace(/<select(.*?)<\/select>/g,"").replace(/<!\-\-.*?\-\->/g,
"").replace(/<.*?>/g,"").replace(/^\s+|\s+$/g,"");return b.columnText?b.columnText(a,c,d):d}},colvisRestore:{className:"buttons-colvisRestore",text:function(a){return a.i18n("buttons.colvisRestore","Restore visibility")},init:function(a,b,c){c._visOriginal=a.columns().indexes().map(function(b){return a.column(b).visible()}).toArray()},action:function(a,b,c,d){b.columns().every(function(a){a=b.colReorder&&b.colReorder.transpose?b.colReorder.transpose(a,"toOriginal"):a;this.visible(d._visOriginal[a])})}},
colvisGroup:{className:"buttons-colvisGroup",action:function(a,b,c,d){b.columns(d.show).visible(!0,!1);b.columns(d.hide).visible(!1,!1);b.columns.adjust()},show:[],hide:[]}});return e.Buttons});
