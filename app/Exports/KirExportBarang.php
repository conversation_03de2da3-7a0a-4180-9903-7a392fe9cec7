<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class KirExportBarang implements WithTitle, WithStyles
{
    protected $data;
    protected $room;

    public function __construct($data, $room = null)
    {
        $this->data = $data;
        if ($room) {
            $this->room = $room['room'] ?? null;
        }
    }

    public function title(): string
    {
        return 'Kartu Inventaris Ruangan (KIR)';
    }

    public function styles(Worksheet $sheet)
    {
        $roomName = $this->room ? $this->room->room_name : '<PERSON><PERSON>a <PERSON>';

        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'));
        $sheet->setCellValue('A3', 'KARTU INVENTARIS RUANGAN (KIR)');
        $sheet->setCellValue('A5', 'Provinsi');
        $sheet->setCellValue('B5', ': ' . strtoupper(config('app.hospital_province')));
        $sheet->setCellValue('A6', 'Kotamadia');
        $sheet->setCellValue('B6', ': ' . strtoupper(config('app.hospital_city')));
        $sheet->setCellValue('A7', 'Satuan Kerja');
        $sheet->setCellValue('B7', ': ' . config('app.report_header_hospital'));
        $sheet->setCellValue('A8', 'Ruangan');

        $sheet->setCellValue('B8', ': ' . $roomName);

        $sheet->mergeCells('A1:O1');
        $sheet->mergeCells('A3:O3');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A5')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('A6')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('A7')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A7')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('A8')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A8')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $sheet->mergeCells('A10:A11');
        $sheet->setCellValue('A10', 'No. Urut');
        $sheet->mergeCells('B10:B11');
        $sheet->setCellValue('B10', 'Jenis Barang / Nama Barang');
        $sheet->mergeCells('C10:C11');
        $sheet->mergeCells('D10:D11');
        $sheet->mergeCells('E10:E11');
        $sheet->mergeCells('F10:F11');
        $sheet->mergeCells('G10:G11');
        $sheet->mergeCells('H10:H11');
        $sheet->mergeCells('I10:I11');
        $sheet->mergeCells('J10:J11');
        $sheet->mergeCells('K10:K11');
        $sheet->setCellValue('C10', 'Merk / Model');
        $sheet->setCellValue('D10', 'No. Seri Pabrikan');
        $sheet->setCellValue('E10', 'Ukuran');
        $sheet->setCellValue('F10', 'Bahan');
        $sheet->setCellValue('G10', 'Tahun Pembuatan / Pembelian');
        $sheet->setCellValue('H10', 'No. Kode Barang');
        $sheet->setCellValue('I10', 'Register');
        $sheet->setCellValue('J10', 'Jumlah Barang');
        $sheet->setCellValue('K10', 'Harga Beli Perolehan (Rp.)');
        $sheet->mergeCells('L10:N10');
        $sheet->setCellValue('L10', 'Keadaan Barang');
        $sheet->setCellValue('L11', 'B');
        $sheet->setCellValue('M11', 'RR');
        $sheet->setCellValue('N11', 'RB');
        $sheet->mergeCells('O10:O11');
        $sheet->setCellValue('O10', 'Keterangan Mutasi/DLL');

        $sheet->getStyle('A10:O10')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A10:O10')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A10:O10')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle('A10:O12')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A10:O12')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A10:O12')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        foreach (range('A', 'O') as $col) {
            $sheet->getColumnDimension($col)->setWidth(20);
        }

        $sheet->setCellValue('A12', '1');
        $sheet->setCellValue('B12', '2');
        $sheet->setCellValue('C12', '3');
        $sheet->setCellValue('D12', '4');
        $sheet->setCellValue('E12', '5');
        $sheet->setCellValue('F12', '6');
        $sheet->setCellValue('G12', '7');
        $sheet->setCellValue('H12', '8');
        $sheet->mergeCells('I12:J12');
        $sheet->setCellValue('I12', '9');
        $sheet->setCellValue('K12', '10');
        $sheet->setCellValue('L12', '11');
        $sheet->setCellValue('M12', '12');
        $sheet->setCellValue('N12', '13');
        $sheet->setCellValue('O12', '14');
        $row = 13;
        foreach ($this->data as $index => $asset) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $asset->item_name);
            $sheet->setCellValue('C' . $row, $asset->brand);
            $sheet->setCellValue('D' . $row, $asset->serialNumbers);
            $sheet->setCellValue('E' . $row, $asset->size ?? '-');
            $sheet->setCellValue('F' . $row, $asset->material);
            $sheet->setCellValue('G' . $row, $asset->year_of_manufacture ?? '-');
            $sheet->setCellValue('H' . $row, $asset->item_code);
            $sheet->setCellValue('I' . $row, $asset->registerCodes);
            $sheet->setCellValue('J' . $row, $asset->asset_count);
            $sheet->setCellValue('K' . $row, number_format($asset->unit_price, 0, ',', '.'));
            $sheet->setCellValue('L' . $row, $asset->condition == "BAIK" ? "✅" : "-");
            $sheet->setCellValue('M' . $row, $asset->condition == "RUSAK RINGAN" ? "✅" : "-");
            $sheet->setCellValue('N' . $row, $asset->condition == "RUSAK BERAT" ? "✅" : "-");
            $sheet->setCellValue('O' . $row, $asset->description ?? '-');

            $sheet->getStyle('A' . $row . ':O' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $row++;
        }

        $sheet->getStyle('A11:O' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    }
}
