<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EmployeeTemplateExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths, WithTitle, WithColumnFormatting
{
    public function array(): array
    {
        return [
            [
                "KODE_001",
                fake("id_ID")->name(),
                'PTT',
                'Staf Instalasi Logistik',
                'KARYAWAN',
                'EMPLOYEE',
                'SIL'
            ],
            [
                "KODE_002",
                fake("id_ID")->name(),
                'Penata Muda',
                'Kepala Instalasi Logistik',
                'PIC_PENYIMPANAN_BARANG',
                'EMPLOYEE',
                'KIL'
            ],
            [
                "KODE_003",
                fake("id_ID")->name(),
                '-',
                'Ketua Tim Kerja Penunjang Diagnostik dan Non Diagostik',
                'KARYAWAN',
                'EMPLOYEE',
                'PJKIR'
            ],
            [
                "KODE_004",
                fake("id_ID")->name(),
                'III/b',
                'Penanggung Jawab KIR',
                'KARYAWAN',
                'EMPLOYEE',
                'SIPFRS'
            ],
            [
                "KODE_005",
                fake("id_ID")->name(),
                'III',
                'Karu Enggang Gading',
                'KARYAWAN',
                'EMPLOYEE',
                'PJKIR'
            ]
        ];
    }

    public function headings(): array
    {
        return [
            'kode_karyawan' => 'Kode Karyawan',
            'nama_karyawan' => 'Nama Karyawan',
            'golongan_karyawan' => 'Golongan Karyawan',
            'jabatan_karyawan' => 'Jabatan Karyawan',
            'tipe_karyawan' => 'Tipe Karyawan',
            'kategori' => 'Kategori',
            'role_code' => 'Role Code'
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Set row height for better spacing
        $sheet->getRowDimension('1')->setRowHeight(25);
        $sheet->getRowDimension('2')->setRowHeight(20);
        $sheet->getRowDimension('3')->setRowHeight(20);
        $sheet->getRowDimension('4')->setRowHeight(20);
        $sheet->getRowDimension('5')->setRowHeight(20);
        $sheet->getRowDimension('6')->setRowHeight(20);

        return [
            // Professional header styling
            1 => [
                'font' => [
                    'bold' => true,
                    'size' => 12,
                    'color' => ['rgb' => 'FFFFFF'],
                    'name' => 'Calibri',
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2C3E50'], // Professional dark navy
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => ['rgb' => '34495E'],
                    ],
                ],
            ],
            // Alternating row colors for better readability
            2 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            3 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            4 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            5 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F8F9FA'], // Light gray
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            6 => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFFF'], // White
                ],
                'font' => [
                    'size' => 11,
                    'name' => 'Calibri',
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D5D8DC'],
                    ],
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 20, // kode_karyawan - wider for employee identification numbers
            'B' => 30, // nama_karyawan - wider for longer names with titles
            'C' => 18, // golongan_karyawan - professional spacing
            'D' => 35, // jabatan_karyawan - accommodate longer position titles
            'E' => 25, // tipe_karyawan - adequate spacing for PIC types
            'F' => 15, // kategori - adequate for category
            'G' => 15, // role_code - adequate for role codes
        ];
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT, // Format kode_karyawan sebagai text
        ];
    }

    public function title(): string
    {
        return 'Template Import Karyawan';
    }
}
