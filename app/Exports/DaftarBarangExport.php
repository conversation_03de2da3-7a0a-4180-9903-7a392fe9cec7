<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class DaftarBarangExport implements WithTitle, WithStyles
{
    protected $data;
    protected $params;

    public function __construct($data, $params)
    {
        $this->data = $data;
        $this->params = $params;
    }

    public function title(): string
    {
        return 'Daftar Barang Aset Peralatan dan Mesin';
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setCellValue('A1', 'Laporan Dibuat: ' . date('Y-m-d : H:i:s'));
        $sheet->setCellValue('A3', 'Laporan Daftar Barang');
        $sheet->setCellValue('A4', 'aset peralatan dan mesin');
        $sheet->setCellValue('A5', config('app.report_header_hospital'));
        $sheet->setCellValue('A6', 'Tahun: ' . now()->year);

        $sheet->mergeCells('A1:T1');
        $sheet->mergeCells('A3:T3');
        $sheet->mergeCells('A4:T4');
        $sheet->mergeCells('A5:T5');
        $sheet->mergeCells('A5:T5');
        $sheet->mergeCells('A6:T6');

        $sheet->getStyle('A1')->getFont()->setItalic(true);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A4')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A5')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A6')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet->mergeCells('A8:A9');
        $sheet->setCellValue('A8', 'No');
        $sheet->mergeCells('B8:C9');
        $sheet->setCellValue('B8', 'Kode');
        $sheet->setCellValue('B9', 'Barang');
        $sheet->setCellValue('C9', 'Register');
        $sheet->mergeCells('D8:D9');
        $sheet->setCellValue('D8', 'Nama Barang');
        $sheet->mergeCells('E8:E9');
        $sheet->mergeCells('F8:F9');
        $sheet->mergeCells('G8:G9');
        $sheet->mergeCells('H8:H9');
        $sheet->mergeCells('I8:I9');
        $sheet->setCellValue('E8', 'Merk/Type');
        $sheet->setCellValue('F8', 'Bahan');
        $sheet->setCellValue('G8', 'Kondisi');
        $sheet->setCellValue('H8', 'Asal Perolehan');
        $sheet->setCellValue('I8', 'Tanggal Perolehan');
        $sheet->mergeCells('J8:M8');
        $sheet->setCellValue('J8', 'Nomor');
        $sheet->setCellValue('J9', 'Rangka');
        $sheet->setCellValue('K9', 'Mesin');
        $sheet->setCellValue('L9', 'Polisi');
        $sheet->setCellValue('M9', 'BPKB');
        $sheet->mergeCells('N8:N9');
        $sheet->mergeCells('O8:O9');
        $sheet->mergeCells('P8:P9');
        $sheet->mergeCells('Q8:Q9');
        $sheet->mergeCells('R8:R9');
        $sheet->mergeCells('S8:S9');
        $sheet->mergeCells('T8:T9');
        $sheet->setCellValue('N8', 'UEB');
        $sheet->setCellValue('O8', 'Harga Perolehan');
        $sheet->setCellValue('P8', 'Akumulasi Penyusutan Sebelumnya');
        $sheet->setCellValue('Q8', 'Beban Penyusutan');
        $sheet->setCellValue('R8', 'Akumulasi Penyusutan');
        $sheet->setCellValue('S8', 'Sisa UEB');
        $sheet->setCellValue('T8', 'Nilai Buku');

        $sheet->getStyle('A9:T9')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A9:T9')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A9:T9')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle('A8:T9')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A8:T9')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A8:T9')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        $sheet->getStyle('A8:T9')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        foreach (range('A', 'T') as $col) {
            $sheet->getColumnDimension($col)->setWidth(20);
        }

        $row = 10;
        foreach ($this->data as $asset) {
            $sheet->setCellValue('A' . $row, $row - 8);
            $sheet->setCellValue('B' . $row, $asset->item->item_code);
            $sheet->setCellValue('C' . $row, $asset->register_code);
            $sheet->setCellValue('D' . $row, $asset->item->item_name);
            $sheet->setCellValue('E' . $row, $asset->assetEntry->brand);
            $sheet->setCellValue('F' . $row, $asset->assetEntry->material);
            $sheet->setCellValue('G' . $row, $asset->asset_condition);
            $sheet->setCellValue('H' . $row, $asset->assetEntry->source_supply);
            $sheet->setCellValue('I' . $row, $asset->assetEntry->received_date);
            $sheet->setCellValue('J' . $row, $asset->chassis_number);
            $sheet->setCellValue('K' . $row, $asset->engine_number);
            $sheet->setCellValue('L' . $row, $asset->license_plate_number);
            $sheet->setCellValue('M' . $row, $asset->bpkb_number);
            $sheet->setCellValue('N' . $row, '-');
            $sheet->setCellValue('O' . $row, '-');
            $sheet->setCellValue('P' . $row, '-');
            $sheet->setCellValue('Q' . $row, '-');
            $sheet->setCellValue('R' . $row, '-');
            $sheet->setCellValue('S' . $row, '-');
            $sheet->setCellValue('T' . $row, '-');

            $sheet->getStyle('A' . $row . ':T' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
            $row++;
        }

        $sheet->getStyle('A9:T' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    }
}
