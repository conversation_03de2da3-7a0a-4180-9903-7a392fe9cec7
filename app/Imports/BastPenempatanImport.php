<?php

namespace App\Imports;

use App\Models\DocumentBast;
use App\Models\RegisterAsset;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class BastPenempatanImport implements ToModel, WithHeadingRow
{
    protected $fileDocsUrl;
    protected $type;

    public function __construct($fileDocsUrl, $type)
    {
        $this->fileDocsUrl = $fileDocsUrl;
        $this->type = $type;
    }

    public function model(array $row)
    {
        $asset = RegisterAsset::where(["register_code" => $row["kode_register"], "qr_code" => $row["qr_code"]])->first();

        return DocumentBast::create([
            "register_asset_id" => $asset->id,
            "type" => $this->type,
            "path_document" => $this->fileDocsUrl
        ]);
    }
}
