<?php

namespace App\Http\Controllers\AssetMaintenance;

use App\Http\Controllers\Controller;
use App\Models\IncidentalRequest;
use App\Models\MaintenanceActivity;
use App\Models\MaintenanceSchedule;
use Carbon\Carbon;

class HomeController extends Controller
{
    function index()
    {
        $title = "Pemeliharaan Aset";
        $breadcrumbs = ["Pemeliharaan Aset"];

        // Get this month's ALKES maintenance count
        $alkesMaintenanceCount = MaintenanceActivity::where('activity_category', 'ALKES')
            ->whereMonth('activity_date', Carbon::now()->month)
            ->whereYear('activity_date', Carbon::now()->year)
            ->count();

        // Get this month's NON ALKES maintenance count
        $nonAlkesMaintenanceCount = MaintenanceActivity::where('activity_category', 'NON_ALKES')
            ->whereMonth('activity_date', Carbon::now()->month)
            ->whereYear('activity_date', Carbon::now()->year)
            ->count();

        // Get open incidental requests count
        $openRequestCount = IncidentalRequest::where('latest_activity_status', 'OPEN')
            ->count();

        // Get this month's maintenance schedules
        $maintenanceSchedules = MaintenanceSchedule::select('maintenance_schedules.*', 'items.item_name')
            ->join('items', 'items.id', '=', 'maintenance_schedules.item_id')
            ->whereMonth('schedule_date', Carbon::now()->month)
            ->whereYear('schedule_date', Carbon::now()->year)
            ->orderBy('schedule_date')
            ->get();

        return view("asset-maintenance.home.index", compact(
            "title", 
            "breadcrumbs",
            "alkesMaintenanceCount",
            "nonAlkesMaintenanceCount", 
            "openRequestCount",
            "maintenanceSchedules"
        ));
    }
}
