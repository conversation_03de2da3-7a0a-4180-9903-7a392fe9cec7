<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use App\Imports\DataAssetImport;
use App\Models\Asset;
use App\Models\AssetEntry;
use App\Models\DocumentAssetMedia;
use App\Models\Item;
use App\Models\Uom;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Yajra\DataTables\Facades\DataTables;

class AssetHospitalController extends Controller
{
    public function index()
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'Data Aset';
        $breadcrumbs = ['Manajemen Aset', 'Data Aset'];

        return view('asset-management.asset-hospital.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        if ($request->ajax()) {
            $data = DB::table('assets')
                ->leftJoin('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->leftJoin('items', 'assets.item_id', '=', 'items.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->select(
                    'assets.*',
                    'asset_entries.payment_date',
                    'asset_entries.received_date',
                    'asset_entries.source_supply',
                    'items.item_name',
                    'items.item_code',
                    'rooms.room_name'
                )
                ->when($request->filter, function ($query) {
                    if (request('filter') == 'not-allocation') {
                        $query->whereNull('document_room_id');
                    }
                })
                ->where('assets.category_type', 'EQUIPMENT');

            return DataTables::of($data)
                ->addIndexColumn()
                ->editColumn('qr_code', function ($row) {
                    return '<div class="text-center align-middle">'.QrCode::size(50)->generate($row->qr_code).'</div>';
                })
                ->addColumn('real_qr_code', function ($row) {
                    return $row->qr_code;
                })
                ->addColumn('action', function ($row) {
                    return '<a href="'.route('asset-management.asset-hospital.edit', $row->id).'" class="btn btn-sm btn-outline-warning btn-edit"><i class="fas fa-edit"></i></a>';
                })
                ->addColumn('asset_year', function ($row) {
                    return Carbon::parse($row->received_date)->format('Y');
                })
                ->filterColumn('item_name', function ($query, $keyword) {
                    $query->where('items.item_name', 'like', "%{$keyword}%");
                })
                ->filterColumn('item_code', function ($query, $keyword) {
                    $query->where('items.item_code', 'like', "%{$keyword}%");
                })
                ->filterColumn('register_code', function ($query, $keyword) {
                    $query->where('assets.register_code', 'like', "%{$keyword}%");
                })
                ->filterColumn('serial_number', function ($query, $keyword) {
                    $query->where('assets.serial_number', 'like', "%{$keyword}%");
                })
                ->filterColumn('payment_date', function ($query, $keyword) {
                    $query->where('asset_entries.payment_date', 'like', "%{$keyword}%");
                })
                ->filterColumn('received_date', function ($query, $keyword) {
                    $query->where('asset_entries.received_date', 'like', "%{$keyword}%");
                })
                ->filterColumn('source_supply', function ($query, $keyword) {
                    $query->where('asset_entries.source_supply', 'like', "%{$keyword}%");
                })
                ->filterColumn('room_name', function ($query, $keyword) {
                    $query->where('rooms.room_name', 'like', "%{$keyword}%");
                })
                ->addColumn('foto', function ($row) {
                    $lastDoc = DocumentAssetMedia::join('document_assets', 'document_assets.id', '=', 'document_asset_medias.document_asset_id')
                        ->join('documents', 'documents.id', '=', 'document_assets.document_id')
                        ->where(['document_assets.asset_id' => $row->id])
                        ->whereIn('document_type', ['PENEMPATAN', 'MUTASI', 'RUSAK'])
                        ->orderBy('document_asset_medias.id', 'DESC')
                        ->first();

                    if ($lastDoc) {
                        return '<img src="'.asset('/storage/'.$lastDoc->media_path).'" width="70px" height="70px" style="cursor: pointer;" class="img-fluid text-center img-asset">';
                    }

                    return "<span class='text-danger'>Belum ada foto</span>";
                })
                ->addColumn('default_image', function ($row) {
                    if ($row->default_image) {
                        return '<img src="'.asset('/storage/'.$row->default_image).'" width="70px" height="70px" style="cursor: pointer;" class="img-fluid text-center img-asset">';
                    }

                    return "<span class='text-danger'>Belum ada foto</span>";
                })
                ->rawColumns(['action', 'qr_code', 'foto', 'default_image'])
                ->make(true);
        }
    }

    public function generate_register_code(Request $request)
    {
        try {
            $item = Item::find($request->kode_barang);
            $jumlahbarang = $request->jumlah_barang;
            $lastcode = Asset::where('qr_code', 'like', '%'.now('Asia/Jakarta')->format('Ymd').'%')->where('item_id', $item->id)->latest('register_code')->first();

            if ($lastcode) {
                $codeParts = explode('.', $lastcode->register_code);
                $lastSegment = end($codeParts);
                $start_register_code = str_pad(((int) $lastSegment + 1), 4, '0', STR_PAD_LEFT);
            } else {
                $start_register_code = '0001';
            }

            $registercodes = [];

            for ($i = 1; $i <= $jumlahbarang; $i++) {
                $registercode = str_pad($start_register_code++, 4, '0', STR_PAD_LEFT);
                $dynamic_registercode = $registercode;
                $dynamic_qrcode = $item->item_code.'.'.$registercode.'.'.now('Asia/Jakarta')->format('Ymd');
                $registercodes[] = [
                    'register_code' => $dynamic_registercode,
                    'qr_code' => $dynamic_qrcode,
                ];
            }

            return response()->json([
                'register_codes' => $registercodes,
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    public function store(Request $request)
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'entry_date' => 'required|date|date_format:Y-m-d',
            'payment_date' => 'required|date|date_format:Y-m-d',
            'contract_number' => 'required|string|max:255',
            'job_description' => 'required|string|max:255',
            'payment_ba_number' => 'required|string|max:255',
            // Align options with UI (kso/pinjam used in view)
            'acquisition_source' => 'required|string|in:apbn,apbd,hibah,kso/pinjam,blud,jkn,dak,sisoin,swasta',
            'distributor_id' => 'required|exists:distributors,id',
            'contract_type' => 'required|string|max:255',
            'description' => 'nullable|string',
            // ASPAK fields
            'aspak_distributor' => 'nullable|string|max:255',
            'aspak_maintenance_officer' => 'nullable|string|max:255',
            // SIAP BMD fields
            'siap_bmd_rkbmd_code' => 'nullable|string|max:255',
            'siap_bmd_activity' => 'nullable|string|max:255',
            'siap_bmd_sub_activity' => 'nullable|string|max:255',
            'siap_bmd_kusa_name' => 'nullable|string|max:255',
            'items' => 'required|array|min:1',
            'items.*.item_id' => 'required|integer|exists:items,id',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.contract_item_name' => 'required|string|max:255',
            'items.*.brand' => 'required|string|max:255',
            'items.*.type_model' => 'required|string|max:255',
            'items.*.material' => 'required|string|max:255',
            'items.*.size' => 'required|string|max:255',
            'items.*.uom_id' => 'required|exists:uoms,id',
            'items.*.unit_price' => 'required|numeric|min:1000',
            'items.*.serial_number' => 'required|string|max:255',
            'items.*.rfid_tag' => 'nullable|string|max:255',
            'items.*.frame_number' => 'nullable|string|max:255',
            'items.*.engine_number' => 'nullable|string|max:255',
            'items.*.police_number' => 'nullable|string|max:255',
            'items.*.bpkb_number' => 'nullable|string|max:255',
            'items.*.condition' => 'required|string|in:BAIK,RUSAK_RINGAN,RUSAK_BERAT',
            'items.*.akd_akl_number' => 'required|string|max:255',
            // ASPAK item fields (send IDs, not text)
            'items.*.aspak_item_id' => 'nullable|integer',
            'items.*.aspak_service_room_id' => 'nullable|integer',
            'items.*.aspak_alat' => 'nullable|string|max:255',
            'items.*.aspak_product_origin' => 'nullable|string|max:255',
            'items.*.general_specification' => 'nullable|string|max:255',
            'items.*.asset_description' => 'nullable|string|max:255',
            'items.*.aspak_image_url' => 'nullable|string|max:255',
            'items.*.aspak_power_source' => 'nullable|string|max:255',
            'items.*.aspak_electricity' => 'nullable|string|max:255',
            'items.*.aspak_tersambung_ups' => 'nullable|boolean',
            'items.*.aspak_information_tkdn' => 'nullable|string|max:255',
            'items.*.aspak_risk_class' => 'nullable|string|max:255',
            'items.*.aspak_technical_lifetime_year' => 'nullable|string|max:255',
        ], [], [
            'entry_date' => 'Tanggal Barang Masuk',
            'payment_date' => 'Tanggal Pembayaran',
            'contract_number' => 'No. BAST Kontrak',
            'job_description' => 'Uraian Pekerjaan',
            'payment_ba_number' => 'No. BAST Pembayaran',
            'acquisition_source' => 'Asal Perolehan',
            'distributor_id' => 'Distributor',
            'contract_type' => 'Jenis Kontrak',
            'description' => 'Keterangan',
            'aspak_distributor' => 'Distributor ASPAK',
            'aspak_maintenance_officer' => 'Petugas Maintenance ASPAK',
            'siap_bmd_rkbmd_code' => 'Kode RKBMD SIAP BMD',
            'siap_bmd_activity' => 'Aktivitas SIAP BMD',
            'siap_bmd_sub_activity' => 'Sub Aktivitas SIAP BMD',
            'siap_bmd_kusa_name' => 'Nama KUSA SIAP BMD',
            'items' => 'Daftar Barang',
            'items.*.item_id' => 'Kode Barang',
            'items.*.item_name' => 'Nama Barang',
            'items.*.contract_item_name' => 'Nama Barang Kontrak',
            'items.*.brand' => 'Merk',
            'items.*.type_model' => 'Tipe/Model',
            'items.*.material' => 'Bahan',
            'items.*.size' => 'Ukuran',
            'items.*.uom_id' => 'Satuan Barang',
            'items.*.unit_price' => 'Harga Satuan',
            'items.*.serial_number' => 'No. Seri',
            'items.*.rfid_tag' => 'Tag RFID',
            'items.*.frame_number' => 'No. Rangka',
            'items.*.engine_number' => 'No. Mesin',
            'items.*.police_number' => 'No. Polisi',
            'items.*.bpkb_number' => 'No. BPKB',
            'items.*.condition' => 'Kondisi',
            'items.*.akd_akl_number' => 'No. AKD/AKL',
            'items.*.aspak_item_id' => 'Item ASPAK',
            'items.*.aspak_service_room_id' => 'Ruangan Service ASPAK',
            'items.*.aspak_alat' => 'Alat ASPAK',
            'items.*.aspak_product_origin' => 'Asal Produk ASPAK',
            'items.*.general_specification' => 'Spesifikasi Umum',
            'items.*.asset_description' => 'Deskripsi Aset',
            'items.*.aspak_image_url' => 'URL Gambar ASPAK',
            'items.*.aspak_power_source' => 'Sumber Daya ASPAK',
            'items.*.aspak_electricity' => 'Daya Listrik ASPAK',
            'items.*.aspak_tersambung_ups' => 'Tersambung UPS ASPAK',
            'items.*.aspak_information_tkdn' => 'Informasi TKDN ASPAK',
            'items.*.aspak_risk_class' => 'Kelas Risiko ASPAK',
            'items.*.aspak_technical_lifetime_year' => 'Umur Teknis (Tahun) ASPAK',
        ]);

        try {
            DB::beginTransaction();

            // Compute aggregate values from items
            $itemsPayload = $validated['items'] ?? [];
            $computedQuantity = count($itemsPayload);
            $computedTotalPrice = collect($itemsPayload)->sum(function ($it) {
                return (float) ($it['unit_price'] ?? 0);
            });

            $assetEntry = AssetEntry::create([
                'received_date' => $validated['entry_date'],
                'purchasing_account' => $validated['job_description'],
                'bast_contract_number' => $validated['contract_number'],
                'payment_date' => $validated['payment_date'],
                'quantity' => $computedQuantity,
                'total_price' => $computedTotalPrice,
                'bast_payment_number' => $validated['payment_ba_number'],
                'source_supply' => $validated['acquisition_source'],
                'distributor_id' => $validated['distributor_id'],
                'contract_form' => $validated['contract_type'],
                'description' => $validated['description'],
                // ASPAK mappings
                'aspak_distributor' => $validated['aspak_distributor'] ?? null,
                'aspak_maintenance_pic' => $validated['aspak_maintenance_officer'] ?? null,
                // SIAP BMD mappings
                'siap_bmd_asset_need_plan_code' => $validated['siap_bmd_rkbmd_code'] ?? null,
                'siap_bmd_activity_id' => $validated['siap_bmd_activity'] ?? null,
                'siap_bmd_sub_activity_id' => $validated['siap_bmd_sub_activity'] ?? null,
                'siap_bmd_asset_user_name' => $validated['siap_bmd_kusa_name'] ?? null,
                'created_by' => getAuthUserId(),
                'created_by_name' => getAuthUserName(),
            ]);

            $itemRegisterCodes = [];

            foreach ($validated['items'] as $itemData) {
                $item = Item::find($itemData['item_id']);
                $uom = Uom::find($itemData['uom_id']);

                if (! isset($itemRegisterCodes[$item->id])) {
                    $lastCode = Asset::where('qr_code', 'like', '%'.now('Asia/Jakarta')->format('ymd').'%')
                        ->where('item_id', $item->id)
                        ->latest('register_code')
                        ->first();

                    if ($lastCode) {
                        $codeParts = explode('.', $lastCode->register_code);
                        $lastSegment = end($codeParts);
                        $startRegisterCode = (int) $lastSegment + 1;
                    } else {
                        $startRegisterCode = 1;
                    }

                    $itemRegisterCodes[$item->id] = $startRegisterCode;
                }

                $registerCode = str_pad($itemRegisterCodes[$item->id]++, 4, '0', STR_PAD_LEFT);
                $qrCode = $item->item_code.'.'.$registerCode.'.'.now('Asia/Jakarta')->format('ymd');

                Asset::create([
                    'asset_entry_id' => $assetEntry->id,
                    'item_id' => $item->id,
                    'item_name' => $itemData['item_name'],
                    'asset_name' => $itemData['contract_item_name'],
                    'brand' => $itemData['brand'],
                    'type' => $itemData['type_model'],
                    'material' => $itemData['material'],
                    'size' => $itemData['size'],
                    'uom_id' => $itemData['uom_id'],
                    'uom_name' => $uom->uom_name,
                    'unit_price' => $itemData['unit_price'],
                    'serial_number' => $itemData['serial_number'],
                    'rfid_tag' => $itemData['rfid_tag'] ?? null,
                    'chassis_number' => $itemData['frame_number'] ?? null,
                    'engine_number' => $itemData['engine_number'] ?? null,
                    'license_plate_number' => $itemData['police_number'] ?? null,
                    'bpkb_number' => $itemData['bpkb_number'] ?? null,
                    'asset_condition' => $itemData['condition'],
                    'description' => $itemData['asset_description'] ?? null,
                    'akd_number' => $itemData['akd_akl_number'],
                    'general_specifications' => $itemData['general_specification'] ?? null,
                    'asset_code' => $item->item_code.'.'.$registerCode,
                    'register_code' => $registerCode,
                    'qr_code' => $qrCode,
                    // ASPAK item mappings (use IDs)
                    'aspak_item_id' => $itemData['aspak_item_id'] ?? null,
                    'aspak_service_room_id' => $itemData['aspak_service_room_id'] ?? null,
                    'aspak_tool' => $itemData['aspak_alat'] ?? null,
                    'product_origin' => $itemData['aspak_product_origin'] ?? null,
                    'aspak_description' => $itemData['asset_description'] ?? null,
                    'aspak_image_url' => $itemData['aspak_image_url'] ?? null,
                    'power_source' => $itemData['aspak_power_source'] ?? null,
                    'electricity' => $itemData['aspak_electricity'] ?? null,
                    'connected_ups' => $itemData['aspak_tersambung_ups'] ?? null,
                    'information_tkdn' => $itemData['aspak_information_tkdn'] ?? null,
                    'risk_class' => $itemData['aspak_risk_class'] ?? null,
                    'technical_lifetime_year' => $itemData['aspak_technical_lifetime_year'] ?? null,
                    'created_by' => getAuthUserId(),
                    'created_by_name' => getAuthUserName(),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil disimpan',
                'redirect' => route('asset-management.asset-hospital.index'),
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    public function create()
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'Tambah Aset';
        $breadcrumbs = ['Manajemen Aset', 'Data Aset', 'Tambah Aset'];
        $hospitalName = config('app.hospital_name');

        return view('asset-management.asset-hospital.create', compact('title', 'breadcrumbs', 'hospitalName'));
    }

    public function show(Asset $asset)
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }
        // Frontend-render refactor: return only data and QR string; do not render HTML here
        $asset->load([
            'item',
            'room:id,room_code',
            'assetEntry:id,brand,type,general_specifications,unit_price,distributor_id,source_supply,received_date',
            'assetEntry.distributor:id,distributor_name',
        ]);

        $qrString = "http://sensi.smilers.co.id/manajemen-aset/history-aset?qr={$asset->qr_code}";

        return response()->json([
            'data' => $asset,
            'qrCodeString' => $qrString,
        ], 200);
        $asset->load([
            'item',
            'assetEntry:id,brand,type,general_specifications,unit_price,distributor_id,source_supply,received_date',
        ]);


        $qrString = "http://sensi.smilers.co.id/manajemen-aset/history-aset?qr={$asset->qr_code}";
        $qr = QrCode::size(40)->generate($qrString);
        $qrForPrint = QrCode::size(57)->generate($qrString);

        $roomCode = $asset->room->room_code ?? '-';

        $response = '<div class="mb-3" style="min-width: 90mm !important; max-width: 90mm !important; min-height: 50mm !important">
                <div class="card">
                    <div class="card-body border border-1 border-dark rounded d-flex justify-content-between p-2">
                        <div class="border-end w-25 border-2 border-dark text-center">
                            <img src="/img/logo/logo-kalbar.png" alt="" width="45">
                            <div class="d-block mt-1 text-center">
                                <h6 class="fs-10px">'.$asset->assetEntry->source_supply.' <br> '.Carbon::parse($asset->assetEntry->received_date)->format('Y').' </h6>
                            </div>
                            <div class="d-block">
                                '.$qr.'
                            </div>
                        </div>

                        <div class="text-center">
                            <div class="d-block fw-800 fs-18px">'.config('app.report_header_hospital').'</div>
                            <div class="d-block">11.01.61.00.110202.00001</div>
                            <div class="d-block border-bottom border-3 border-dark"><strong>'.$asset->register_code.'</strong></div>
                            <div class="d-block border-bottom border-3 border-dark fs-16px fw-bold">'.$asset->item->item_code.'</div>
                            <div class="d-block fw-bold">'.$asset->item->item_name.'</div>
                            <div class="d-block" style="padding-top: 1px; padding-bottom: 1px;">'.$roomCode.'</div>
                            <div class="d-block fw-bold"><strong>Rp. '.number_format($asset->assetEntry->unit_price, 0, ',', '.').'</strong> '.$asset->assetEntry->distributor->distributor_name.' </div>
                        </div>
                    </div>
                </div>
            </div>';

        $qrCode = '<div style="">
            <div class="card">
                <div class="card-body border border-1 border-dark rounded p-2 text-center">
                    <div class="d-block">
                        '.$qrForPrint.'
                    </div>
                    <div class="d-block mt-2">
                        <div class="d-block fw-bold">'.$asset->item->item_name.'</div>
                        <div class="d-block">'.$asset->qr_code.'</div>
                    </div>
                </div>
            </div>
        </div>';

        return response()->json([
            'data' => $asset,
            'label' => $response,
            'qrcode' => $qrCode,
            'qrCodeString' => $qrString,
        ], 200);
    }

    public function edit(Asset $asset)
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'Edit Aset';
        $breadcrumbs = ['Manajemen Asset', 'Data Aset', 'Edit Aset'];
        // Load asset entry with related data and a small preview of its assets
        $assetEntry = AssetEntry::with([
            'distributor',
            'siapBmdActivity',
            'siapBmdSubActivity',
            // Preview latest 10 assets for this entry with minimal relations
            'assets' => function ($q) {
                $q->with(['item', 'room'])->latest('id')->limit(10);
            },
        ])->withCount('assets')->find($asset->asset_entry_id);
        $asset->load([
            'item',
            'uom',
            'aspakItem',
            'aspakServiceRoom',
        ]);

        return view('asset-management.asset-hospital.edit', compact('title', 'breadcrumbs', 'asset', 'assetEntry'));
    }

    public function allocation()
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }
        $title = 'Alokasi Asset';
        $breadcrumbs = ['Manajemen Aset', 'Data Aset', 'Alokasi Aset'];

        return view('asset-management.asset-hospital.allocation', compact('title', 'breadcrumbs'));
    }

    public function store_allocation(Request $request): \Illuminate\Http\JsonResponse
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }
        $request->validate([
            'kode_barang' => 'required|array',
            'ruangan' => 'required|array',
        ]);

        try {
            DB::beginTransaction();

            foreach ($request->kode_barang as $key => $val) {
                $asset = Asset::find($val);
                $asset->document_room_id = $request->ruangan[$key];
                $asset->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil disimpan',
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    public function import(Request $request): \Illuminate\Http\JsonResponse
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }
        $request->validate([
            'file_import' => 'required|mimes:csv,xls,xlsx',
        ]);

        try {
            DB::beginTransaction();

            Excel::import(new DataAssetImport, $request->file('file_import'));

            DB::commit();

            return response()->json([
                'message' => 'Import data aset berhasil',
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json([
                'message' => $th->getMessage(),
            ], 500);
        }
    }

    public function download_template()
    {
        $path = public_path('/example/example-import-data-asset.xlsx');

        return response()->download($path);
    }

    public function print_label()
    {
        $title = 'Print Label Aset';
        $breadcrumbs = ['Manajemen Aset', 'Print Label Aset'];

        return view('asset-management.asset-hospital.print-label', compact('title', 'breadcrumbs'));
    }

    public function uploadPhoto(Request $request)
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'asset_id' => 'required|exists:assets,id',
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:10240',
        ]);

        try {
            DB::beginTransaction();

            $asset = Asset::findOrFail($request->asset_id);

            // Upload foto
            $photo = $request->file('photo');
            $photoName = 'asset_'.$asset->id.'_'.time().'.'.$photo->getClientOriginalExtension();
            $photoPath = $photo->storeAs('assets/photos', $photoName, 'public');

            // Delete old photo if exists
            if ($asset->default_image) {
                Storage::disk('public')->delete($asset->default_image);
            }

            $asset->update([
                'default_image' => $photoPath,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Foto berhasil diupload',
            ], 200);

        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: '.$th->getMessage(),
            ], 500);
        }
    }

    public function update(Request $request, Asset $asset)
    {
        if (! hasPermissionInGuard('Data Aset - Action')) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'entry_date' => 'required|date|date_format:Y-m-d',
            'payment_date' => 'required|date|date_format:Y-m-d',
            'contract_number' => 'required|string|max:255',
            'job_description' => 'required|string|max:255',
            'payment_ba_number' => 'required|string|max:255',
            'acquisition_source' => 'required|string|in:apbn,apbd,hibah,kso/pinjam,blud,jkn,dak,sisoin,swasta',
            'distributor_id' => 'required|exists:distributors,id',
            'contract_type' => 'required|string|max:255',
            'description' => 'nullable|string',
            'aspak_distributor' => 'nullable|string|max:255',
            'aspak_maintenance_officer' => 'nullable|string|max:255',
            'siap_bmd_rkbmd_code' => 'nullable|string|max:255',
            'siap_bmd_activity' => 'nullable|string|max:255',
            'siap_bmd_sub_activity' => 'nullable|string|max:255',
            'siap_bmd_kusa_name' => 'nullable|string|max:255',
            'items' => 'required',
            'items.contract_item_name' => 'required|string|max:255',
            'items.brand' => 'required|string|max:255',
            'items.type_model' => 'required|string|max:255',
            'items.material' => 'required|string|max:255',
            'items.size' => 'required|string|max:255',
            'items.uom_id' => 'required|exists:uoms,id',
            'items.unit_price' => 'required|numeric|min:1000',
            'items.serial_number' => 'required|string|max:255',
            'items.rfid_tag' => 'nullable|string|max:255',
            'items.frame_number' => 'nullable|string|max:255',
            'items.engine_number' => 'nullable|string|max:255',
            'items.akd_akl_number' => 'required|string|max:255',
            'items.police_number' => 'nullable|string|max:255',
            'items.bpkb_number' => 'nullable|string|max:255',
            'items.condition' => 'required|string|in:BAIK,RUSAK_RINGAN,RUSAK_BERAT',
            'items.general_specification' => 'nullable|string|max:255',
            'items.asset_description' => 'nullable|string|max:255',
            'items.aspak_item_id' => 'nullable|integer',
            'items.aspak_service_room_id' => 'nullable|integer',
            'items.aspak_alat' => 'nullable|string|max:255',
            'items.aspak_product_origin' => 'nullable|string|max:255',
            'items.aspak_description' => 'nullable|string|max:255',
            'items.aspak_image_url' => 'nullable|string|max:255',
            'items.aspak_power_source' => 'nullable|string|max:255',
            'items.aspak_electricity' => 'nullable|string|max:255',
            'items.aspak_tersambung_ups' => 'nullable|boolean',
            'items.aspak_information_tkdn' => 'nullable|string|max:255',
            'items.aspak_risk_class' => 'nullable|string|max:255',
            'items.aspak_technical_lifetime_year' => 'nullable|string|max:255',
        ], [], [
            'asset_id' => 'ID Aset',
            'entry_date' => 'Tanggal Barang Masuk',
            'payment_date' => 'Tanggal Pembayaran',
            'contract_number' => 'No. BAST Kontrak',
            'job_description' => 'Uraian Pekerjaan',
            'payment_ba_number' => 'No. BAST Pembayaran',
            'acquisition_source' => 'Asal Perolehan',
            'distributor_id' => 'Distributor',
            'contract_type' => 'Jenis Kontrak',
            'description' => 'Keterangan',
            'aspak_distributor' => 'Distributor ASPAK',
            'aspak_maintenance_officer' => 'Petugas Maintenance ASPAK',
            'siap_bmd_rkbmd_code' => 'Kode RKBMD SIAP BMD',
            'siap_bmd_activity' => 'Aktivitas SIAP BMD',
            'siap_bmd_sub_activity' => 'Sub Aktivitas SIAP BMD',
            'siap_bmd_kusa_name' => 'Nama KUSA SIAP BMD',
            'items' => 'Daftar Barang',
            'items.contract_item_name' => 'Nama Barang Kontrak',
            'items.brand' => 'Merk',
            'items.type_model' => 'Tipe/Model',
            'items.material' => 'Bahan',
            'items.size' => 'Ukuran',
            'items.uom_id' => 'Satuan Barang',
            'items.unit_price' => 'Harga Satuan',
            'items.serial_number' => 'No. Seri',
            'items.rfid_tag' => 'Tag RFID',
            'items.frame_number' => 'No. Rangka',
            'items.engine_number' => 'No. Mesin',
            'items.akd_akl_number' => 'No. AKD/AKL',
            'items.police_number' => 'No. Polisi',
            'items.bpkb_number' => 'No. BPKB',
            'items.condition' => 'Kondisi',
            'items.general_specification' => 'Spesifikasi Umum',
            'items.asset_description' => 'Deskripsi Aset',
            'items.aspak_item_id' => 'Item ASPAK',
            'items.aspak_service_room_id' => 'Ruangan Service ASPAK',
            'items.aspak_alat' => 'Alat ASPAK',
            'items.aspak_product_origin' => 'Asal Produk ASPAK',
            'items.aspak_description' => 'Keterangan ASPAK',
            'items.aspak_image_url' => 'URL Gambar ASPAK',
            'items.aspak_power_source' => 'Sumber Daya ASPAK',
            'items.aspak_electricity' => 'Daya Listrik ASPAK',
            'items.aspak_tersambung_ups' => 'Tersambung UPS ASPAK',
            'items.aspak_information_tkdn' => 'Informasi TKDN ASPAK',
            'items.aspak_risk_class' => 'Kelas Risiko ASPAK',
            'items.aspak_technical_lifetime_year' => 'Umur Teknis (Tahun) ASPAK',
        ]);

        try {
            DB::beginTransaction();

            // Get the related AssetEntry from the bound Asset
            $assetEntry = $asset->assetEntry;
            if (! $assetEntry) {
                throw new \Exception('AssetEntry not found for the given Asset.');
            }

            $assetEntry->update([
                'received_date' => $validated['entry_date'],
                'payment_date' => $validated['payment_date'],
                'bast_contract_number' => $validated['contract_number'],
                'purchasing_account' => $validated['job_description'],
                'bast_payment_number' => $validated['payment_ba_number'],
                'source_supply' => $validated['acquisition_source'],
                'distributor_id' => $validated['distributor_id'],
                'contract_form' => $validated['contract_type'],
                'description' => $validated['description'],
                'aspak_distributor' => $validated['aspak_distributor'] ?? null,
                'aspak_maintenance_pic' => $validated['aspak_maintenance_officer'] ?? null,
                'siap_bmd_asset_need_plan_code' => $validated['siap_bmd_rkbmd_code'] ?? null,
                'siap_bmd_activity_id' => $validated['siap_bmd_activity'] ?? null,
                'siap_bmd_sub_activity_id' => $validated['siap_bmd_sub_activity'] ?? null,
                'siap_bmd_asset_user_name' => $validated['siap_bmd_kusa_name'] ?? null,
                'updated_by' => getAuthUserId(),
                'updated_by_name' => getAuthUserName(),
            ]);

            $uom = Uom::find($validated['items']['uom_id']);

            $asset->update([
                'asset_name' => $validated['items']['contract_item_name'],
                'brand' => $validated['items']['brand'],
                'type' => $validated['items']['type_model'],
                'material' => $validated['items']['material'],
                'size' => $validated['items']['size'],
                'uom_id' => $validated['items']['uom_id'],
                'uom_name' => $uom->uom_name,
                'unit_price' => $validated['items']['unit_price'],
                'serial_number' => $validated['items']['serial_number'],
                'rfid_tag' => $validated['items']['rfid_tag'] ?? null,
                'chassis_number' => $validated['items']['frame_number'] ?? null,
                'engine_number' => $validated['items']['engine_number'] ?? null,
                'license_plate_number' => $validated['items']['police_number'] ?? null,
                'bpkb_number' => $validated['items']['bpkb_number'] ?? null,
                'asset_condition' => $validated['items']['condition'],
                'general_specifications' => $validated['items']['general_specification'] ?? null,
                'description' => $validated['items']['asset_description'] ?? null,
                'akd_number' => $validated['items']['akd_akl_number'],
                'aspak_item_id' => $validated['items']['aspak_item_id'] ?? null,
                'aspak_service_room_id' => $validated['items']['aspak_service_room_id'] ?? null,
                'aspak_tool' => $validated['items']['aspak_alat'] ?? null,
                'product_origin' => $validated['items']['aspak_product_origin'] ?? null,
                'aspak_description' => $validated['items']['asset_description'] ?? null,
                'aspak_image_url' => $validated['items']['aspak_image_url'] ?? null,
                'power_source' => $validated['items']['aspak_power_source'] ?? null,
                'electricity' => $validated['items']['aspak_electricity'] ?? null,
                'connected_ups' => $validated['items']['aspak_tersambung_ups'] ?? null,
                'information_tkdn' => $validated['items']['aspak_information_tkdn'] ?? null,
                'risk_class' => $validated['items']['aspak_risk_class'] ?? null,
                'technical_lifetime_year' => $validated['items']['aspak_technical_lifetime_year'] ?? null,
                'updated_by' => getAuthUserId(),
                'updated_by_name' => getAuthUserName(),
            ]);

            // Recalculate totals for AssetEntry based on all associated assets
            $assetEntry->update([
                'quantity' => $assetEntry->assets()->count(),
                'total_price' => $assetEntry->assets()->sum('unit_price'),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil diperbarui',
                'redirect' => route('asset-management.asset-hospital.index'),
            ], 200);
        } catch (\Throwable $th) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $th->getMessage(),
            ], 500);
        }
    }
}
