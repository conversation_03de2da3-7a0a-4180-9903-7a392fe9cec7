<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;

class AspakIntegrationController extends Controller
{
    public function index()
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'ASPAK Integration';
        $breadcrumbs = ['Manajemen Aset', 'ASPAK Integration'];

        return view('asset-management.aspak-integration.index', compact('title', 'breadcrumbs'));
    }
}