<?php

namespace App\Http\Controllers\AssetManagement;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class AspakIntegrationController extends Controller
{
    public function index()
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        $title = 'ASPAK Integration';
        $breadcrumbs = ['Manajemen Aset', 'ASPAK Integration'];

        return view('asset-management.aspak-integration.index', compact('title', 'breadcrumbs'));
    }

    public function list(Request $request)
    {
        if (! hasPermissionInGuard('Data Aset - View')) {
            abort(403, 'Unauthorized action.');
        }

        if ($request->ajax()) {
            $data = DB::table('assets')
                ->leftJoin('asset_entries', 'assets.asset_entry_id', '=', 'asset_entries.id')
                ->leftJoin('items', 'assets.item_id', '=', 'items.id')
                ->leftJoin('rooms', 'assets.document_room_id', '=', 'rooms.id')
                ->leftJoin('aspak_items', 'assets.aspak_item_id', '=', 'aspak_items.id')
                ->leftJoin('aspak_service_rooms', 'assets.aspak_service_room_id', '=', 'aspak_service_rooms.id')
                ->select(
                    'assets.*',
                    'asset_entries.payment_date',
                    'asset_entries.received_date',
                    'asset_entries.source_supply',
                    'asset_entries.aspak_distributor',
                    'asset_entries.aspak_maintenance_pic',
                    'items.item_name',
                    'items.item_code',
                    'rooms.room_name',
                    'aspak_items.item_name as aspak_item_name',
                    'aspak_items.item_code as aspak_item_code',
                    'aspak_service_rooms.room_service_name as aspak_room_name',
                    'aspak_service_rooms.room_service_code as aspak_room_code'
                )
                ->where('assets.category_type', 'EQUIPMENT')
                ->whereNotNull('assets.aspak_item_id'); // Only show assets with ASPAK integration

            return DataTables::of($data)
                ->addIndexColumn()
                ->editColumn('qr_code', function ($row) {
                    return '<div class="text-center align-middle">'.QrCode::size(50)->generate($row->qr_code).'</div>';
                })
                ->addColumn('real_qr_code', function ($row) {
                    return $row->qr_code;
                })
                ->addColumn('action', function ($row) {
                    return '<a href="'.route('asset-management.asset-hospital.edit', $row->id).'" class="btn btn-sm btn-outline-warning btn-edit"><i class="fas fa-edit"></i></a>';
                })
                ->addColumn('asset_year', function ($row) {
                    return Carbon::parse($row->received_date)->format('Y');
                })
                ->addColumn('aspak_info', function ($row) {
                    $aspakItem = $row->aspak_item_name ? $row->aspak_item_code . ' - ' . $row->aspak_item_name : '-';
                    $aspakRoom = $row->aspak_room_name ? $row->aspak_room_code . ' - ' . $row->aspak_room_name : '-';
                    return $aspakItem . '<br><small class="text-muted">' . $aspakRoom . '</small>';
                })
                ->addColumn('aspak_details', function ($row) {
                    $tool = $row->aspak_tool ? 'Tool: ' . $row->aspak_tool : '';
                    $description = $row->aspak_description ? 'Desc: ' . $row->aspak_description : '';
                    $details = array_filter([$tool, $description]);
                    return implode('<br>', $details) ?: '-';
                })
                ->addColumn('aspak_maintenance', function ($row) {
                    $distributor = $row->aspak_distributor ? 'Distributor: ' . $row->aspak_distributor : '';
                    $pic = $row->aspak_maintenance_pic ? 'PIC: ' . $row->aspak_maintenance_pic : '';
                    $maintenance = array_filter([$distributor, $pic]);
                    return implode('<br>', $maintenance) ?: '-';
                })
                ->filterColumn('item_name', function ($query, $keyword) {
                    $query->where('items.item_name', 'like', "%{$keyword}%");
                })
                ->filterColumn('aspak_item_name', function ($query, $keyword) {
                    $query->where('aspak_items.item_name', 'like', "%{$keyword}%");
                })
                ->filterColumn('aspak_room_name', function ($query, $keyword) {
                    $query->where('aspak_service_rooms.room_service_name', 'like', "%{$keyword}%");
                })
                ->rawColumns(['action', 'qr_code', 'aspak_info', 'aspak_details', 'aspak_maintenance'])
                ->make(true);
        }
    }
}
