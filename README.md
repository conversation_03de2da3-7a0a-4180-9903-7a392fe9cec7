# SENSI - Sistem Manajemen Aset Rumah Sakit

<p align="center">
    <img src="https://laravel.com/img/logomark.min.svg" width="100" alt="Laravel Logo">
</p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## Tentang SENSI

SENSI (Sistem Manajemen Aset Rumah Sakit) adalah aplikasi web berbasis Laravel yang dirancang khusus untuk mengelola aset dan inventaris rumah sakit secara komprehensif. Sistem ini menyediakan solusi terintegrasi untuk manajemen aset medis dan non-medis, pemeliharaan, logistik, dan perencanaan.

## Fitur Utama

### 🏥 Asset Management (Manajemen Aset)
- Pendaftaran dan pencatatan aset rumah sakit
- Kategorisasi aset medis dan non-medis
- Tracking lokasi dan status aset
- Mutasi dan perpindahan aset
- Laporan inventaris dan KIR (Kartu Inventaris Ruangan)
- Penghapusan dan disposal aset
- Riwayat aset lengkap

### 🔧 Asset Maintenance (Pemeliharaan Aset)
- Penjadwalan pemeliharaan preventif
- Manajemen pemeliharaan medis dan non-medis
- Permintaan pemeliharaan insidental
- Tracking aktivitas pemeliharaan
- Laporan pemeliharaan komprehensif

### 📦 Logistic Management (Manajemen Logistik)
- Manajemen stok barang habis pakai
- Permintaan dan pemenuhan logistik
- Penerimaan dan pengeluaran barang
- Stock opname dan rekap stok
- Adjustment stok
- Laporan logistik lengkap

### 📋 Planning (Perencanaan)
- Perencanaan pemeliharaan aset
- Perencanaan pengadaan consumable
- Approval workflow
- Laporan perencanaan

### 💰 Depreciation (Depresiasi)
- Perhitungan depresiasi aset
- Setting parameter depresiasi
- Laporan nilai aset

### 🗂️ Master Data Management
- Manajemen kategori aset
- Data ruangan dan divisi
- Data karyawan dan petugas
- Data distributor dan supplier
- Unit of Measure (UOM)
- Program dan konfigurasi sistem

## Requirements

- **PHP**: ^8.4
- **Laravel**: ^12.0
- **Database**: MySQL
- **Web Server**: Apache/Nginx
- **Composer**: Latest version

## Dependencies Utama

- **Laravel Framework**: ^12.0
- **Laravel Sanctum**: ^4.0 (API Authentication)
- **Spatie Laravel Permission**: ^6.10 (Role & Permission Management)
- **Yajra DataTables**: ^12.0 (Advanced Tables)
- **Maatwebsite Excel**: ^3.1 (Excel Import/Export)
- **DomPDF**: ^3.0 (PDF Generation)
- **PHPWord**: ^1.3 (Word Document Generation)
- **Simple QR Code**: ^4.2 (QR Code Generation)

## Instalasi

### 1. Clone Repository
```bash
git clone <repository-url>
cd sensi
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Environment Setup
```bash
cp .env.example .env
php artisan key:generate
```

### 4. Konfigurasi Database
Edit file `.env` dan sesuaikan konfigurasi database:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sensi
DB_USERNAME=taufan_budi2
DB_PASSWORD=satudua
```

### 5. Konfigurasi Rumah Sakit
Tambahkan konfigurasi rumah sakit di file `.env`:

```env
HOSPITAL_NAME="Nama Rumah Sakit"
HOSPITAL_PROVINCE="Provinsi"
HOSPITAL_CITY="Kota"
HOSPITAL_ADDRESS="Alamat Lengkap"
REPORT_HEADER_AREA="Area/Wilayah"
REPORT_HEADER_HOSPITAL="Nama RS untuk Header"
REPORT_HEADER_ADDRESS="Alamat untuk Header"
REPORT_HEADER_PHONE="Nomor Telepon"
REPORT_HEADER_EMAIL="<EMAIL>"
```

### 6. Database Migration & Seeding
```bash
php artisan migrate
php artisan db:seed
```

### 7. Storage Link
```bash
php artisan storage:link
```

## Menjalankan Aplikasi

### Development Server
```bash
php artisan serve
```

Aplikasi akan berjalan di `http://localhost:8000`

### Login Development
- **Email**: <EMAIL>
- **Password**: password

## Struktur Proyek

```
sensi/
├── app/
│   ├── Http/Controllers/
│   │   ├── AssetManagement/     # Controller manajemen aset
│   │   ├── AssetMaintenance/    # Controller pemeliharaan
│   │   ├── Logistic/           # Controller logistik
│   │   ├── Planning/           # Controller perencanaan
│   │   ├── Depreciation/       # Controller depresiasi
│   │   └── MasterData/         # Controller master data
│   ├── Models/                 # Eloquent models
│   ├── DataTables/            # DataTables classes
│   ├── Exports/               # Excel export classes
│   └── Imports/               # Excel import classes
├── resources/
│   └── views/
│       ├── asset-management/   # Views manajemen aset
│       ├── asset-maintenance/  # Views pemeliharaan
│       ├── logistic/          # Views logistik
│       ├── planning/          # Views perencanaan
│       ├── depreciation/      # Views depresiasi
│       └── master-data/       # Views master data
└── public/
    ├── css/                   # Stylesheets
    ├── js/                    # JavaScript files
    └── plugins/               # Third-party plugins
```

## Fitur Keamanan

- **Role-based Access Control**: Menggunakan Spatie Laravel Permission
- **Authentication**: Laravel Sanctum untuk API
- **Authorization**: Policy-based authorization
- **CSRF Protection**: Built-in Laravel CSRF protection

## Laporan dan Export

- **PDF Reports**: Menggunakan DomPDF
- **Excel Export/Import**: Menggunakan Maatwebsite Excel
- **Word Documents**: Menggunakan PHPWord
- **QR Code**: Untuk labeling aset

## Contributing

Untuk berkontribusi pada proyek ini:

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit perubahan (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## License

Proyek ini menggunakan lisensi [MIT License](https://opensource.org/licenses/MIT).

## Support

Untuk dukungan teknis atau pertanyaan, silakan hubungi tim development.

---

**SENSI** - Solusi Terpadu Manajemen Aset Rumah Sakit
