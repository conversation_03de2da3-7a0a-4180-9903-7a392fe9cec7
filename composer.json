{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.4", "barryvdh/laravel-dompdf": "^3.0", "fakerphp/faker": "^1.24", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "phpoffice/phpword": "^1.3", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-permission": "^6.10", "yajra/laravel-datatables": "^12.0", "yajra/laravel-datatables-oracle": "^12.0"}, "require-dev": {"laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/AuthHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"branch-alias": {"dev-master": "12.x-dev"}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}